"""
Test Configuration for CVE Feed Service
Configures tests to use Traefik-managed serviceURLmanager endpoints
"""

import os
from typing import Dict, Any

# =============================================================================
# TRAEFIK SERVICE URL MANAGER CONFIGURATION
# =============================================================================

# Base domains for Traefik-managed services (no ports)
# For testing, we'll use localhost with the running service
SERVICE_DOMAINS = {
    "api": "http://localhost:8001",  # Running CVE service
    "frontend": "https://app.feedme.localhost",
    "admin": "https://admin.feedme.localhost",
    "docs": "https://docs.feedme.localhost",
    "metrics": "https://metrics.feedme.localhost",
    "dashboard": "https://dashboard.feedme.localhost"
}

# Development-only domains (when running in development mode)
DEV_SERVICE_DOMAINS = {
    "pgadmin": "https://pgadmin.feedme.localhost",
    "redis": "https://redis.feedme.localhost"
}

# =============================================================================
# TEST ENVIRONMENT CONFIGURATION
# =============================================================================

class TestConfig:
    """Test configuration using Traefik serviceURLmanager endpoints"""
    
    # API Configuration
    API_BASE_URL = SERVICE_DOMAINS["api"]
    API_VERSION = "v1"
    API_URL = f"{API_BASE_URL}/api/{API_VERSION}"
    
    # Frontend Configuration
    FRONTEND_URL = SERVICE_DOMAINS["frontend"]
    ADMIN_URL = SERVICE_DOMAINS["admin"]
    DOCS_URL = SERVICE_DOMAINS["docs"]
    
    # Monitoring Configuration
    METRICS_URL = SERVICE_DOMAINS["metrics"]
    DASHBOARD_URL = SERVICE_DOMAINS["dashboard"]
    
    # Test User Credentials
    TEST_USERS = {
        "admin": {
            "username": "<EMAIL>",
            "password": "admin123",
            "role": "admin"
        },
        "analyst": {
            "username": "<EMAIL>", 
            "password": "analyst123",
            "role": "security_analyst"
        },
        "developer": {
            "username": "<EMAIL>",
            "password": "dev123", 
            "role": "developer"
        }
    }
    
    # Test Timeouts
    REQUEST_TIMEOUT = 30
    HEALTH_CHECK_TIMEOUT = 10
    SERVICE_STARTUP_TIMEOUT = 120
    
    # Test Data
    SAMPLE_CVE_DATA = {
        "cve_id": "CVE-2024-TEST-001",
        "description": "Test CVE for integration testing",
        "severity": "HIGH",
        "cvss_score": 8.5,
        "published_date": "2024-01-20T10:00:00Z",
        "status": "published"
    }
    
    SAMPLE_APPLICATION_DATA = {
        "name": "Test Application",
        "description": "Test application for integration testing",
        "version": "1.0.0",
        "environment": "development",
        "repository_url": "https://github.com/test/app"
    }

# =============================================================================
# PLAYWRIGHT CONFIGURATION
# =============================================================================

class PlaywrightConfig:
    """Playwright configuration for E2E testing"""
    
    # Browser Configuration
    HEADLESS = True
    BROWSER_TIMEOUT = 30000  # 30 seconds
    
    # Test Configuration
    BASE_URL = SERVICE_DOMAINS["frontend"]
    API_URL = SERVICE_DOMAINS["api"]
    
    # Viewport Configuration
    VIEWPORT = {"width": 1280, "height": 720}
    
    # Screenshot Configuration
    SCREENSHOT_ON_FAILURE = True
    SCREENSHOT_PATH = "test-results/screenshots"
    
    # Video Configuration
    VIDEO_ON_FAILURE = True
    VIDEO_PATH = "test-results/videos"

# =============================================================================
# BEHAVE CONFIGURATION
# =============================================================================

class BehaveConfig:
    """Behave BDD configuration"""
    
    # Service URLs
    API_URL = SERVICE_DOMAINS["api"]
    FRONTEND_URL = SERVICE_DOMAINS["frontend"]
    ADMIN_URL = SERVICE_DOMAINS["admin"]
    
    # Test Data Paths
    FEATURES_PATH = "frontend/src/__tests__/behave"
    STEP_DEFINITIONS_PATH = "frontend/src/__tests__/behave/steps"
    
    # Output Configuration
    OUTPUT_FORMAT = "pretty"
    OUTPUT_PATH = "test-results/behave"

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================

HEALTH_CHECK_ENDPOINTS = {
    "api": f"{SERVICE_DOMAINS['api']}/health",
    # For testing, we'll only check the API service that's actually running
    # "frontend": f"{SERVICE_DOMAINS['frontend']}/health",
    # "admin": f"{SERVICE_DOMAINS['admin']}/health",
    # "docs": f"{SERVICE_DOMAINS['docs']}/health",
    # "metrics": f"{SERVICE_DOMAINS['metrics']}/-/healthy",
    # "dashboard": f"{SERVICE_DOMAINS['dashboard']}/api/health"
}

# =============================================================================
# TEST UTILITIES
# =============================================================================

def get_service_url(service_name: str) -> str:
    """Get the URL for a service using Traefik serviceURLmanager"""
    if service_name in SERVICE_DOMAINS:
        return SERVICE_DOMAINS[service_name]
    elif service_name in DEV_SERVICE_DOMAINS:
        return DEV_SERVICE_DOMAINS[service_name]
    else:
        raise ValueError(f"Unknown service: {service_name}")

def get_api_endpoint(endpoint: str) -> str:
    """Get full API endpoint URL"""
    return f"{TestConfig.API_URL}/{endpoint.lstrip('/')}"

def get_health_check_url(service_name: str) -> str:
    """Get health check URL for a service"""
    if service_name in HEALTH_CHECK_ENDPOINTS:
        return HEALTH_CHECK_ENDPOINTS[service_name]
    else:
        return f"{get_service_url(service_name)}/health"

# =============================================================================
# ENVIRONMENT DETECTION
# =============================================================================

def is_docker_environment() -> bool:
    """Check if running in Docker environment"""
    return os.path.exists("/.dockerenv") or os.environ.get("DOCKER_ENV") == "true"

def is_development_mode() -> bool:
    """Check if running in development mode"""
    return os.environ.get("ENVIRONMENT", "development") == "development"

def get_test_database_url() -> str:
    """Get test database URL"""
    if is_docker_environment():
        return "postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db_test"
    else:
        return "sqlite+aiosqlite:///./test_database.db"

# =============================================================================
# TEST MARKERS AND CATEGORIES
# =============================================================================

TEST_CATEGORIES = {
    "unit": "Unit tests - isolated component testing",
    "integration": "Integration tests - cross-component testing", 
    "api": "API tests - endpoint testing",
    "e2e": "End-to-end tests - full workflow testing",
    "performance": "Performance tests - load and stress testing",
    "security": "Security tests - vulnerability and penetration testing",
    "accessibility": "Accessibility tests - WCAG compliance testing",
    "mobile": "Mobile tests - responsive design testing"
}

# =============================================================================
# MOCK DATA CONFIGURATION
# =============================================================================

MOCK_DATA_CONFIG = {
    "cves": {
        "count": 50,
        "severities": ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
        "statuses": ["published", "modified", "rejected"]
    },
    "applications": {
        "count": 20,
        "environments": ["development", "staging", "production"],
        "technologies": ["React", "Node.js", "Python", "Java", "Go"]
    },
    "users": {
        "count": 10,
        "roles": ["admin", "security_analyst", "developer", "viewer"]
    }
}

# =============================================================================
# EXPORT CONFIGURATION
# =============================================================================

# Make key configurations available at module level
API_URL = TestConfig.API_URL
FRONTEND_URL = TestConfig.FRONTEND_URL
ADMIN_URL = TestConfig.ADMIN_URL
TEST_USERS = TestConfig.TEST_USERS

__all__ = [
    "TestConfig",
    "PlaywrightConfig", 
    "BehaveConfig",
    "SERVICE_DOMAINS",
    "HEALTH_CHECK_ENDPOINTS",
    "get_service_url",
    "get_api_endpoint",
    "get_health_check_url",
    "is_docker_environment",
    "is_development_mode",
    "get_test_database_url",
    "API_URL",
    "FRONTEND_URL", 
    "ADMIN_URL",
    "TEST_USERS"
]
