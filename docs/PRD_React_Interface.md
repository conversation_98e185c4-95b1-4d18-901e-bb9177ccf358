# Product Requirements Document: React Interface for CVE Feed Service

## Executive Summary

This PRD outlines the development of a modern React-based web interface for the CVE Feed Service, prioritizing dark mode design and comprehensive user experience flows. The interface will provide enterprise-grade vulnerability management capabilities with intuitive navigation and robust testing coverage.

## Project Overview

### Objectives
- Create a responsive, dark-mode-first React interface
- Provide comprehensive vulnerability management workflows
- Implement enterprise-grade authentication and authorization
- Ensure accessibility and performance standards
- Deliver comprehensive test coverage (Playwright + Flow tests)

### Target Users
- Security Engineers
- DevOps Teams
- Application Owners
- Security Managers
- Compliance Officers

### Technical Stack
- **Frontend**: React 18+ with TypeScript
- **Styling**: Tailwind CSS with dark mode support
- **State Management**: Redux Toolkit + RTK Query
- **Routing**: React Router v6
- **Testing**: Playwright (E2E) + Jest (Unit) + React Testing Library
- **Build Tool**: Vite
- **Authentication**: JWT with refresh tokens

## Core Features & User Flows

### 1. Authentication Flow

#### 1.1 Login Flow
**Description**: Secure user authentication with JWT tokens

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Login Form | Email/password input with validation | `test-login-form-validation` | `login-success-flow` |
| Error Messages | Display authentication errors | `test-login-error-display` | `login-failure-flow` |
| Loading States | Show loading during authentication | `test-login-loading-state` | `login-loading-flow` |
| Remember Me | Persistent session option | `test-remember-me-toggle` | `remember-me-flow` |
| Forgot Password | Password reset link | `test-forgot-password-link` | `password-reset-flow` |

#### 1.2 Registration Flow
**Description**: New user account creation

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Registration Form | User details input with validation | `test-registration-form` | `registration-success-flow` |
| Password Strength | Real-time password validation | `test-password-strength` | `password-validation-flow` |
| Terms Acceptance | Legal agreement checkbox | `test-terms-acceptance` | `terms-agreement-flow` |
| Email Verification | Account activation process | `test-email-verification` | `email-verification-flow` |

### 2. Dashboard Flow

#### 2.1 Main Dashboard
**Description**: Central hub showing vulnerability overview and key metrics

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Metrics Cards | CVE counts by severity | `test-metrics-display` | `dashboard-metrics-flow` |
| Recent CVEs | Latest vulnerability entries | `test-recent-cves-list` | `recent-cves-flow` |
| Application Status | Health indicators per app | `test-app-status-grid` | `app-status-flow` |
| Quick Actions | Shortcut buttons for common tasks | `test-quick-actions` | `quick-actions-flow` |
| Search Bar | Global vulnerability search | `test-global-search` | `global-search-flow` |

#### 2.2 Filtering & Search
**Description**: Advanced filtering capabilities for vulnerability data

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Filter Sidebar | Severity, date, application filters | `test-filter-sidebar` | `filtering-flow` |
| Search Results | Paginated vulnerability list | `test-search-results` | `search-results-flow` |
| Sort Options | Multiple sorting criteria | `test-sort-options` | `sorting-flow` |
| Saved Searches | Bookmark frequent queries | `test-saved-searches` | `saved-searches-flow` |

### 3. Application Management Flow

#### 3.1 Application List
**Description**: Comprehensive application inventory management

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Application Grid | Responsive app cards/table | `test-application-grid` | `app-list-flow` |
| Add Application | New application creation modal | `test-add-application` | `app-creation-flow` |
| Bulk Actions | Multi-select operations | `test-bulk-actions` | `bulk-operations-flow` |
| Status Indicators | Visual health/risk indicators | `test-status-indicators` | `status-display-flow` |

#### 3.2 Application Details
**Description**: Detailed view of individual applications

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| App Overview | Basic information and metrics | `test-app-overview` | `app-details-flow` |
| Component List | Associated software components | `test-component-list` | `component-management-flow` |
| CVE Timeline | Vulnerability history | `test-cve-timeline` | `cve-timeline-flow` |
| Risk Assessment | Security posture analysis | `test-risk-assessment` | `risk-analysis-flow` |

### 4. CVE Management Flow

#### 4.1 CVE Feed
**Description**: Tailored vulnerability feed based on application components

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| CVE List | Paginated vulnerability entries | `test-cve-list` | `cve-feed-flow` |
| Severity Badges | Visual severity indicators | `test-severity-badges` | `severity-display-flow` |
| Filter Controls | Real-time filtering options | `test-cve-filters` | `cve-filtering-flow` |
| Export Options | Data export functionality | `test-export-options` | `data-export-flow` |

#### 4.2 CVE Details
**Description**: Comprehensive vulnerability information

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| CVE Header | ID, title, and key metrics | `test-cve-header` | `cve-details-flow` |
| Description | Detailed vulnerability info | `test-cve-description` | `cve-info-flow` |
| CVSS Metrics | Scoring breakdown | `test-cvss-metrics` | `cvss-display-flow` |
| Affected Components | Impact analysis | `test-affected-components` | `impact-analysis-flow` |
| Remediation | Fix recommendations | `test-remediation-info` | `remediation-flow` |

### 5. Component Management Flow

#### 5.1 Component Inventory
**Description**: Software component tracking and management

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| Component Table | Searchable component list | `test-component-table` | `component-list-flow` |
| Add Component | New component registration | `test-add-component` | `component-creation-flow` |
| CPE Mapping | Component to CPE associations | `test-cpe-mapping` | `cpe-mapping-flow` |
| Version Tracking | Component version management | `test-version-tracking` | `version-management-flow` |

### 6. User Management Flow (Admin)

#### 6.1 User Administration
**Description**: User account and permission management

| UI Component | Description | Playwright Test | Flow Test |
|--------------|-------------|-----------------|-----------|
| User List | Account management table | `test-user-list` | `user-management-flow` |
| Role Assignment | Permission level controls | `test-role-assignment` | `role-management-flow` |
| API Key Management | Token generation/revocation | `test-api-keys` | `api-key-flow` |
| Audit Log | User activity tracking | `test-audit-log` | `audit-trail-flow` |

## Design System Specifications

### Dark Mode Theme
- **Primary Colors**: Deep blues (#1e293b, #334155)
- **Accent Colors**: Cyan (#06b6d4) and Orange (#f97316)
- **Severity Colors**: 
  - Critical: #dc2626 (Red)
  - High: #ea580c (Orange)
  - Medium: #ca8a04 (Yellow)
  - Low: #16a34a (Green)
- **Background**: #0f172a (Slate 900)
- **Surface**: #1e293b (Slate 800)
- **Text**: #f8fafc (Slate 50)

### Typography
- **Headings**: Inter font family
- **Body**: System font stack
- **Code**: JetBrains Mono

### Component Library
- Custom components built with Tailwind CSS
- Consistent spacing using 8px grid system
- Responsive breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)

## Technical Requirements

### Performance
- Initial page load < 3 seconds
- Route transitions < 500ms
- API response handling with loading states
- Infinite scroll for large datasets
- Image optimization and lazy loading

### Accessibility
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Testing Strategy

### Test Coverage Requirements
- **Unit Tests**: 90%+ coverage using Jest + React Testing Library
- **Integration Tests**: API integration with MSW (Mock Service Worker)
- **E2E Tests**: Critical user journeys with Playwright
- **Visual Regression**: Chromatic for component testing
- **Performance Tests**: Lighthouse CI integration

### Test Environment Setup
- **Development**: Local test database with seed data
- **Staging**: Production-like environment for E2E tests
- **CI/CD**: Automated test execution on pull requests

## Security Considerations

### Authentication & Authorization
- JWT token management with refresh mechanism
- Role-based access control (RBAC)
- Session timeout handling
- Secure token storage (httpOnly cookies)

### Data Protection
- Input sanitization and validation
- XSS protection
- CSRF protection
- Content Security Policy (CSP)

## Deployment & DevOps

### Build Process
- Vite for fast development and optimized production builds
- Environment-specific configuration
- Asset optimization and bundling
- Source map generation for debugging

### Monitoring
- Error tracking with Sentry
- Performance monitoring
- User analytics (privacy-compliant)
- API usage metrics

## Success Metrics

### User Experience
- Page load time < 3 seconds
- User task completion rate > 95%
- User satisfaction score > 4.5/5

### Technical
- Test coverage > 90%
- Zero critical accessibility violations
- Performance budget compliance
- Security audit compliance

## Timeline & Milestones

### Phase 1: Foundation (Weeks 1-2)
- Project setup and tooling
- Authentication flow implementation
- Basic dashboard layout

### Phase 2: Core Features (Weeks 3-6)
- CVE feed and details
- Application management
- Component inventory

### Phase 3: Advanced Features (Weeks 7-8)
- User management
- Advanced filtering
- Export functionality

### Phase 4: Testing & Polish (Weeks 9-10)
- Comprehensive test suite
- Performance optimization
- Accessibility audit

## Detailed Testing Specifications

### Playwright Test Structure

#### Authentication Tests
```typescript
// tests/e2e/auth.spec.ts
describe('Authentication Flow', () => {
  test('login-success-flow', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password123');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('login-failure-flow', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'wrongpassword');
    await page.click('[data-testid=login-button]');
    await expect(page.locator('[data-testid=error-message]')).toBeVisible();
  });
});
```

#### CVE Management Tests
```typescript
// tests/e2e/cve-management.spec.ts
describe('CVE Management Flow', () => {
  test('cve-feed-flow', async ({ page }) => {
    await page.goto('/cves');
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    await page.selectOption('[data-testid=severity-filter]', 'HIGH');
    await expect(page.locator('[data-testid=cve-item]')).toHaveCount(5);
  });

  test('cve-details-flow', async ({ page }) => {
    await page.goto('/cves');
    await page.click('[data-testid=cve-item]:first-child');
    await expect(page.locator('[data-testid=cve-header]')).toBeVisible();
    await expect(page.locator('[data-testid=cvss-metrics]')).toBeVisible();
  });
});
```

### Flow Test Specifications

#### Dashboard Flow Tests
```typescript
// tests/flows/dashboard.flow.ts
export const dashboardFlows = {
  'dashboard-metrics-flow': {
    steps: [
      { action: 'navigate', target: '/dashboard' },
      { action: 'waitFor', target: '[data-testid=metrics-cards]' },
      { action: 'verify', target: 'metrics-display', expected: 'visible' },
      { action: 'verify', target: 'severity-breakdown', expected: 'populated' }
    ],
    assertions: [
      'Critical CVE count is displayed',
      'High severity metrics are visible',
      'Application health indicators show'
    ]
  },

  'global-search-flow': {
    steps: [
      { action: 'navigate', target: '/dashboard' },
      { action: 'type', target: '[data-testid=global-search]', value: 'CVE-2023' },
      { action: 'press', key: 'Enter' },
      { action: 'waitFor', target: '[data-testid=search-results]' }
    ],
    assertions: [
      'Search results are displayed',
      'Results contain CVE-2023 entries',
      'Pagination controls are visible'
    ]
  }
};
```

#### Application Management Flow Tests
```typescript
// tests/flows/applications.flow.ts
export const applicationFlows = {
  'app-creation-flow': {
    steps: [
      { action: 'navigate', target: '/applications' },
      { action: 'click', target: '[data-testid=add-application-btn]' },
      { action: 'fill', target: '[data-testid=app-name-input]', value: 'Test App' },
      { action: 'select', target: '[data-testid=environment-select]', value: 'production' },
      { action: 'select', target: '[data-testid=criticality-select]', value: 'high' },
      { action: 'click', target: '[data-testid=save-application-btn]' },
      { action: 'waitFor', target: '[data-testid=success-notification]' }
    ],
    assertions: [
      'Application is created successfully',
      'New application appears in list',
      'Success notification is shown'
    ]
  },

  'component-management-flow': {
    steps: [
      { action: 'navigate', target: '/applications/[app-id]' },
      { action: 'click', target: '[data-testid=add-component-btn]' },
      { action: 'fill', target: '[data-testid=component-name]', value: 'nginx' },
      { action: 'fill', target: '[data-testid=component-version]', value: '1.20.1' },
      { action: 'click', target: '[data-testid=save-component-btn]' }
    ],
    assertions: [
      'Component is added to application',
      'CPE mapping is suggested',
      'Component appears in inventory'
    ]
  }
};
```

### Component Test Structure

#### UI Component Tests
```typescript
// src/components/__tests__/CVECard.test.tsx
describe('CVECard Component', () => {
  const mockCVE = {
    id: 'CVE-2023-1234',
    title: 'Test Vulnerability',
    severity: 'HIGH',
    cvssScore: 8.5,
    publishedDate: '2023-01-01'
  };

  test('renders CVE information correctly', () => {
    render(<CVECard cve={mockCVE} />);
    expect(screen.getByText('CVE-2023-1234')).toBeInTheDocument();
    expect(screen.getByText('HIGH')).toBeInTheDocument();
    expect(screen.getByText('8.5')).toBeInTheDocument();
  });

  test('applies correct severity styling', () => {
    render(<CVECard cve={mockCVE} />);
    const severityBadge = screen.getByText('HIGH');
    expect(severityBadge).toHaveClass('bg-orange-600');
  });
});
```

## Implementation Roadmap

### Week 1-2: Project Foundation
- **Setup & Configuration**
  - Initialize React + TypeScript + Vite project
  - Configure Tailwind CSS with dark mode
  - Setup Redux Toolkit + RTK Query
  - Configure testing environment (Jest, Playwright, RTL)
  - Setup CI/CD pipeline with GitHub Actions

- **Authentication Implementation**
  - JWT token management
  - Login/logout functionality
  - Protected route components
  - Token refresh mechanism

### Week 3-4: Core Dashboard
- **Dashboard Components**
  - Metrics cards with real-time data
  - Recent CVEs list
  - Application status grid
  - Global search functionality

- **Navigation & Layout**
  - Responsive sidebar navigation
  - Header with user menu
  - Breadcrumb navigation
  - Mobile-responsive design

### Week 5-6: CVE Management
- **CVE Feed Interface**
  - Paginated CVE list
  - Advanced filtering sidebar
  - Severity-based styling
  - Export functionality

- **CVE Details Page**
  - Comprehensive vulnerability information
  - CVSS metrics visualization
  - Affected components analysis
  - Remediation recommendations

### Week 7-8: Application & Component Management
- **Application Management**
  - Application inventory grid
  - Create/edit application forms
  - Application details with components
  - Risk assessment dashboard

- **Component Management**
  - Component inventory table
  - CPE mapping interface
  - Version tracking
  - Bulk operations

### Week 9-10: Testing & Polish
- **Comprehensive Testing**
  - Complete Playwright test suite
  - Flow test implementation
  - Performance optimization
  - Accessibility audit and fixes

- **Production Readiness**
  - Error boundary implementation
  - Loading states optimization
  - SEO optimization
  - Security hardening

## Appendices

### API Integration Points
- Authentication: `/api/v1/auth/login`, `/api/v1/auth/refresh`
- Applications: `/api/v1/applications`, `/api/v1/applications/{id}`
- Components: `/api/v1/components`, `/api/v1/components/{id}/cpe-mappings`
- CVEs: `/api/v1/cves/feed`, `/api/v1/cves/{id}`
- Users: `/api/v1/auth/users`, `/api/v1/auth/api-keys`

### State Management Structure
```typescript
interface RootState {
  auth: {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
    loading: boolean;
  };
  applications: {
    items: Application[];
    selectedApp: Application | null;
    loading: boolean;
    filters: ApplicationFilters;
  };
  cves: {
    feed: CVE[];
    selectedCVE: CVE | null;
    loading: boolean;
    filters: CVEFilters;
    pagination: PaginationState;
  };
  components: {
    items: Component[];
    loading: boolean;
    cpeMappings: CPEMapping[];
  };
  ui: {
    theme: 'dark' | 'light';
    sidebarOpen: boolean;
    notifications: Notification[];
  };
}
```

### Environment Configuration
```typescript
// src/config/environment.ts
export const config = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  enableDevTools: import.meta.env.DEV,
  sentryDsn: import.meta.env.VITE_SENTRY_DSN,
  version: import.meta.env.VITE_APP_VERSION || '1.0.0'
};
```

## Detailed Component Specifications

### Core Component Library

#### 1. Layout Components

**Header Component**
```typescript
interface HeaderProps {
  user: User | null;
  onLogout: () => void;
  onToggleTheme: () => void;
  notifications: Notification[];
}

// Features:
// - User avatar with dropdown menu
// - Global search bar with autocomplete
// - Notification bell with badge count
// - Theme toggle (dark/light)
// - Breadcrumb navigation
```

**Sidebar Component**
```typescript
interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  currentPath: string;
  userRole: UserRole;
}

// Features:
// - Collapsible navigation menu
// - Role-based menu items
// - Active state indicators
// - Keyboard navigation support
// - Mobile-responsive behavior
```

**DataTable Component**
```typescript
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading: boolean;
  pagination: PaginationState;
  sorting: SortingState;
  filtering: FilteringState;
  onPaginationChange: (pagination: PaginationState) => void;
  onSortingChange: (sorting: SortingState) => void;
  onFilteringChange: (filtering: FilteringState) => void;
}

// Features:
// - Server-side pagination
// - Multi-column sorting
// - Advanced filtering
// - Row selection
// - Export functionality
// - Responsive design
```

#### 2. Form Components

**FormField Component**
```typescript
interface FormFieldProps {
  label: string;
  name: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea';
  validation: ValidationRules;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  options?: SelectOption[]; // for select type
}

// Features:
// - Real-time validation
// - Accessibility labels
// - Error state styling
// - Loading states
// - Custom validation rules
```

**SearchInput Component**
```typescript
interface SearchInputProps {
  placeholder: string;
  onSearch: (query: string) => void;
  onClear: () => void;
  suggestions?: SearchSuggestion[];
  loading?: boolean;
  debounceMs?: number;
}

// Features:
// - Debounced search input
// - Autocomplete suggestions
// - Search history
// - Keyboard shortcuts (Ctrl+K)
// - Clear button
```

#### 3. Data Visualization Components

**MetricsCard Component**
```typescript
interface MetricsCardProps {
  title: string;
  value: number | string;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ComponentType;
  color: 'critical' | 'high' | 'medium' | 'low' | 'info';
  loading?: boolean;
}

// Features:
// - Animated counters
// - Trend indicators
// - Loading skeletons
// - Responsive sizing
// - Accessibility support
```

**CVETimeline Component**
```typescript
interface CVETimelineProps {
  cves: CVETimelineItem[];
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
  onCVEClick: (cve: CVE) => void;
}

// Features:
// - Interactive timeline visualization
// - Severity-based color coding
// - Zoom and pan functionality
// - Tooltip details
// - Date range picker
```

**SeverityBadge Component**
```typescript
interface SeverityBadgeProps {
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  score?: number;
  size?: 'sm' | 'md' | 'lg';
  showScore?: boolean;
}

// Features:
// - Consistent severity styling
// - CVSS score display
// - Multiple sizes
// - Accessibility labels
// - Hover tooltips
```

### Advanced Feature Specifications

#### 1. Real-time Updates

**WebSocket Integration**
```typescript
// Real-time CVE feed updates
const useRealtimeCVEs = () => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const ws = new WebSocket(`${config.wsUrl}/cves/feed`);

    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      dispatch(addNewCVE(update));
      showNotification({
        type: 'info',
        title: 'New CVE Available',
        message: `${update.id} - ${update.title}`,
      });
    };

    setSocket(ws);
    return () => ws.close();
  }, [dispatch]);

  return socket;
};
```

#### 2. Offline Support

**Service Worker Configuration**
```typescript
// Progressive Web App capabilities
const swConfig = {
  onUpdate: (registration) => {
    showNotification({
      type: 'info',
      title: 'Update Available',
      message: 'A new version is available. Refresh to update.',
      actions: [
        {
          label: 'Refresh',
          onClick: () => window.location.reload(),
        },
      ],
    });
  },
  onSuccess: (registration) => {
    console.log('SW registered: ', registration);
  },
};
```

#### 3. Performance Optimization

**Virtual Scrolling for Large Lists**
```typescript
const VirtualizedCVEList = ({ cves }: { cves: CVE[] }) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: cves.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120, // Estimated row height
    overscan: 10,
  });

  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualRow.size}px`,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            <CVEListItem cve={cves[virtualRow.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Comprehensive Testing Specifications

### 1. Unit Testing Patterns

**Component Testing with React Testing Library**
```typescript
// src/components/__tests__/CVECard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { CVECard } from '../CVECard';
import { mockStore } from '../../test-utils/mockStore';

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('CVECard Component', () => {
  const mockCVE = {
    id: 'CVE-2023-1234',
    title: 'Test Vulnerability',
    severity: 'HIGH' as const,
    cvssScore: 8.5,
    publishedDate: '2023-01-01T00:00:00Z',
    description: 'Test description',
  };

  test('renders CVE information correctly', () => {
    renderWithProviders(<CVECard cve={mockCVE} />);

    expect(screen.getByText('CVE-2023-1234')).toBeInTheDocument();
    expect(screen.getByText('HIGH')).toBeInTheDocument();
    expect(screen.getByText('8.5')).toBeInTheDocument();
    expect(screen.getByText('Test Vulnerability')).toBeInTheDocument();
  });

  test('applies correct severity styling', () => {
    renderWithProviders(<CVECard cve={mockCVE} />);

    const severityBadge = screen.getByText('HIGH');
    expect(severityBadge).toHaveClass('bg-orange-600');
  });

  test('handles click events', () => {
    const onClickMock = jest.fn();
    renderWithProviders(<CVECard cve={mockCVE} onClick={onClickMock} />);

    fireEvent.click(screen.getByRole('button'));
    expect(onClickMock).toHaveBeenCalledWith(mockCVE);
  });

  test('shows loading state', () => {
    renderWithProviders(<CVECard cve={mockCVE} loading />);

    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });
});
```

**Hook Testing**
```typescript
// src/hooks/__tests__/useAuth.test.ts
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { useAuth } from '../useAuth';
import { mockStore } from '../../test-utils/mockStore';

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={mockStore}>{children}</Provider>
);

describe('useAuth Hook', () => {
  test('returns authentication state', () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.user).toBe(null);
    expect(typeof result.current.login).toBe('function');
    expect(typeof result.current.logout).toBe('function');
  });

  test('handles login flow', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.login({
        username: '<EMAIL>',
        password: 'password123',
      });
    });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toBeDefined();
  });
});
```

### 2. Integration Testing

**API Integration Tests with MSW**
```typescript
// src/test-utils/server.ts
import { setupServer } from 'msw/node';
import { rest } from 'msw';

export const server = setupServer(
  rest.post('/api/v1/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        access_token: 'mock-jwt-token',
        token_type: 'bearer',
        user: {
          id: '1',
          username: '<EMAIL>',
          role: 'security_analyst',
        },
      })
    );
  }),

  rest.get('/api/v1/cves/feed', (req, res, ctx) => {
    const severity = req.url.searchParams.get('severity');
    const mockCVEs = [
      {
        id: 'CVE-2023-1234',
        title: 'Test Vulnerability',
        severity: severity || 'HIGH',
        cvssScore: 8.5,
        publishedDate: '2023-01-01T00:00:00Z',
      },
    ];

    return res(
      ctx.json({
        cves: mockCVEs,
        total: mockCVEs.length,
        limit: 50,
        offset: 0,
        has_more: false,
      })
    );
  }),
);

// Setup
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

### 3. End-to-End Testing with Playwright

**Complete User Journey Tests**
```typescript
// tests/e2e/cve-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('CVE Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password123');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('complete CVE feed workflow', async ({ page }) => {
    // Navigate to CVE feed
    await page.click('[data-testid=nav-cves]');
    await expect(page).toHaveURL('/cves');

    // Wait for CVE list to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();

    // Apply severity filter
    await page.selectOption('[data-testid=severity-filter]', 'HIGH');
    await page.waitForResponse('/api/v1/cves/feed?severity=HIGH');

    // Verify filtered results
    const cveItems = page.locator('[data-testid=cve-item]');
    await expect(cveItems).toHaveCount(5);

    // Click on first CVE
    await cveItems.first().click();
    await expect(page).toHaveURL(/\/cves\/CVE-\d{4}-\d+/);

    // Verify CVE details page
    await expect(page.locator('[data-testid=cve-header]')).toBeVisible();
    await expect(page.locator('[data-testid=cvss-metrics]')).toBeVisible();
    await expect(page.locator('[data-testid=affected-components]')).toBeVisible();
  });

  test('CVE search functionality', async ({ page }) => {
    await page.goto('/cves');

    // Use global search
    await page.fill('[data-testid=global-search]', 'CVE-2023');
    await page.press('[data-testid=global-search]', 'Enter');

    // Wait for search results
    await expect(page.locator('[data-testid=search-results]')).toBeVisible();

    // Verify search results contain query
    const results = page.locator('[data-testid=search-result-item]');
    await expect(results.first()).toContainText('CVE-2023');
  });

  test('CVE export functionality', async ({ page }) => {
    await page.goto('/cves');

    // Apply filters
    await page.selectOption('[data-testid=severity-filter]', 'CRITICAL');
    await page.waitForResponse('/api/v1/cves/feed?severity=CRITICAL');

    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid=export-button]');
    await page.click('[data-testid=export-csv]');

    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/cves-export-.*\.csv/);
  });
});
```

### 4. Performance Testing

**Lighthouse CI Integration**
```typescript
// lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/dashboard',
        'http://localhost:3000/cves',
        'http://localhost:3000/applications',
      ],
      startServerCommand: 'npm run preview',
      numberOfRuns: 3,
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['warn', { minScore: 0.9 }],
        'categories:seo': ['warn', { minScore: 0.9 }],
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
};
```

**Bundle Size Analysis**
```typescript
// vite.config.ts - Bundle analyzer
import { defineConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          redux: ['@reduxjs/toolkit', 'react-redux'],
          router: ['react-router-dom'],
          ui: ['@headlessui/react', '@heroicons/react'],
        },
      },
    },
  },
});
```

This comprehensive PRD provides the detailed blueprint for developing a modern, accessible, and thoroughly tested React interface for the CVE Feed Service with dark mode as the primary design focus.
