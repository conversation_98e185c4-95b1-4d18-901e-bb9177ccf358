CVE Feed Service Documentation
=================================

Welcome to the **CVE Feed Service** - an enterprise-grade vulnerability management platform that provides comprehensive solutions for tracking, assessing, and managing Common Vulnerabilities and Exposures (CVE) data across your organization's applications and infrastructure.

🎨 **NEW: Modern React Interface** - Complete dark-mode-first web interface with comprehensive user flows! See :doc:`PRD_React_Interface` for specifications.

🛡️ **Enterprise Security Platform**
   Complete vulnerability management with real-time CVE feeds, automated risk assessment, and compliance reporting.

🧪 **World-Class Testing Framework**
   Industry-leading testing approach with 72 comprehensive tests including advanced BDD scenarios for business validation.

📊 **Business-Driven Development**
   Natural language scenarios that validate business requirements and ensure stakeholder alignment.

🚀 **Production-Ready Architecture**
   Modern async Python with FastAPI, comprehensive monitoring, and enterprise deployment patterns.

.. toctree::
   :maxdepth: 2
   :caption: 📚 Getting Started:

   overview
   installation

.. toctree::
   :maxdepth: 2
   :caption: 👥 User Documentation:

   user-guide/index

.. toctree::
   :maxdepth: 2
   :caption: 🔧 Technical Documentation:

   services/index
   api-reference/index

.. toctree::
   :maxdepth: 2
   :caption: 🧪 Testing & Quality:

   testing/index
   bdd/index

.. toctree::
   :maxdepth: 2
   :caption: 🚀 Development & Deployment:

   development/index
   deployment/index
   enterprise/index

.. toctree::
   :maxdepth: 2
   :caption: 🎨 React Interface:

   PRD_React_Interface

Quick Start
-----------

The CVE Feed Service is a FastAPI-based application that:

* Manages application inventories with component tracking
* Ingests CVE data from the National Vulnerability Database (NVD)
* Provides tailored vulnerability feeds based on CPE (Common Platform Enumeration) mappings
* Offers role-based access control for security teams

Key Features
------------

✅ **Enterprise Application Management**
   Complete CRUD operations for applications and their components with advanced lifecycle management

✅ **Intelligent CVE Data Ingestion**
   Automated ingestion from NVD with rate limiting, incremental updates, and bulk processing capabilities

✅ **Tailored Vulnerability Feeds**
   Application-specific CVE feeds based on component CPE mappings with risk prioritization

✅ **Advanced Authentication & Authorization**
   JWT-based auth with role-based access control (RBAC), API keys, and multi-factor authentication support

✅ **CPE Mapping & Validation**
   Support for CPE 2.3 format with validation, normalization, and intelligent matching logic

✅ **Comprehensive Testing Framework**
   72 tests including unit, integration, API, external, and BDD scenarios with 100% pass rate

✅ **Business-Driven Development**
   75+ Gherkin scenarios covering all business workflows with natural language validation

✅ **Security & Compliance**
   Built-in support for NIST CSF, SOC 2, PCI DSS, GDPR, and HIPAA compliance frameworks

✅ **Performance & Scalability**
   Auto-scaling, caching strategies, load testing, and performance monitoring

✅ **Error Handling & Recovery**
   Comprehensive fault tolerance, disaster recovery, and graceful degradation capabilities

Getting Started
---------------

1. **Installation**: Follow the :doc:`installation` guide to set up your environment
2. **User Guide**: Read the :doc:`user-guide/index` for detailed usage instructions
3. **API Reference**: Explore the :doc:`api-reference/index` for technical details
4. **Development**: See :doc:`development/index` for contributing guidelines

Architecture Overview
---------------------

The CVE Feed Service follows a modern, API-first architecture:

.. code-block:: text

   ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
   │   Web UI        │    │   CLI Tools     │    │  External APIs  │
   │   (Future)      │    │                 │    │                 │
   └─────────────────┘    └─────────────────┘    └─────────────────┘
            │                       │                       │
            └───────────────────────┼───────────────────────┘
                                    │
   ┌─────────────────────────────────┼─────────────────────────────────┐
   │                    FastAPI Application                            │
   │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐ │
   │  │     API     │  │   Auth      │  │  Services   │  │   Utils   │ │
   │  │ Endpoints   │  │ & Security  │  │   Layer     │  │           │ │
   │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘ │
   └─────────────────────────────────┼─────────────────────────────────┘
                                     │
   ┌─────────────────────────────────┼─────────────────────────────────┐
   │                    Data Layer                                     │
   │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐ │
   │  │ PostgreSQL  │  │ SQLAlchemy  │  │  Alembic    │  │  Models   │ │
   │  │  Database   │  │    ORM      │  │ Migrations  │  │           │ │
   │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘ │
   └─────────────────────────────────────────────────────────────────┘

Support
-------

* **Issues**: Report bugs and feature requests on `GitHub Issues <https://github.com/forkrul/day3-cve-feed/issues>`_
* **Documentation**: This documentation is available online and in the repository
* **API Documentation**: Interactive API docs available at ``/api/v1/docs`` when running the service

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
