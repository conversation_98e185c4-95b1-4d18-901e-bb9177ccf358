"""Comprehensive API tests for authentication endpoints."""

import pytest
from httpx import AsyncClient
from uuid import uuid4
from datetime import datetime, timedelta

from src.cve_feed_service.main import app


@pytest.mark.asyncio
class TestAuthAPI:
    """Test authentication API endpoints comprehensively."""

    async def test_user_registration_success(self, authenticated_client):
        """Test successful user registration."""
        client = authenticated_client
        client = authenticated_client
        user_data = {
            "username": f"testuser_{uuid4().hex[:8]}",
            "email": f"test_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }

        response = await client.post("/api/v1/auth/users", json=user_data)

        # Should return 201 (created), 422 (validation error), or 401 (auth required)
        assert response.status_code in [201, 401, 422]
        
        if response.status_code == 201:
            result = response.json()
            assert result["username"] == user_data["username"]
            assert result["email"] == user_data["email"]
            assert "password" not in result  # Password should not be returned

    async def test_self(self, authenticated_client):
        """Test user registration with validation errors."""
        client = authenticated_client
        # Test missing required fields
        invalid_data_sets = [
            {},  # Empty data
            {"username": "test"},  # Missing email and password
            {"email": "<EMAIL>"},  # Missing username and password
            {"password": "test123"},  # Missing username and email
            {"username": "", "email": "<EMAIL>", "password": "test123"},  # Empty username
            {"username": "test", "email": "", "password": "test123"},  # Empty email
            {"username": "test", "email": "<EMAIL>", "password": ""},  # Empty password
            {"username": "test", "email": "invalid-email", "password": "test123"},  # Invalid email
            {"username": "test", "email": "<EMAIL>", "password": "123"},  # Short password
        ]
        
        for invalid_data in invalid_data_sets:
            response = await client.post("/api/v1/auth/users", json=invalid_data)
            assert response.status_code in [401, 422]

    async def test_self(self, authenticated_client):
        """Test user registration with duplicate username."""
        client = authenticated_client
        username = f"duplicate_user_{uuid4().hex[:8]}"
        
        user_data = {
            "username": username,
            "email": f"test1_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "Test User 1"
        }
        
        # First registration
        response1 = await client.post("/api/v1/auth/users", json=user_data)
        
        # Second registration with same username
        user_data["email"] = f"test2_{uuid4().hex[:8]}@example.com"
        response2 = await client.post("/api/v1/auth/users", json=user_data)
        
        # First should succeed or fail with validation or auth
        assert response1.status_code in [201, 401, 422]
        
        # Second should fail if first succeeded
        if response1.status_code == 201:
            assert response2.status_code in [400, 409, 422]  # Conflict or validation error

    async def test_self(self, authenticated_client):
        """Test successful user login."""
        client = authenticated_client
        # First create a user
        username = f"loginuser_{uuid4().hex[:8]}"
        password = "testpassword123"
        
        user_data = {
            "username": username,
            "email": f"login_{uuid4().hex[:8]}@example.com",
            "password": password,
            "full_name": "Login Test User"
        }
        
        create_response = await client.post("/api/v1/auth/users", json=user_data)
        
        if create_response.status_code == 201:
            # Try to login
            login_data = {
                "username": username,
                "password": password
            }
            
            response = await client.post("/api/v1/auth/login", data=login_data)
            
            # Should return 200 with token or 422/401 for auth issues
            assert response.status_code in [200, 401, 422]
            
            if response.status_code == 200:
                result = response.json()
                assert "access_token" in result
                assert "token_type" in result

    async def test_self(self, authenticated_client):
        """Test user login with invalid credentials."""
        client = authenticated_client
        login_data = {
            "username": "nonexistent_user",
            "password": "wrongpassword"
        }
        
        response = await client.post("/api/v1/auth/login", data=login_data)
        
        # Should return 401 (unauthorized) or 422 (validation error)
        assert response.status_code in [401, 422]

    async def test_self(self, authenticated_client):
        """Test user login with missing fields."""
        client = authenticated_client
        invalid_login_data = [
            {},  # Empty data
            {"username": "test"},  # Missing password
            {"password": "test123"},  # Missing username
            {"username": "", "password": "test123"},  # Empty username
            {"username": "test", "password": ""},  # Empty password
        ]
        
        for invalid_data in invalid_login_data:
            response = await client.post("/api/v1/auth/login", data=invalid_data)
            assert response.status_code == 422

    async def test_self(self, authenticated_client):
        """Test getting current user without authentication."""
        client = authenticated_client
        response = await client.get("/api/v1/auth/me")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test getting current user with invalid token."""
        client = authenticated_client
        headers = {"Authorization": "Bearer invalid_token"}
        response = await client.get("/api/v1/auth/me", headers=headers)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test listing users without authentication."""
        client = authenticated_client
        response = await client.get("/api/v1/auth/users")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test updating user without authentication."""
        client = authenticated_client
        user_id = str(uuid4())
        update_data = {"full_name": "Updated Name"}
        
        response = await client.put(f"/api/v1/auth/users/{user_id}", json=update_data)

        # Should return 401 (unauthorized) or 405 (method not allowed)
        assert response.status_code in [401, 405]

    async def test_self(self, authenticated_client):
        """Test deleting user without authentication."""
        client = authenticated_client
        user_id = str(uuid4())
        
        response = await client.delete(f"/api/v1/auth/users/{user_id}")

        # Should return 401 (unauthorized) or 405 (method not allowed)
        assert response.status_code in [401, 405]

    async def test_self(self, authenticated_client):
        """Test changing password without authentication."""
        client = authenticated_client
        password_data = {
            "old_password": "oldpass",
            "new_password": "newpass"
        }
        
        response = await client.post("/api/v1/auth/change-password", json=password_data)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test creating API key without authentication."""
        client = authenticated_client
        api_key_data = {
            "name": "Test API Key",
            "description": "Test key",
            "expires_at": (datetime.now() + timedelta(days=30)).isoformat()
        }
        
        response = await client.post("/api/v1/auth/api-keys", json=api_key_data)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test listing API keys without authentication."""
        client = authenticated_client
        response = await client.get("/api/v1/auth/api-keys")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test deleting API key without authentication."""
        client = authenticated_client
        key_id = str(uuid4())
        
        response = await client.delete(f"/api/v1/auth/api-keys/{key_id}")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_self(self, authenticated_client):
        """Test various HTTP methods on auth endpoints."""
        client = authenticated_client
        # Test unsupported methods
        response = await client.patch("/api/v1/auth/users")
        assert response.status_code == 405  # Method not allowed
        
        response = await client.put("/api/v1/auth/login")
        assert response.status_code == 405

    async def test_self(self, authenticated_client):
        """Test content type validation for auth endpoints."""
        client = authenticated_client
        # Test login with wrong content type
        response = await client.post(
            "/api/v1/auth/login",
            data="username=test&password=test",
            headers={"Content-Type": "text/plain"}
        )
        
        # Should handle content type appropriately
        assert response.status_code in [422, 415, 401]

    async def test_self(self, authenticated_client):
        """Test authentication with special characters."""
        client = authenticated_client
        special_usernames = [
            "<EMAIL>",
            "user.name",
            "user_name",
            "user-name",
            "user123",
        ]
        
        for username in special_usernames:
            user_data = {
                "username": username,
                "email": f"{username.replace('@', '_').replace('.', '_')}@example.com",
                "password": "testpassword123",
                "full_name": "Special User"
            }
            
            response = await client.post("/api/v1/auth/users", json=user_data)
            # Should handle special characters appropriately
            assert response.status_code in [201, 401, 422]

    async def test_self(self, authenticated_client):
        """Test password complexity requirements."""
        client = authenticated_client
        weak_passwords = [
            "123",
            "password",
            "abc",
            "12345678",
            "aaaaaaaa",
        ]
        
        for weak_password in weak_passwords:
            user_data = {
                "username": f"weakpass_{uuid4().hex[:8]}",
                "email": f"weak_{uuid4().hex[:8]}@example.com",
                "password": weak_password,
                "full_name": "Weak Password User"
            }
            
            response = await client.post("/api/v1/auth/users", json=user_data)
            # Weak passwords should be rejected or auth required
            assert response.status_code in [400, 401, 422]

    async def test_self(self, authenticated_client):
        """Test concurrent authentication requests."""
        client = authenticated_client
        import asyncio
        
        async def make_login_request():
            login_data = {
                "username": "nonexistent",
                "password": "invalid"
            }
            return await client.post("/api/v1/auth/login", data=login_data)
        
        # Make 5 concurrent login attempts
        tasks = [make_login_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All should fail with 401 or 422
        for response in responses:
            assert response.status_code in [401, 422]

    async def test_self(self, authenticated_client):
        """Test authentication response headers."""
        client = authenticated_client
        response = await client.post("/api/v1/auth/login", data={})
        
        headers = response.headers
        assert "content-type" in headers
        
        # Check for security headers (might be set by middleware)
        # These are optional but good to have
        security_headers = [
            "x-content-type-options",
            "x-frame-options",
            "x-xss-protection"
        ]
        
        # Don't assert these as they might not be implemented yet
        for header in security_headers:
            if header in headers:
                assert headers[header] is not None

    async def test_self(self, authenticated_client):
        """Test authentication error response format."""
        client = authenticated_client
        response = await client.post("/api/v1/auth/login", data={})
        
        if response.status_code == 422:
            error_response = response.json()
            assert "detail" in error_response
            
            # Check if detail is properly formatted
            detail = error_response["detail"]
            assert isinstance(detail, (list, str, dict))

    async def test_self(self, authenticated_client):
        """Test handling of large authentication payloads."""
        client = authenticated_client
        # Create a user with very long fields
        large_data = {
            "username": f"user_{uuid4().hex[:8]}",
            "email": f"test_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "x" * 1000  # Very long name
        }
        
        response = await client.post("/api/v1/auth/users", json=large_data)

        # Should handle large payloads gracefully
        assert response.status_code in [201, 401, 413, 422]  # 413 = Payload too large
