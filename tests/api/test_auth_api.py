"""Comprehensive API tests for authentication endpoints."""

import pytest
from httpx import AsyncClient
from uuid import uuid4
from datetime import datetime, timedelta

from src.cve_feed_service.main import app


class TestAuthAPI:
    """Test authentication API endpoints comprehensively."""

    @pytest.fixture
    async def client(self, async_client):
        """Get async client."""
        return async_client

    async def test_user_registration_success(self, client):
        """Test successful user registration."""
        user_data = {
            "username": f"testuser_{uuid4().hex[:8]}",
            "email": f"test_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        
        response = await client.post("/api/v1/auth/users", json=user_data)
        
        # Should return 201 (created) or 422 (validation error)
        assert response.status_code in [201, 422]
        
        if response.status_code == 201:
            result = response.json()
            assert result["username"] == user_data["username"]
            assert result["email"] == user_data["email"]
            assert "password" not in result  # Password should not be returned

    async def test_user_registration_validation_errors(self, client):
        """Test user registration with validation errors."""
        # Test missing required fields
        invalid_data_sets = [
            {},  # Empty data
            {"username": "test"},  # Missing email and password
            {"email": "<EMAIL>"},  # Missing username and password
            {"password": "test123"},  # Missing username and email
            {"username": "", "email": "<EMAIL>", "password": "test123"},  # Empty username
            {"username": "test", "email": "", "password": "test123"},  # Empty email
            {"username": "test", "email": "<EMAIL>", "password": ""},  # Empty password
            {"username": "test", "email": "invalid-email", "password": "test123"},  # Invalid email
            {"username": "test", "email": "<EMAIL>", "password": "123"},  # Short password
        ]
        
        for invalid_data in invalid_data_sets:
            response = await client.post("/api/v1/auth/users", json=invalid_data)
            assert response.status_code == 422

    async def test_user_registration_duplicate_username(self, client):
        """Test user registration with duplicate username."""
        username = f"duplicate_user_{uuid4().hex[:8]}"
        
        user_data = {
            "username": username,
            "email": f"test1_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "Test User 1"
        }
        
        # First registration
        response1 = await client.post("/api/v1/auth/users", json=user_data)
        
        # Second registration with same username
        user_data["email"] = f"test2_{uuid4().hex[:8]}@example.com"
        response2 = await client.post("/api/v1/auth/users", json=user_data)
        
        # First should succeed or fail with validation
        assert response1.status_code in [201, 422]
        
        # Second should fail if first succeeded
        if response1.status_code == 201:
            assert response2.status_code in [400, 409, 422]  # Conflict or validation error

    async def test_user_login_success(self, client):
        """Test successful user login."""
        # First create a user
        username = f"loginuser_{uuid4().hex[:8]}"
        password = "testpassword123"
        
        user_data = {
            "username": username,
            "email": f"login_{uuid4().hex[:8]}@example.com",
            "password": password,
            "full_name": "Login Test User"
        }
        
        create_response = await client.post("/api/v1/auth/users", json=user_data)
        
        if create_response.status_code == 201:
            # Try to login
            login_data = {
                "username": username,
                "password": password
            }
            
            response = await client.post("/api/v1/auth/login", data=login_data)
            
            # Should return 200 with token or 422/401 for auth issues
            assert response.status_code in [200, 401, 422]
            
            if response.status_code == 200:
                result = response.json()
                assert "access_token" in result
                assert "token_type" in result

    async def test_user_login_invalid_credentials(self, client):
        """Test user login with invalid credentials."""
        login_data = {
            "username": "nonexistent_user",
            "password": "wrongpassword"
        }
        
        response = await client.post("/api/v1/auth/login", data=login_data)
        
        # Should return 401 (unauthorized) or 422 (validation error)
        assert response.status_code in [401, 422]

    async def test_user_login_missing_fields(self, client):
        """Test user login with missing fields."""
        invalid_login_data = [
            {},  # Empty data
            {"username": "test"},  # Missing password
            {"password": "test123"},  # Missing username
            {"username": "", "password": "test123"},  # Empty username
            {"username": "test", "password": ""},  # Empty password
        ]
        
        for invalid_data in invalid_login_data:
            response = await client.post("/api/v1/auth/login", data=invalid_data)
            assert response.status_code == 422

    async def test_get_current_user_without_auth(self, client):
        """Test getting current user without authentication."""
        response = await client.get("/api/v1/auth/me")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_get_current_user_with_invalid_token(self, client):
        """Test getting current user with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = await client.get("/api/v1/auth/me", headers=headers)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_list_users_without_auth(self, client):
        """Test listing users without authentication."""
        response = await client.get("/api/v1/auth/users")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_update_user_without_auth(self, client):
        """Test updating user without authentication."""
        user_id = str(uuid4())
        update_data = {"full_name": "Updated Name"}
        
        response = await client.put(f"/api/v1/auth/users/{user_id}", json=update_data)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_delete_user_without_auth(self, client):
        """Test deleting user without authentication."""
        user_id = str(uuid4())
        
        response = await client.delete(f"/api/v1/auth/users/{user_id}")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_change_password_without_auth(self, client):
        """Test changing password without authentication."""
        password_data = {
            "old_password": "oldpass",
            "new_password": "newpass"
        }
        
        response = await client.post("/api/v1/auth/change-password", json=password_data)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_create_api_key_without_auth(self, client):
        """Test creating API key without authentication."""
        api_key_data = {
            "name": "Test API Key",
            "description": "Test key",
            "expires_at": (datetime.now() + timedelta(days=30)).isoformat()
        }
        
        response = await client.post("/api/v1/auth/api-keys", json=api_key_data)
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_list_api_keys_without_auth(self, client):
        """Test listing API keys without authentication."""
        response = await client.get("/api/v1/auth/api-keys")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_delete_api_key_without_auth(self, client):
        """Test deleting API key without authentication."""
        key_id = str(uuid4())
        
        response = await client.delete(f"/api/v1/auth/api-keys/{key_id}")
        
        # Should return 401 (unauthorized)
        assert response.status_code == 401

    async def test_auth_endpoints_http_methods(self, client):
        """Test various HTTP methods on auth endpoints."""
        # Test unsupported methods
        response = await client.patch("/api/v1/auth/users")
        assert response.status_code == 405  # Method not allowed
        
        response = await client.put("/api/v1/auth/login")
        assert response.status_code == 405

    async def test_auth_content_type_validation(self, client):
        """Test content type validation for auth endpoints."""
        # Test login with wrong content type
        response = await client.post(
            "/api/v1/auth/login",
            data="username=test&password=test",
            headers={"Content-Type": "text/plain"}
        )
        
        # Should handle content type appropriately
        assert response.status_code in [422, 415, 401]

    async def test_auth_special_characters_in_credentials(self, client):
        """Test authentication with special characters."""
        special_usernames = [
            "<EMAIL>",
            "user.name",
            "user_name",
            "user-name",
            "user123",
        ]
        
        for username in special_usernames:
            user_data = {
                "username": username,
                "email": f"{username.replace('@', '_').replace('.', '_')}@example.com",
                "password": "testpassword123",
                "full_name": "Special User"
            }
            
            response = await client.post("/api/v1/auth/users", json=user_data)
            # Should handle special characters appropriately
            assert response.status_code in [201, 422]

    async def test_auth_password_complexity(self, client):
        """Test password complexity requirements."""
        weak_passwords = [
            "123",
            "password",
            "abc",
            "12345678",
            "aaaaaaaa",
        ]
        
        for weak_password in weak_passwords:
            user_data = {
                "username": f"weakpass_{uuid4().hex[:8]}",
                "email": f"weak_{uuid4().hex[:8]}@example.com",
                "password": weak_password,
                "full_name": "Weak Password User"
            }
            
            response = await client.post("/api/v1/auth/users", json=user_data)
            # Weak passwords should be rejected
            assert response.status_code in [422, 400]

    async def test_auth_concurrent_requests(self, client):
        """Test concurrent authentication requests."""
        import asyncio
        
        async def make_login_request():
            login_data = {
                "username": "nonexistent",
                "password": "invalid"
            }
            return await client.post("/api/v1/auth/login", data=login_data)
        
        # Make 5 concurrent login attempts
        tasks = [make_login_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All should fail with 401 or 422
        for response in responses:
            assert response.status_code in [401, 422]

    async def test_auth_response_headers(self, client):
        """Test authentication response headers."""
        response = await client.post("/api/v1/auth/login", data={})
        
        headers = response.headers
        assert "content-type" in headers
        
        # Check for security headers (might be set by middleware)
        # These are optional but good to have
        security_headers = [
            "x-content-type-options",
            "x-frame-options",
            "x-xss-protection"
        ]
        
        # Don't assert these as they might not be implemented yet
        for header in security_headers:
            if header in headers:
                assert headers[header] is not None

    async def test_auth_error_response_format(self, client):
        """Test authentication error response format."""
        response = await client.post("/api/v1/auth/login", data={})
        
        if response.status_code == 422:
            error_response = response.json()
            assert "detail" in error_response
            
            # Check if detail is properly formatted
            detail = error_response["detail"]
            assert isinstance(detail, (list, str, dict))

    async def test_auth_large_payload_handling(self, client):
        """Test handling of large authentication payloads."""
        # Create a user with very long fields
        large_data = {
            "username": f"user_{uuid4().hex[:8]}",
            "email": f"test_{uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "x" * 1000  # Very long name
        }
        
        response = await client.post("/api/v1/auth/users", json=large_data)
        
        # Should handle large payloads gracefully
        assert response.status_code in [201, 422, 413]  # 413 = Payload too large
