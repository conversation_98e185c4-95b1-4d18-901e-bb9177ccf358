"""Comprehensive API tests for CVE endpoints."""

import pytest
from httpx import AsyncClient
from uuid import uuid4
from datetime import datetime, timedelta

from src.cve_feed_service.main import app


class TestCVEsAPI:
    """Test CVE API endpoints comprehensively."""

    @pytest.fixture
    async def authenticated_client(self, async_client):
        """Create an authenticated client."""
        return async_client

    async def test_list_cves_default(self, authenticated_client):
        """Test listing CVEs with default parameters."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/")
        
        # Should return 200 or 401 (if auth required)
        assert response.status_code in [200, 401]
        
        if response.status_code == 200:
            result = response.json()
            assert "cves" in result
            assert "total" in result
            assert isinstance(result["cves"], list)
            assert isinstance(result["total"], int)

    async def test_list_cves_with_pagination(self, authenticated_client):
        """Test listing CVEs with pagination."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/?skip=0&limit=5")
        
        assert response.status_code in [200, 401]
        
        if response.status_code == 200:
            result = response.json()
            assert "cves" in result
            assert len(result["cves"]) <= 5

    async def test_list_cves_with_severity_filter(self, authenticated_client):
        """Test listing CVEs with severity filter."""
        client = authenticated_client
        
        for severity in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]:
            response = await client.get(f"/api/v1/cves/?severity={severity}")
            assert response.status_code in [200, 401]

    async def test_list_cves_with_search(self, authenticated_client):
        """Test listing CVEs with search parameter."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/?search=nginx")
        
        assert response.status_code in [200, 401]
        
        if response.status_code == 200:
            result = response.json()
            assert "cves" in result

    async def test_list_cves_with_date_range(self, authenticated_client):
        """Test listing CVEs with date range filters."""
        client = authenticated_client
        
        # Test with date range
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = await client.get(f"/api/v1/cves/?published_after={start_date}&published_before={end_date}")
        
        assert response.status_code in [200, 401]

    async def test_list_cves_invalid_parameters(self, authenticated_client):
        """Test listing CVEs with invalid parameters."""
        client = authenticated_client
        
        # Test invalid severity
        response = await client.get("/api/v1/cves/?severity=INVALID")
        assert response.status_code in [200, 400, 422, 401]
        
        # Test negative skip
        response = await client.get("/api/v1/cves/?skip=-1")
        assert response.status_code in [200, 422, 401]
        
        # Test invalid date format
        response = await client.get("/api/v1/cves/?published_after=invalid-date")
        assert response.status_code in [200, 422, 401]

    async def test_get_cve_feed_default(self, authenticated_client):
        """Test getting CVE feed with default parameters."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/feed")
        
        assert response.status_code in [200, 401]
        
        if response.status_code == 200:
            result = response.json()
            assert "cves" in result
            assert "total" in result
            assert "limit" in result
            # API uses "offset" instead of "skip"
            assert "offset" in result

    async def test_get_cve_feed_with_application_filter(self, authenticated_client):
        """Test getting CVE feed filtered by application."""
        client = authenticated_client
        
        app_id = str(uuid4())
        response = await client.get(f"/api/v1/cves/feed?application_id={app_id}")

        assert response.status_code in [200, 400, 401, 404]

    async def test_get_cve_feed_with_severity_filter(self, authenticated_client):
        """Test getting CVE feed with severity filter."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/feed?severity=HIGH")
        
        assert response.status_code in [200, 401]

    async def test_get_cve_feed_with_multiple_filters(self, authenticated_client):
        """Test getting CVE feed with multiple filters."""
        client = authenticated_client
        
        params = {
            "severity": "HIGH",
            "limit": "10",
            "skip": "0",
            "published_after": (datetime.now() - timedelta(days=7)).isoformat()
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        response = await client.get(f"/api/v1/cves/feed?{query_string}")
        
        assert response.status_code in [200, 401]

    async def test_get_cve_by_id(self, authenticated_client):
        """Test getting CVE by ID."""
        client = authenticated_client
        
        cve_id = "CVE-2023-0001"
        response = await client.get(f"/api/v1/cves/{cve_id}")
        
        # Should return 404 (not found) or 401 (unauthorized)
        assert response.status_code in [200, 404, 401]

    async def test_get_cve_invalid_id_format(self, authenticated_client):
        """Test getting CVE with invalid ID format."""
        client = authenticated_client
        
        invalid_ids = [
            "invalid-cve-id",
            "CVE-INVALID",
            "123456",
            "CVE-2023-INVALID"
        ]
        
        for invalid_id in invalid_ids:
            response = await client.get(f"/api/v1/cves/{invalid_id}")
            assert response.status_code in [404, 422, 401]

    async def test_cve_endpoints_http_methods(self, authenticated_client):
        """Test various HTTP methods on CVE endpoints."""
        client = authenticated_client
        
        # Test unsupported methods on list endpoint
        response = await client.post("/api/v1/cves/")
        assert response.status_code == 405  # Method not allowed
        
        response = await client.put("/api/v1/cves/")
        assert response.status_code == 405
        
        response = await client.delete("/api/v1/cves/")
        assert response.status_code == 405

    async def test_cve_feed_response_structure(self, authenticated_client):
        """Test CVE feed response structure."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/feed")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check required fields (API uses "offset" instead of "skip")
            required_fields = ["cves", "total", "limit", "offset"]
            for field in required_fields:
                assert field in result
            
            # Check data types
            assert isinstance(result["cves"], list)
            assert isinstance(result["total"], int)
            assert isinstance(result["limit"], int)
            assert isinstance(result["skip"], int)

    async def test_cve_search_functionality(self, authenticated_client):
        """Test CVE search functionality."""
        client = authenticated_client
        
        search_terms = [
            "nginx",
            "apache",
            "remote code execution",
            "sql injection",
            "cross-site scripting"
        ]
        
        for term in search_terms:
            response = await client.get(f"/api/v1/cves/?search={term}")
            assert response.status_code in [200, 401]

    async def test_cve_feed_pagination_edge_cases(self, authenticated_client):
        """Test CVE feed pagination edge cases."""
        client = authenticated_client
        
        # Test with very large skip
        response = await client.get("/api/v1/cves/feed?skip=999999")
        assert response.status_code in [200, 401]
        
        # Test with zero limit
        response = await client.get("/api/v1/cves/feed?limit=0")
        assert response.status_code in [200, 422, 401]
        
        # Test with very large limit
        response = await client.get("/api/v1/cves/feed?limit=999999")
        assert response.status_code in [200, 422, 401]

    async def test_cve_date_filtering_edge_cases(self, authenticated_client):
        """Test CVE date filtering edge cases."""
        client = authenticated_client
        
        # Test with future dates
        future_date = (datetime.now() + timedelta(days=365)).isoformat()
        response = await client.get(f"/api/v1/cves/?published_after={future_date}")
        assert response.status_code in [200, 401]
        
        # Test with very old dates
        old_date = datetime(2000, 1, 1).isoformat()
        response = await client.get(f"/api/v1/cves/?published_after={old_date}")
        assert response.status_code in [200, 401]
        
        # Test with end date before start date
        start_date = datetime.now().isoformat()
        end_date = (datetime.now() - timedelta(days=1)).isoformat()
        response = await client.get(f"/api/v1/cves/?published_after={start_date}&published_before={end_date}")
        assert response.status_code in [200, 422, 401]

    async def test_cve_response_headers(self, authenticated_client):
        """Test CVE endpoint response headers."""
        client = authenticated_client
        
        response = await client.get("/api/v1/cves/")
        
        # Check for common headers
        headers = response.headers
        assert "content-type" in headers
        
        if response.status_code == 200:
            assert "application/json" in headers["content-type"]

    async def test_cve_concurrent_requests(self, authenticated_client):
        """Test concurrent requests to CVE endpoints."""
        import asyncio
        
        client = authenticated_client
        
        async def make_request():
            return await client.get("/api/v1/cves/feed")
        
        # Make 5 concurrent requests
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All requests should complete successfully
        for response in responses:
            assert response.status_code in [200, 401]

    async def test_cve_special_characters_in_search(self, authenticated_client):
        """Test CVE search with special characters."""
        client = authenticated_client
        
        special_searches = [
            "test & search",
            "test + plus",
            "test%20encoded",
            "test'quote",
            'test"doublequote',
            "test<script>",
            "test;semicolon"
        ]
        
        for search_term in special_searches:
            response = await client.get(f"/api/v1/cves/?search={search_term}")
            assert response.status_code in [200, 401]

    async def test_cve_feed_performance(self, authenticated_client):
        """Test CVE feed performance with various parameters."""
        import time
        
        client = authenticated_client
        
        # Test response time for different request sizes
        limits = [1, 10, 50, 100]
        
        for limit in limits:
            start_time = time.time()
            response = await client.get(f"/api/v1/cves/feed?limit={limit}")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Response should be reasonably fast (under 2 seconds)
            if response.status_code == 200:
                assert response_time < 2.0

    async def test_cve_error_handling(self, authenticated_client):
        """Test CVE endpoint error handling."""
        client = authenticated_client
        
        # Test malformed requests
        response = await client.get("/api/v1/cves/?limit=invalid")
        assert response.status_code in [200, 422, 401]
        
        response = await client.get("/api/v1/cves/?skip=invalid")
        assert response.status_code in [200, 422, 401]
        
        # Test with extremely long search terms
        long_search = "x" * 1000
        response = await client.get(f"/api/v1/cves/?search={long_search}")
        assert response.status_code in [200, 414, 401]  # 414 = URI too long
