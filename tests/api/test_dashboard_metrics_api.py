"""
API TDD Tests for Dashboard Metrics Endpoint
Test-driven development approach for /api/v1/dashboard/metrics endpoint
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from src.cve_feed_service.main import app


class TestDashboardMetricsAPISuccess:
    """Test successful dashboard metrics retrieval"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Authentication headers for API requests"""
        return {"Authorization": "Bearer valid-jwt-token"}

    @pytest.fixture
    def expected_metrics_structure(self):
        """Expected metrics response structure"""
        return {
            "total_cves": 150,
            "critical_cves": 5,
            "high_cves": 25,
            "medium_cves": 70,
            "low_cves": 50,
            "applications_count": 12,
            "components_count": 45,
            "last_updated": "2023-12-01T10:30:00Z",
            "trend_data": {
                "cves_last_7_days": 8,
                "cves_last_30_days": 32,
                "new_applications_last_30_days": 2
            }
        }

    def test_dashboard_metrics_api_returns_200(self, client, auth_headers):
        """Test that dashboard metrics endpoint returns HTTP 200"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 100}

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)

            # Assert
            assert response.status_code == 200

    def test_dashboard_metrics_api_returns_complete_structure(self, client, auth_headers, expected_metrics_structure):
        """Test that dashboard metrics returns complete data structure"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = expected_metrics_structure

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)
            data = response.json()

            # Assert
            assert "total_cves" in data
            assert "critical_cves" in data
            assert "high_cves" in data
            assert "medium_cves" in data
            assert "low_cves" in data
            assert "applications_count" in data
            assert "components_count" in data
            assert "last_updated" in data
            assert "trend_data" in data

    def test_dashboard_metrics_api_severity_counts_sum_to_total(self, client, auth_headers, expected_metrics_structure):
        """Test that severity counts sum to total CVE count"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = expected_metrics_structure

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)
            data = response.json()

            # Assert
            severity_sum = (
                data["critical_cves"] + 
                data["high_cves"] + 
                data["medium_cves"] + 
                data["low_cves"]
            )
            assert severity_sum == data["total_cves"]

    def test_dashboard_metrics_api_includes_trend_data(self, client, auth_headers, expected_metrics_structure):
        """Test that dashboard metrics includes trend information"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = expected_metrics_structure

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)
            data = response.json()

            # Assert
            trend_data = data["trend_data"]
            assert "cves_last_7_days" in trend_data
            assert "cves_last_30_days" in trend_data
            assert "new_applications_last_30_days" in trend_data
            assert isinstance(trend_data["cves_last_7_days"], int)
            assert isinstance(trend_data["cves_last_30_days"], int)

    def test_dashboard_metrics_api_last_updated_is_valid_timestamp(self, client, auth_headers, expected_metrics_structure):
        """Test that last_updated field contains valid ISO timestamp"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = expected_metrics_structure

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)
            data = response.json()

            # Assert
            last_updated = data["last_updated"]
            # Should be able to parse as ISO format
            parsed_date = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            assert isinstance(parsed_date, datetime)
            # Should be recent (within last 24 hours for this test)
            assert parsed_date > datetime.now().replace(tzinfo=parsed_date.tzinfo) - timedelta(days=1)


class TestDashboardMetricsAPIFiltering:
    """Test dashboard metrics with filtering options"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Authentication headers for API requests"""
        return {"Authorization": "Bearer valid-jwt-token"}

    def test_dashboard_metrics_api_with_date_range_filter(self, client, auth_headers):
        """Test dashboard metrics with date range filtering"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 50, "filtered": True}

            # Act
            response = client.get(
                "/api/v1/dashboard/metrics?date_range=last_30_days",
                headers=auth_headers
            )

            # Assert
            assert response.status_code == 200
            mock_metrics.assert_called_once()
            call_args = mock_metrics.call_args
            assert "date_range" in call_args.kwargs
            assert call_args.kwargs["date_range"] == "last_30_days"

    def test_dashboard_metrics_api_with_application_filter(self, client, auth_headers):
        """Test dashboard metrics filtered by specific application"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 25, "application_filtered": True}

            # Act
            response = client.get(
                "/api/v1/dashboard/metrics?application_id=app-123",
                headers=auth_headers
            )

            # Assert
            assert response.status_code == 200
            mock_metrics.assert_called_once()
            call_args = mock_metrics.call_args
            assert "application_id" in call_args.kwargs
            assert call_args.kwargs["application_id"] == "app-123"

    def test_dashboard_metrics_api_with_multiple_filters(self, client, auth_headers):
        """Test dashboard metrics with multiple filter parameters"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 15, "multi_filtered": True}

            # Act
            response = client.get(
                "/api/v1/dashboard/metrics?date_range=last_7_days&application_id=app-123&severity=HIGH",
                headers=auth_headers
            )

            # Assert
            assert response.status_code == 200
            call_args = mock_metrics.call_args
            assert call_args.kwargs["date_range"] == "last_7_days"
            assert call_args.kwargs["application_id"] == "app-123"
            assert call_args.kwargs["severity"] == "HIGH"


class TestDashboardMetricsAPIAuthentication:
    """Test authentication requirements for dashboard metrics"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    def test_dashboard_metrics_api_requires_authentication(self, client):
        """Test that dashboard metrics endpoint requires authentication"""
        # Act
        response = client.get("/api/v1/dashboard/metrics")

        # Assert
        assert response.status_code == 401

    def test_dashboard_metrics_api_invalid_token_returns_401(self, client):
        """Test that invalid token returns 401"""
        # Arrange
        invalid_headers = {"Authorization": "Bearer invalid-token"}

        # Act
        response = client.get("/api/v1/dashboard/metrics", headers=invalid_headers)

        # Assert
        assert response.status_code == 401

    def test_dashboard_metrics_api_expired_token_returns_401(self, client):
        """Test that expired token returns 401"""
        # Arrange
        expired_headers = {"Authorization": "Bearer expired-token"}

        # Act
        response = client.get("/api/v1/dashboard/metrics", headers=expired_headers)

        # Assert
        assert response.status_code == 401


class TestDashboardMetricsAPIAuthorization:
    """Test authorization levels for dashboard metrics"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    def test_dashboard_metrics_api_read_only_user_access(self, client):
        """Test that read-only users can access basic metrics"""
        with patch('src.cve_feed_service.core.dependencies.require_authentication') as mock_auth:
            with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
                # Arrange
                mock_user = MagicMock()
                mock_user.role.value = "read_only"
                mock_auth.return_value = mock_user
                mock_metrics.return_value = {"total_cves": 100}

                headers = {"Authorization": "Bearer read-only-token"}

                # Act
                response = client.get("/api/v1/dashboard/metrics", headers=headers)

                # Assert
                assert response.status_code == 200

    def test_dashboard_metrics_api_admin_user_gets_extended_metrics(self, client):
        """Test that admin users get extended metrics"""
        with patch('src.cve_feed_service.core.dependencies.require_authentication') as mock_auth:
            with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
                # Arrange
                mock_user = MagicMock()
                mock_user.role.value = "admin"
                mock_auth.return_value = mock_user
                mock_metrics.return_value = {
                    "total_cves": 100,
                    "system_health": {"status": "healthy"},
                    "admin_metrics": {"active_users": 25}
                }

                headers = {"Authorization": "Bearer admin-token"}

                # Act
                response = client.get("/api/v1/dashboard/metrics", headers=headers)
                data = response.json()

                # Assert
                assert response.status_code == 200
                assert "system_health" in data
                assert "admin_metrics" in data


class TestDashboardMetricsAPIErrorHandling:
    """Test error handling for dashboard metrics API"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Authentication headers for API requests"""
        return {"Authorization": "Bearer valid-jwt-token"}

    def test_dashboard_metrics_api_handles_service_errors(self, client, auth_headers):
        """Test that API handles service layer errors gracefully"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.side_effect = Exception("Database connection failed")

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)

            # Assert
            assert response.status_code == 500
            data = response.json()
            assert "detail" in data
            assert "internal server error" in data["detail"].lower()

    def test_dashboard_metrics_api_handles_invalid_date_range(self, client, auth_headers):
        """Test that API handles invalid date range parameters"""
        # Act
        response = client.get(
            "/api/v1/dashboard/metrics?date_range=invalid_range",
            headers=auth_headers
        )

        # Assert
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "invalid date range" in data["detail"].lower()

    def test_dashboard_metrics_api_handles_invalid_application_id(self, client, auth_headers):
        """Test that API handles invalid application ID"""
        # Act
        response = client.get(
            "/api/v1/dashboard/metrics?application_id=invalid-uuid",
            headers=auth_headers
        )

        # Assert
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data


class TestDashboardMetricsAPIPerformance:
    """Test performance aspects of dashboard metrics API"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Authentication headers for API requests"""
        return {"Authorization": "Bearer valid-jwt-token"}

    def test_dashboard_metrics_api_response_time(self, client, auth_headers):
        """Test that dashboard metrics API responds within acceptable time"""
        import time

        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 100}

            # Act
            start_time = time.time()
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)
            end_time = time.time()

            # Assert
            assert response.status_code == 200
            response_time = end_time - start_time
            assert response_time < 2.0  # Should respond within 2 seconds

    def test_dashboard_metrics_api_caching_headers(self, client, auth_headers):
        """Test that dashboard metrics API includes appropriate caching headers"""
        with patch('src.cve_feed_service.services.dashboard_service.DashboardService.get_metrics') as mock_metrics:
            # Arrange
            mock_metrics.return_value = {"total_cves": 100}

            # Act
            response = client.get("/api/v1/dashboard/metrics", headers=auth_headers)

            # Assert
            assert response.status_code == 200
            headers = response.headers
            assert "Cache-Control" in headers
            assert "max-age" in headers["Cache-Control"]
