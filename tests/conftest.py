"""Test configuration and fixtures."""

import asyncio
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
from httpx import AsyncClient

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.main import app


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    # Use in-memory SQLite for unit tests
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
            "isolation_level": None,
        },
        echo=False,  # Set to True for SQL debugging
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest.fixture
async def db_session(test_engine):
    """Create database session for each test with proper isolation."""
    async with AsyncSession(test_engine) as session:
        # Start a transaction
        transaction = await session.begin()

        try:
            yield session
        finally:
            # Always rollback to ensure test isolation
            await transaction.rollback()
            await session.close()


@pytest.fixture
async def async_client(test_engine):
    """Create async test client with test database."""
    from src.cve_feed_service.main import app
    from src.cve_feed_service.db.database import get_db

    # Override the database dependency
    async def get_test_db():
        async with AsyncSession(test_engine) as session:
            yield session

    app.dependency_overrides[get_db] = get_test_db

    try:
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    finally:
        # Clean up dependency override
        app.dependency_overrides.clear()


@pytest.fixture
def sample_application_data():
    """Sample application creation data."""
    return {
        "name": "Test Application",
        "description": "Test application for testing",
        "environment": "test",
        "criticality": "medium",
        "owner": "Test Team"
    }


@pytest.fixture
def sample_component_data():
    """Sample component creation data."""
    return {
        "name": "nginx",
        "version": "1.20.1",
        "vendor": "nginx",
        "component_type": "web_server",
        "description": "Web server component"
    }


@pytest.fixture
def sample_cpe_mapping_data():
    """Sample CPE mapping creation data."""
    return {
        "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
        "confidence": 1.0,
        "mapping_source": "official"
    }


@pytest.fixture
def sample_cve_data():
    """Sample CVE creation data."""
    return {
        "cve_id": "CVE-2023-0001",
        "description": "Test vulnerability description",
        "cvss_v3_score": 7.5,
        "cvss_v3_severity": "HIGH",
        "published_date": "2023-01-01T00:00:00Z",
        "last_modified_date": "2023-01-01T00:00:00Z",
        "source": "NVD",
        "references": ["https://example.com/vuln"],
        "cpe_configurations": ["cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"]
    }


@pytest.fixture
def sample_user_data():
    """Sample user creation data."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "is_active": True,
        "is_superuser": False
    }


@pytest.fixture
def unique_application_data():
    """Generate unique application data for each test."""
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Application {unique_id}",
        "description": f"Test application for testing {unique_id}",
        "environment": "test",
        "criticality": "medium",
        "owner": "Test Team"
    }


@pytest.fixture
def multiple_application_data():
    """Generate multiple unique application data sets."""
    import uuid
    applications = []
    for i in range(5):
        unique_id = str(uuid.uuid4())[:8]
        applications.append({
            "name": f"Test App {i} {unique_id}",
            "description": f"Test application {i} for testing",
            "environment": "test" if i % 2 == 0 else "production",
            "criticality": "medium",
            "owner": f"Team {i}"
        })
    return applications


@pytest.fixture
async def clean_database(db_session):
    """Ensure clean database state for each test."""
    # This fixture runs before each test to ensure clean state
    yield
    # Cleanup is handled by the db_session fixture rollback
