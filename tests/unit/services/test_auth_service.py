"""Tests for Authentication service."""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from src.cve_feed_service.services.auth_service import AuthService
from src.cve_feed_service.schemas.auth import UserCreate, UserUpdate, APIKeyCreate
from src.cve_feed_service.models.user import User, APIKey
from src.cve_feed_service.core.auth import verify_password, get_password_hash


class TestAuthService:
    """Test cases for Authentication service."""

    async def test_create_user_success(self, db_session, sample_user_data):
        """Test successful user creation."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )

        # Act
        result = await service.create_user(user_data)
        await db_session.commit()

        # Assert
        assert result is not None
        assert result.username == sample_user_data["username"]
        assert result.email == sample_user_data["email"]
        assert result.is_active == sample_user_data["is_active"]
        assert result.hashed_password is not None
        assert result.hashed_password != "testpassword123"  # Should be hashed

    async def test_create_user_duplicate_username_raises_error(self, db_session, sample_user_data):
        """Test that duplicate usernames raise ValueError."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )

        # Create first user
        await service.create_user(user_data)
        await db_session.commit()

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_user(user_data)

    async def test_create_user_duplicate_email_raises_error(self, db_session, sample_user_data):
        """Test that duplicate emails raise ValueError."""
        # Arrange
        service = AuthService(db_session)
        
        # Create first user
        user_data1 = UserCreate(
            username="user1",
            password="testpassword123",
            **sample_user_data
        )
        await service.create_user(user_data1)
        await db_session.commit()

        # Try to create second user with same email
        user_data2 = UserCreate(
            username="user2",
            password="testpassword123",
            email=sample_user_data["email"],  # Same email
            full_name="Different User"
        )

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_user(user_data2)

    async def test_get_user_by_username(self, db_session, sample_user_data):
        """Test getting user by username."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.get_user_by_username(created_user.username)

        # Assert
        assert result is not None
        assert result.username == created_user.username
        assert result.email == created_user.email

    async def test_get_user_by_email(self, db_session, sample_user_data):
        """Test getting user by email."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.get_user_by_email(created_user.email)

        # Assert
        assert result is not None
        assert result.email == created_user.email
        assert result.username == created_user.username

    async def test_authenticate_user_success(self, db_session, sample_user_data):
        """Test successful user authentication."""
        # Arrange
        service = AuthService(db_session)
        password = "testpassword123"
        user_data = UserCreate(
            password=password,
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.authenticate_user(created_user.username, password)

        # Assert
        assert result is not None
        assert result.username == created_user.username

    async def test_authenticate_user_wrong_password(self, db_session, sample_user_data):
        """Test authentication with wrong password."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.authenticate_user(created_user.username, "wrongpassword")

        # Assert
        assert result is None

    async def test_authenticate_user_nonexistent(self, db_session):
        """Test authentication with non-existent user."""
        # Arrange
        service = AuthService(db_session)

        # Act
        result = await service.authenticate_user("nonexistent", "password")

        # Assert
        assert result is None

    async def test_authenticate_user_inactive(self, db_session, sample_user_data):
        """Test authentication with inactive user."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            is_active=False,  # Inactive user
            **{k: v for k, v in sample_user_data.items() if k != 'is_active'}
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.authenticate_user(created_user.username, "testpassword123")

        # Assert
        assert result is None

    async def test_update_user_success(self, db_session, sample_user_data):
        """Test successful user update."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        update_data = UserUpdate(
            full_name="Updated Full Name",
            is_active=False
        )

        # Act
        result = await service.update_user(created_user.id, update_data)

        # Assert
        assert result is not None
        assert result.full_name == "Updated Full Name"
        assert result.is_active is False
        assert result.username == created_user.username  # Unchanged

    async def test_update_user_password(self, db_session, sample_user_data):
        """Test updating user password."""
        # Arrange
        service = AuthService(db_session)
        old_password = "oldpassword123"
        new_password = "newpassword456"
        
        user_data = UserCreate(
            password=old_password,
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.change_password(created_user.id, old_password, new_password)

        # Assert
        assert result is True

        # Verify old password no longer works
        auth_result_old = await service.authenticate_user(created_user.username, old_password)
        assert auth_result_old is None

        # Verify new password works
        auth_result_new = await service.authenticate_user(created_user.username, new_password)
        assert auth_result_new is not None

    async def test_change_password_wrong_old_password(self, db_session, sample_user_data):
        """Test changing password with wrong old password."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.change_password(created_user.id, "wrongpassword", "newpassword")

        # Assert
        assert result is False

    async def test_create_api_key_success(self, db_session, sample_user_data):
        """Test successful API key creation."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        api_key_data = APIKeyCreate(
            name="Test API Key",
            description="API key for testing",
            expires_at=datetime.utcnow() + timedelta(days=30)
        )

        # Act
        result = await service.create_api_key(created_user.id, api_key_data)
        await db_session.commit()

        # Assert
        assert result is not None
        assert result.name == "Test API Key"
        assert result.user_id == created_user.id
        assert result.key is not None
        assert len(result.key) > 20  # Should be a proper key
        assert result.is_active is True

    async def test_validate_api_key_success(self, db_session, sample_user_data):
        """Test successful API key validation."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        
        api_key_data = APIKeyCreate(
            name="Test API Key",
            expires_at=datetime.utcnow() + timedelta(days=30)
        )
        api_key = await service.create_api_key(created_user.id, api_key_data)
        await db_session.commit()

        # Act
        result = await service.validate_api_key(api_key.key)

        # Assert
        assert result is not None
        assert result.user_id == created_user.id

    async def test_validate_api_key_expired(self, db_session, sample_user_data):
        """Test validation of expired API key."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        
        api_key_data = APIKeyCreate(
            name="Expired API Key",
            expires_at=datetime.utcnow() - timedelta(days=1)  # Expired
        )
        api_key = await service.create_api_key(created_user.id, api_key_data)
        await db_session.commit()

        # Act
        result = await service.validate_api_key(api_key.key)

        # Assert
        assert result is None

    async def test_validate_api_key_inactive(self, db_session, sample_user_data):
        """Test validation of inactive API key."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        
        api_key_data = APIKeyCreate(
            name="Test API Key",
            expires_at=datetime.utcnow() + timedelta(days=30)
        )
        api_key = await service.create_api_key(created_user.id, api_key_data)
        await db_session.commit()

        # Deactivate the API key
        await service.deactivate_api_key(api_key.id)
        await db_session.commit()

        # Act
        result = await service.validate_api_key(api_key.key)

        # Assert
        assert result is None

    async def test_list_user_api_keys(self, db_session, sample_user_data):
        """Test listing user's API keys."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        
        # Create multiple API keys
        for i in range(3):
            api_key_data = APIKeyCreate(
                name=f"API Key {i}",
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            await service.create_api_key(created_user.id, api_key_data)
        await db_session.commit()

        # Act
        result = await service.list_user_api_keys(created_user.id)

        # Assert
        assert isinstance(result, list)
        assert len(result) == 3
        assert all(key.user_id == created_user.id for key in result)

    async def test_delete_user_success(self, db_session, sample_user_data):
        """Test successful user deletion."""
        # Arrange
        service = AuthService(db_session)
        user_data = UserCreate(
            password="testpassword123",
            **sample_user_data
        )
        created_user = await service.create_user(user_data)
        await db_session.commit()

        # Act
        result = await service.delete_user(created_user.id)

        # Assert
        assert result is True

        # Verify user is soft deleted
        deleted_user = await service.get_user_by_username(created_user.username)
        assert deleted_user is None

    async def test_list_users_pagination(self, db_session):
        """Test user listing with pagination."""
        # Arrange
        service = AuthService(db_session)
        
        # Create multiple users
        created_users = []
        for i in range(5):
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            user_data = UserCreate(
                username=f"testuser{i}_{unique_id}",
                email=f"test{i}_{unique_id}@example.com",
                password="testpassword123",
                full_name=f"Test User {i}"
            )
            user = await service.create_user(user_data)
            created_users.append(user)
        await db_session.commit()

        # Act
        page1 = await service.list_users(skip=0, limit=2)
        page2 = await service.list_users(skip=2, limit=2)
        page3 = await service.list_users(skip=4, limit=2)

        # Assert
        assert len(page1) == 2
        assert len(page2) == 2
        assert len(page3) >= 1  # May have more from other tests

        # Verify our created users are in the results
        all_page_users = page1 + page2 + page3
        created_ids = {user.id for user in created_users}
        page_ids = {user.id for user in all_page_users}
        assert len(created_ids.intersection(page_ids)) == len(created_users)
