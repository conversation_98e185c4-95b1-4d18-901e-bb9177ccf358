"""Tests for CVE service."""

import pytest
from datetime import datetime
from uuid import uuid4

from src.cve_feed_service.services.cve_service import CVEService
from src.cve_feed_service.schemas.cve import CVECreate, CVEUpdate
from src.cve_feed_service.models.cve import CVE


@pytest.mark.asyncio
class TestCVEService:
    """Test cases for CVE service."""

    async def test_create_cve_success(self, db_session, sample_cve_data):
        """Test successful CVE creation."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)

        # Act
        result = await service.create_cve(cve_data)
        await db_session.commit()

        # Assert
        assert result is not None
        assert result.cve_id == sample_cve_data["cve_id"]
        assert result.description == sample_cve_data["description"]
        assert result.cvss_v3_score == sample_cve_data["cvss_v3_score"]

    async def test_create_cve_duplicate_raises_error(self, db_session, sample_cve_data):
        """Test that duplicate CVE IDs raise ValueError."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)

        # Create first CVE
        await service.create_cve(cve_data)
        await db_session.commit()

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_cve(cve_data)

    async def test_get_cve_by_cve_id(self, db_session, sample_cve_data):
        """Test getting CVE by CVE ID."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)
        created_cve = await service.create_cve(cve_data)
        await db_session.commit()

        # Act
        result = await service.get_cve_by_id(created_cve.cve_id)

        # Assert
        assert result is not None
        assert result.cve_id == created_cve.cve_id

    async def test_get_cve_nonexistent_returns_none(self, db_session):
        """Test getting a non-existent CVE returns None."""
        # Arrange
        service = CVEService(db_session)

        # Act
        result = await service.get_cve_by_id("CVE-9999-NONEXISTENT")

        # Assert
        assert result is None

    async def test_list_cves_returns_tuple(self, db_session, sample_cve_data):
        """Test listing CVEs returns a tuple with list and count."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)
        await service.create_cve(cve_data)
        await db_session.commit()

        # Act
        result, total = await service.list_cves()

        # Assert
        assert isinstance(result, list)
        assert isinstance(total, int)
        assert total >= 1
        created_cve = next((cve for cve in result if cve.cve_id == cve_data.cve_id), None)
        assert created_cve is not None

    async def test_list_cves_with_severity_filter(self, db_session):
        """Test listing CVEs with severity filter."""
        # Arrange
        service = CVEService(db_session)
        
        # Create CVEs with different severities
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        high_cve = CVECreate(
            cve_id=f"CVE-2023-HIGH-{unique_id}",
            description="High severity CVE",
            cvss_v3_score=8.5,
            cvss_v3_severity="HIGH",
            published_date=datetime.now(),
            last_modified_date=datetime.now(),
            source="NVD"
        )
        
        medium_cve = CVECreate(
            cve_id=f"CVE-2023-MEDIUM-{unique_id}",
            description="Medium severity CVE",
            cvss_v3_score=5.5,
            cvss_v3_severity="MEDIUM",
            published_date=datetime.now(),
            last_modified_date=datetime.now(),
            source="NVD"
        )

        await service.create_cve(high_cve)
        await service.create_cve(medium_cve)
        await db_session.commit()

        # Act
        high_results = await service.list_cves(severity="HIGH")
        medium_results = await service.list_cves(severity="MEDIUM")

        # Assert
        high_cve_found = any(cve.cve_id == high_cve.cve_id for cve in high_results)
        medium_cve_found = any(cve.cve_id == medium_cve.cve_id for cve in medium_results)
        assert high_cve_found
        assert medium_cve_found

    async def test_update_cve_success(self, db_session, sample_cve_data):
        """Test successful CVE update."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)
        created_cve = await service.create_cve(cve_data)
        await db_session.commit()

        update_data = CVEUpdate(
            description="Updated CVE description",
            cvss_v3_score=9.0
        )

        # Act
        result = await service.update_cve(created_cve.id, update_data)

        # Assert
        assert result is not None
        assert result.description == "Updated CVE description"
        assert result.cvss_v3_score == 9.0
        assert result.cve_id == created_cve.cve_id  # Unchanged

    async def test_update_cve_nonexistent_returns_none(self, db_session):
        """Test updating a non-existent CVE returns None."""
        # Arrange
        service = CVEService(db_session)
        fake_id = uuid4()
        update_data = CVEUpdate(description="Updated")

        # Act
        result = await service.update_cve(fake_id, update_data)

        # Assert
        assert result is None

    async def test_delete_cve_success(self, db_session, sample_cve_data):
        """Test successful CVE deletion."""
        # Arrange
        service = CVEService(db_session)
        cve_data = CVECreate(**sample_cve_data)
        created_cve = await service.create_cve(cve_data)
        await db_session.commit()

        # Act
        result = await service.delete_cve(created_cve.id)

        # Assert
        assert result is True

        # Verify CVE is soft deleted
        deleted_cve = await service.get_cve(created_cve.id)
        assert deleted_cve is None

    async def test_delete_cve_nonexistent_returns_false(self, db_session):
        """Test deleting a non-existent CVE returns False."""
        # Arrange
        service = CVEService(db_session)
        fake_id = uuid4()

        # Act
        result = await service.delete_cve(fake_id)

        # Assert
        assert result is False

    async def test_list_cves_pagination(self, db_session):
        """Test CVE listing with pagination."""
        # Arrange
        service = CVEService(db_session)
        
        # Create multiple CVEs
        created_cves = []
        for i in range(5):
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            cve_data = CVECreate(
                cve_id=f"CVE-2023-{i:04d}-{unique_id}",
                description=f"Test CVE {i}",
                cvss_v3_score=5.0 + i,
                cvss_v3_severity="MEDIUM",
                published_date=datetime.now(),
                last_modified_date=datetime.now(),
                source="NVD"
            )
            cve = await service.create_cve(cve_data)
            created_cves.append(cve)
        await db_session.commit()

        # Act
        page1 = await service.list_cves(skip=0, limit=2)
        page2 = await service.list_cves(skip=2, limit=2)
        page3 = await service.list_cves(skip=4, limit=2)

        # Assert
        assert len(page1) == 2
        assert len(page2) == 2
        assert len(page3) >= 1  # May have more from other tests

        # Verify our created CVEs are in the results
        all_page_cves = page1 + page2 + page3
        created_ids = {cve.id for cve in created_cves}
        page_ids = {cve.id for cve in all_page_cves}
        assert len(created_ids.intersection(page_ids)) == len(created_cves)

    async def test_search_cves_by_description(self, db_session):
        """Test searching CVEs by description."""
        # Arrange
        service = CVEService(db_session)
        
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        searchable_cve = CVECreate(
            cve_id=f"CVE-2023-SEARCH-{unique_id}",
            description="Remote code execution vulnerability in nginx",
            cvss_v3_score=9.8,
            cvss_v3_severity="CRITICAL",
            published_date=datetime.now(),
            last_modified_date=datetime.now(),
            source="NVD"
        )

        await service.create_cve(searchable_cve)
        await db_session.commit()

        # Act
        results = await service.search_cves("remote code execution")

        # Assert
        found_cve = any(cve.cve_id == searchable_cve.cve_id for cve in results)
        assert found_cve

    async def test_get_cves_by_date_range(self, db_session):
        """Test getting CVEs by date range."""
        # Arrange
        service = CVEService(db_session)
        
        import uuid
        from datetime import datetime, timedelta
        
        unique_id = str(uuid.uuid4())[:8]
        base_date = datetime.now()
        
        old_cve = CVECreate(
            cve_id=f"CVE-2022-OLD-{unique_id}",
            description="Old CVE",
            cvss_v3_score=5.0,
            cvss_v3_severity="MEDIUM",
            published_date=base_date - timedelta(days=365),
            last_modified_date=base_date - timedelta(days=365),
            source="NVD"
        )
        
        recent_cve = CVECreate(
            cve_id=f"CVE-2023-RECENT-{unique_id}",
            description="Recent CVE",
            cvss_v3_score=7.0,
            cvss_v3_severity="HIGH",
            published_date=base_date - timedelta(days=30),
            last_modified_date=base_date - timedelta(days=30),
            source="NVD"
        )

        await service.create_cve(old_cve)
        await service.create_cve(recent_cve)
        await db_session.commit()

        # Act
        recent_results = await service.get_cves_by_date_range(
            start_date=base_date - timedelta(days=60),
            end_date=base_date
        )

        # Assert
        recent_cve_found = any(cve.cve_id == recent_cve.cve_id for cve in recent_results)
        old_cve_found = any(cve.cve_id == old_cve.cve_id for cve in recent_results)
        assert recent_cve_found
        assert not old_cve_found
