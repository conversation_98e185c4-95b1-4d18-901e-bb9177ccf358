"""Tests for Component service."""

import pytest
from uuid import uuid4

from src.cve_feed_service.services.component_service import ComponentService
from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.component import ComponentC<PERSON>, ComponentUpdate, CPEMappingCreate
from src.cve_feed_service.schemas.application import ApplicationCreate
from src.cve_feed_service.models.component import Component, CPEMapping


class TestComponentService:
    """Test cases for Component service."""

    @pytest.fixture
    async def test_application(self, db_session):
        """Create a test application for component tests."""
        app_service = ApplicationService(db_session)
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        app_data = ApplicationCreate(
            name=f"Test App {unique_id}",
            description="Test application for component tests",
            environment="test"
        )
        app = await app_service.create_application(app_data)
        await db_session.commit()
        return app

    async def test_create_component_success(self, db_session, test_application, sample_component_data):
        """Test successful component creation."""
        # Arrange
        service = ComponentService(db_session)
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )

        # Act
        result = await service.create_component(component_data)
        await db_session.commit()

        # Assert
        assert result is not None
        assert result.name == sample_component_data["name"]
        assert result.version == sample_component_data["version"]
        assert result.application_id == test_application.id

    async def test_create_component_duplicate_raises_error(self, db_session, test_application, sample_component_data):
        """Test that duplicate components raise ValueError."""
        # Arrange
        service = ComponentService(db_session)
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )

        # Create first component
        await service.create_component(component_data)
        await db_session.commit()

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_component(component_data)

    async def test_get_component_existing_returns_component(self, db_session, test_application, sample_component_data):
        """Test getting an existing component."""
        # Arrange
        service = ComponentService(db_session)
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        created_component = await service.create_component(component_data)
        await db_session.commit()

        # Act
        result = await service.get_component(created_component.id)

        # Assert
        assert result is not None
        assert result.id == created_component.id
        assert result.name == created_component.name

    async def test_get_component_nonexistent_returns_none(self, db_session):
        """Test getting a non-existent component returns None."""
        # Arrange
        service = ComponentService(db_session)
        fake_id = uuid4()

        # Act
        result = await service.get_component(fake_id)

        # Assert
        assert result is None

    async def test_list_components_by_application(self, db_session, test_application):
        """Test listing components by application."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create multiple components for the application
        components_data = [
            {"name": "nginx", "version": "1.20.1", "component_type": "web_server"},
            {"name": "nodejs", "version": "16.14.0", "component_type": "runtime"},
            {"name": "express", "version": "4.18.1", "component_type": "framework"}
        ]
        
        created_components = []
        for comp_data in components_data:
            component = await service.create_component(ComponentCreate(
                application_id=test_application.id,
                **comp_data
            ))
            created_components.append(component)
        await db_session.commit()

        # Act
        result = await service.list_components_by_application(test_application.id)

        # Assert
        assert isinstance(result, list)
        assert len(result) == 3
        
        # Verify all created components are in the result
        result_names = {comp.name for comp in result}
        expected_names = {comp_data["name"] for comp_data in components_data}
        assert result_names == expected_names

    async def test_update_component_success(self, db_session, test_application, sample_component_data):
        """Test successful component update."""
        # Arrange
        service = ComponentService(db_session)
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        created_component = await service.create_component(component_data)
        await db_session.commit()

        update_data = ComponentUpdate(
            version="1.21.0",
            description="Updated component description"
        )

        # Act
        result = await service.update_component(created_component.id, update_data)

        # Assert
        assert result is not None
        assert result.version == "1.21.0"
        assert result.description == "Updated component description"
        assert result.name == created_component.name  # Unchanged

    async def test_update_component_nonexistent_returns_none(self, db_session):
        """Test updating a non-existent component returns None."""
        # Arrange
        service = ComponentService(db_session)
        fake_id = uuid4()
        update_data = ComponentUpdate(version="1.0.0")

        # Act
        result = await service.update_component(fake_id, update_data)

        # Assert
        assert result is None

    async def test_delete_component_success(self, db_session, test_application, sample_component_data):
        """Test successful component deletion."""
        # Arrange
        service = ComponentService(db_session)
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        created_component = await service.create_component(component_data)
        await db_session.commit()

        # Act
        result = await service.delete_component(created_component.id)

        # Assert
        assert result is True

        # Verify component is soft deleted
        deleted_component = await service.get_component(created_component.id)
        assert deleted_component is None

    async def test_delete_component_nonexistent_returns_false(self, db_session):
        """Test deleting a non-existent component returns False."""
        # Arrange
        service = ComponentService(db_session)
        fake_id = uuid4()

        # Act
        result = await service.delete_component(fake_id)

        # Assert
        assert result is False

    async def test_create_cpe_mapping_success(self, db_session, test_application, sample_component_data, sample_cpe_mapping_data):
        """Test successful CPE mapping creation."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create component first
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        component = await service.create_component(component_data)
        await db_session.commit()

        # Create CPE mapping
        cpe_data = CPEMappingCreate(
            component_id=component.id,
            **sample_cpe_mapping_data
        )

        # Act
        result = await service.create_cpe_mapping(cpe_data)
        await db_session.commit()

        # Assert
        assert result is not None
        assert result.component_id == component.id
        assert result.cpe_string == sample_cpe_mapping_data["cpe_string"]
        assert result.confidence == sample_cpe_mapping_data["confidence"]

    async def test_create_cpe_mapping_duplicate_raises_error(self, db_session, test_application, sample_component_data, sample_cpe_mapping_data):
        """Test that duplicate CPE mappings raise ValueError."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create component first
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        component = await service.create_component(component_data)
        await db_session.commit()

        # Create CPE mapping
        cpe_data = CPEMappingCreate(
            component_id=component.id,
            **sample_cpe_mapping_data
        )

        # Create first mapping
        await service.create_cpe_mapping(cpe_data)
        await db_session.commit()

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_cpe_mapping(cpe_data)

    async def test_list_cpe_mappings_by_component(self, db_session, test_application, sample_component_data):
        """Test listing CPE mappings by component."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create component
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        component = await service.create_component(component_data)
        await db_session.commit()

        # Create multiple CPE mappings
        cpe_mappings_data = [
            {"cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*", "confidence": 1.0},
            {"cpe_string": "cpe:2.3:a:nginx:nginx:1.20.*:*:*:*:*:*:*:*", "confidence": 0.8},
        ]
        
        for cpe_data in cpe_mappings_data:
            await service.create_cpe_mapping(CPEMappingCreate(
                component_id=component.id,
                mapping_source="manual",
                **cpe_data
            ))
        await db_session.commit()

        # Act
        result = await service.list_cpe_mappings_by_component(component.id)

        # Assert
        assert isinstance(result, list)
        assert len(result) == 2
        
        # Verify all created mappings are in the result
        result_cpes = {mapping.cpe_string for mapping in result}
        expected_cpes = {cpe_data["cpe_string"] for cpe_data in cpe_mappings_data}
        assert result_cpes == expected_cpes

    async def test_update_cpe_mapping_success(self, db_session, test_application, sample_component_data, sample_cpe_mapping_data):
        """Test successful CPE mapping update."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create component and CPE mapping
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        component = await service.create_component(component_data)
        
        cpe_data = CPEMappingCreate(
            component_id=component.id,
            **sample_cpe_mapping_data
        )
        cpe_mapping = await service.create_cpe_mapping(cpe_data)
        await db_session.commit()

        # Act
        result = await service.update_cpe_mapping(cpe_mapping.id, confidence=0.9)

        # Assert
        assert result is not None
        assert result.confidence == 0.9
        assert result.cpe_string == cpe_mapping.cpe_string  # Unchanged

    async def test_delete_cpe_mapping_success(self, db_session, test_application, sample_component_data, sample_cpe_mapping_data):
        """Test successful CPE mapping deletion."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create component and CPE mapping
        component_data = ComponentCreate(
            application_id=test_application.id,
            **sample_component_data
        )
        component = await service.create_component(component_data)
        
        cpe_data = CPEMappingCreate(
            component_id=component.id,
            **sample_cpe_mapping_data
        )
        cpe_mapping = await service.create_cpe_mapping(cpe_data)
        await db_session.commit()

        # Act
        result = await service.delete_cpe_mapping(cpe_mapping.id)

        # Assert
        assert result is True

        # Verify mapping is deleted
        mappings = await service.list_cpe_mappings_by_component(component.id)
        assert len(mappings) == 0

    async def test_search_components_by_name_and_version(self, db_session, test_application):
        """Test searching components by name and version."""
        # Arrange
        service = ComponentService(db_session)
        
        # Create components with different names and versions
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        
        components_data = [
            {"name": f"nginx-{unique_id}", "version": "1.20.1", "component_type": "web_server"},
            {"name": f"nginx-{unique_id}", "version": "1.21.0", "component_type": "web_server"},
            {"name": f"apache-{unique_id}", "version": "2.4.51", "component_type": "web_server"},
        ]
        
        for comp_data in components_data:
            await service.create_component(ComponentCreate(
                application_id=test_application.id,
                **comp_data
            ))
        await db_session.commit()

        # Act
        nginx_results = await service.search_components(name=f"nginx-{unique_id}")
        version_results = await service.search_components(version="1.20.1")

        # Assert
        nginx_count = len([comp for comp in nginx_results if f"nginx-{unique_id}" in comp.name])
        version_count = len([comp for comp in version_results if comp.version == "1.20.1"])
        
        assert nginx_count == 2  # Two nginx components
        assert version_count >= 1  # At least one component with version 1.20.1
