"""Unit tests for ApplicationService."""

import pytest
from uuid import uuid4

from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate


@pytest.mark.unit
class TestApplicationService:
    """Unit tests for ApplicationService."""

    async def test_create_application_success(self, db_session, sample_application_data):
        """Test successful application creation."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**sample_application_data)
        
        # Act
        result = await service.create_application(app_data)
        
        # Assert
        assert result.name == app_data.name
        assert result.environment == app_data.environment
        assert result.criticality == app_data.criticality
        assert result.id is not None
        assert result.created_at is not None

    async def test_create_application_duplicate_name_raises_error(self, db_session, unique_application_data):
        """Test that duplicate application names raise ValueError."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**unique_application_data)

        # Create first application
        await service.create_application(app_data)
        await db_session.commit()

        # Act & Assert
        with pytest.raises(ValueError, match="already exists"):
            await service.create_application(app_data)

    async def test_get_application_existing_returns_application(self, db_session, unique_application_data):
        """Test getting an existing application."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**unique_application_data)
        created_app = await service.create_application(app_data)
        await db_session.commit()

        # Act
        result = await service.get_application(created_app.id)

        # Assert
        assert result is not None
        assert result.id == created_app.id
        assert result.name == created_app.name

    async def test_get_application_nonexistent_returns_none(self, db_session):
        """Test getting a non-existent application returns None."""
        # Arrange
        service = ApplicationService(db_session)
        fake_id = uuid4()
        
        # Act
        result = await service.get_application(fake_id)
        
        # Assert
        assert result is None

    async def test_list_applications_returns_list(self, db_session, unique_application_data):
        """Test listing applications returns a list."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**unique_application_data)
        await service.create_application(app_data)
        await db_session.commit()

        # Act
        result = await service.list_applications()

        # Assert
        assert isinstance(result, list)
        assert len(result) >= 1
        created_app = next((app for app in result if app.name == app_data.name), None)
        assert created_app is not None

    async def test_list_applications_with_environment_filter(self, db_session):
        """Test listing applications with environment filter."""
        # Arrange
        service = ApplicationService(db_session)

        # Create applications in different environments with unique names
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        prod_app = ApplicationCreate(name=f"Prod App {unique_id}", environment="production")
        test_app = ApplicationCreate(name=f"Test App {unique_id}", environment="test")

        await service.create_application(prod_app)
        await service.create_application(test_app)
        await db_session.commit()

        # Act
        prod_results = await service.list_applications(environment="production")
        test_results = await service.list_applications(environment="test")

        # Assert
        prod_app_found = any(app.name == prod_app.name for app in prod_results)
        test_app_found = any(app.name == test_app.name for app in test_results)
        assert prod_app_found
        assert test_app_found

    async def test_update_application_success(self, db_session, unique_application_data):
        """Test successful application update."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**unique_application_data)
        created_app = await service.create_application(app_data)
        await db_session.commit()

        update_data = ApplicationUpdate(
            description="Updated description",
            criticality="high"
        )

        # Act
        result = await service.update_application(created_app.id, update_data)

        # Assert
        assert result is not None
        assert result.description == "Updated description"
        assert result.criticality == "high"
        assert result.name == created_app.name  # Unchanged

    async def test_update_application_nonexistent_returns_none(self, db_session):
        """Test updating a non-existent application returns None."""
        # Arrange
        service = ApplicationService(db_session)
        fake_id = uuid4()
        update_data = ApplicationUpdate(description="Updated")
        
        # Act
        result = await service.update_application(fake_id, update_data)
        
        # Assert
        assert result is None

    async def test_delete_application_success(self, db_session, unique_application_data):
        """Test successful application deletion."""
        # Arrange
        service = ApplicationService(db_session)
        app_data = ApplicationCreate(**unique_application_data)
        created_app = await service.create_application(app_data)
        await db_session.commit()

        # Act
        result = await service.delete_application(created_app.id)

        # Assert
        assert result is True

        # Verify application is soft deleted
        deleted_app = await service.get_application(created_app.id)
        assert deleted_app is None

    async def test_delete_application_nonexistent_returns_false(self, db_session):
        """Test deleting a non-existent application returns False."""
        # Arrange
        service = ApplicationService(db_session)
        fake_id = uuid4()
        
        # Act
        result = await service.delete_application(fake_id)
        
        # Assert
        assert result is False

    async def test_list_applications_pagination(self, db_session, multiple_application_data):
        """Test application listing with pagination."""
        # Arrange
        service = ApplicationService(db_session)

        # Create multiple applications with unique names
        created_apps = []
        for app_data in multiple_application_data:
            app = await service.create_application(ApplicationCreate(**app_data))
            created_apps.append(app)
        await db_session.commit()

        # Act
        page1 = await service.list_applications(skip=0, limit=2)
        page2 = await service.list_applications(skip=2, limit=2)
        page3 = await service.list_applications(skip=4, limit=2)

        # Assert
        assert len(page1) == 2
        assert len(page2) == 2
        assert len(page3) >= 1  # May have more from other tests

        # Verify no duplicates in our created apps
        created_ids = {app.id for app in created_apps}
        page_ids = {app.id for app in page1 + page2 + page3}
        assert len(created_ids.intersection(page_ids)) == len(created_apps)

    async def test_create_application_different_environments_allowed(self, db_session):
        """Test that same name in different environments is allowed."""
        # Arrange
        service = ApplicationService(db_session)

        import uuid
        unique_name = f"Same Name {str(uuid.uuid4())[:8]}"
        prod_app = ApplicationCreate(name=unique_name, environment="production")
        test_app = ApplicationCreate(name=unique_name, environment="test")

        # Act
        prod_result = await service.create_application(prod_app)
        test_result = await service.create_application(test_app)
        await db_session.commit()

        # Assert
        assert prod_result.name == test_result.name
        assert prod_result.environment != test_result.environment
        assert prod_result.id != test_result.id
