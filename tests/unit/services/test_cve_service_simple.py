"""Simple tests for CVEService without complex database setup."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4
from datetime import datetime

from src.cve_feed_service.services.cve_service import CVEService
from src.cve_feed_service.schemas.cve import CVECreate
from src.cve_feed_service.models.cve import CVE


class TestCVEServiceSimple:
    """Simple tests for CVEService focusing on business logic."""

    def _get_sample_cve_data(self):
        """Helper method to get sample CVE data."""
        unique_id = str(uuid4())[:8]
        return {
            "cve_id": f"CVE-2024-{unique_id}",
            "description": f"Test CVE description {unique_id}",
            "published_date": datetime.utcnow(),
            "last_modified_date": datetime.utcnow(),
            "cvss_v3_score": 7.5,
            "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N",
            "cvss_v3_severity": "HIGH"
        }

    @pytest.mark.asyncio
    async def test_create_cve_calls_db_correctly(self):
        """Test that create_cve calls database methods correctly."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        sample_data = self._get_sample_cve_data()
        cve_data = CVECreate(**sample_data)
        
        # Mock the database response - no existing CVE
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Mock add and commit
        mock_session.add = MagicMock()
        mock_session.commit = AsyncMock()
        
        # Act
        result = await service.create_cve(cve_data)
        
        # Assert
        assert mock_session.execute.called
        assert mock_session.add.called
        assert result is not None

    @pytest.mark.asyncio
    async def test_get_cve_by_id_existing_cve(self):
        """Test getting an existing CVE by ID."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        sample_data = self._get_sample_cve_data()
        mock_cve = CVE(
            id=uuid4(),
            cve_id=sample_data["cve_id"],
            description=sample_data["description"],
            published_date=sample_data["published_date"],
            cvss_v3_score=sample_data["cvss_v3_score"],
            cvss_v3_severity=sample_data["cvss_v3_severity"]
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_cve
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.get_cve_by_id(sample_data["cve_id"])
        
        # Assert
        assert result is not None
        assert result.cve_id == sample_data["cve_id"]
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_cve_by_id_nonexistent_cve(self):
        """Test getting a non-existent CVE by ID."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        # Mock database query to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.get_cve_by_id("CVE-2024-NONEXISTENT")
        
        # Assert
        assert result is None
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_list_cves_returns_tuple(self):
        """Test that list_cves returns a tuple of (cves, total)."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        # Mock CVEs
        sample_data1 = self._get_sample_cve_data()
        sample_data2 = self._get_sample_cve_data()
        
        mock_cves = [
            CVE(id=uuid4(), cve_id=sample_data1["cve_id"], description=sample_data1["description"]),
            CVE(id=uuid4(), cve_id=sample_data2["cve_id"], description=sample_data2["description"])
        ]
        
        # Mock database queries
        # First call for count
        mock_count_result = MagicMock()
        mock_count_result.scalar.return_value = 2
        
        # Second call for actual CVEs
        mock_cves_result = MagicMock()
        mock_cves_result.scalars.return_value.all.return_value = mock_cves
        
        mock_session.execute.side_effect = [mock_count_result, mock_cves_result]
        
        # Act
        result = await service.list_cves()
        
        # Assert
        assert isinstance(result, tuple)
        assert len(result) == 2
        cves, total = result
        assert isinstance(cves, list)
        assert isinstance(total, int)
        assert len(cves) == 2
        assert total == 2
        assert mock_session.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_update_cve_existing_cve(self):
        """Test updating an existing CVE."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        sample_data = self._get_sample_cve_data()
        mock_cve = CVE(
            id=uuid4(),
            cve_id=sample_data["cve_id"],
            description=sample_data["description"],
            cvss_v3_score=sample_data["cvss_v3_score"]
        )
        
        # Mock database query to return existing CVE
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_cve
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()
        
        # Act
        update_data = {"description": "Updated description", "cvss_v3_score": 9.0}
        result = await service.update_cve(sample_data["cve_id"], update_data)
        
        # Assert
        assert result is not None
        assert mock_session.execute.called
        assert mock_session.commit.called

    @pytest.mark.asyncio
    async def test_update_cve_nonexistent_cve(self):
        """Test updating a non-existent CVE returns None."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        # Mock database query to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        update_data = {"description": "Updated description"}
        result = await service.update_cve("CVE-2024-NONEXISTENT", update_data)
        
        # Assert
        assert result is None
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_delete_cve_existing_cve(self):
        """Test deleting an existing CVE."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        sample_data = self._get_sample_cve_data()
        mock_cve = CVE(
            id=uuid4(),
            cve_id=sample_data["cve_id"],
            description=sample_data["description"]
        )
        
        # Mock database query to return existing CVE
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_cve
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()
        
        # Act
        result = await service.delete_cve(sample_data["cve_id"])
        
        # Assert
        assert result is True
        assert mock_session.execute.called
        assert mock_session.commit.called

    @pytest.mark.asyncio
    async def test_delete_cve_nonexistent_cve(self):
        """Test deleting a non-existent CVE returns False."""
        # Arrange
        mock_session = AsyncMock()
        service = CVEService(mock_session)
        
        # Mock database query to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.delete_cve("CVE-2024-NONEXISTENT")
        
        # Assert
        assert result is False
        assert mock_session.execute.called
