"""Simple tests for AuthService without complex database setup."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4
from datetime import datetime, timedelta

from src.cve_feed_service.services.auth_service import AuthService
from src.cve_feed_service.schemas.auth import UserCreate, UserUpdate
from src.cve_feed_service.models.user import User


class TestAuthServiceSimple:
    """Simple tests for AuthService focusing on business logic."""

    def _get_sample_user_data(self):
        """Helper method to get sample user data."""
        unique_id = str(uuid4())[:8]
        return {
            "username": f"testuser_{unique_id}",
            "email": f"test_{unique_id}@example.com",
            "full_name": "Test User",
            "role": "security_analyst",
            "is_active": True,
            "is_superuser": False
        }

    @pytest.mark.asyncio
    async def test_create_user_calls_db_correctly(self):
        """Test that create_user calls database methods correctly."""
        # Arrange
        mock_session = AsyncMock()
        service = AuthService(mock_session)
        
        sample_data = self._get_sample_user_data()
        user_data = UserCreate(password="testpassword123", **sample_data)
        
        # Mock the database response
        mock_user = User(
            id=uuid4(),
            username=sample_data["username"],
            email=sample_data["email"],
            full_name=sample_data["full_name"],
            role=sample_data["role"],
            is_active=sample_data["is_active"],
            hashed_password="hashed_password_here",
            created_at=datetime.utcnow()
        )
        
        # Mock the execute method to return our mock user
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None  # No existing user
        mock_session.execute.return_value = mock_result
        
        # Mock add and commit
        mock_session.add = MagicMock()
        mock_session.commit = AsyncMock()
        
        # Act
        result = await service.create_user(user_data)
        
        # Assert
        assert mock_session.execute.called
        assert mock_session.add.called
        # The service should return a user object (even if mocked)
        assert result is not None

    @pytest.mark.asyncio
    async def test_authenticate_user_with_valid_credentials(self):
        """Test authenticate_user with valid credentials."""
        # Arrange
        mock_session = AsyncMock()
        service = AuthService(mock_session)
        
        # Mock user with hashed password
        from src.cve_feed_service.core.auth import get_password_hash
        password = "testpassword123"
        hashed_password = get_password_hash(password)
        
        mock_user = User(
            id=uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            role="security_analyst"
        )
        
        # Mock database query to return our user
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.authenticate_user("testuser", password)
        
        # Assert
        assert result is not None
        assert result.username == "testuser"
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_authenticate_user_with_invalid_password(self):
        """Test authenticate_user with invalid password."""
        # Arrange
        mock_session = AsyncMock()
        service = AuthService(mock_session)
        
        # Mock user with different hashed password
        from src.cve_feed_service.core.auth import get_password_hash
        correct_password = "correctpassword"
        hashed_password = get_password_hash(correct_password)
        
        mock_user = User(
            id=uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            role="security_analyst"
        )
        
        # Mock database query to return our user
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.authenticate_user("testuser", "wrongpassword")
        
        # Assert
        assert result is None  # Should return None for invalid password
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_user_by_username_existing_user(self):
        """Test getting an existing user by username."""
        # Arrange
        mock_session = AsyncMock()
        service = AuthService(mock_session)
        
        mock_user = User(
            id=uuid4(),
            username="testuser",
            email="<EMAIL>",
            role="security_analyst"
        )
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.get_user_by_username("testuser")
        
        # Assert
        assert result is not None
        assert result.username == "testuser"
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_user_by_username_nonexistent_user(self):
        """Test getting a non-existent user by username."""
        # Arrange
        mock_session = AsyncMock()
        service = AuthService(mock_session)
        
        # Mock database query to return None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Act
        result = await service.get_user_by_username("nonexistent")
        
        # Assert
        assert result is None
        assert mock_session.execute.called
