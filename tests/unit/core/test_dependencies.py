"""Tests for dependency injection utilities."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from fastapi import HTT<PERSON><PERSON>xception
from sqlalchemy.ext.asyncio import AsyncSession

from src.cve_feed_service.core.dependencies import (
    get_db,
    get_current_user_dependency,
    get_current_active_user_dependency,
    get_settings,
    verify_api_key
)
from src.cve_feed_service.core.config import Settings


class TestDatabaseDependency:
    """Test database dependency injection."""

    @pytest.mark.asyncio
    async def test_get_db_basic(self):
        """Test basic database dependency."""
        # Mock the async session
        mock_session = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.AsyncSessionLocal') as mock_session_local:
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session_local.return_value.__aexit__.return_value = None
            
            # Test the dependency
            async for db in get_db():
                assert db == mock_session
                break

    @pytest.mark.asyncio
    async def test_get_db_context_manager(self):
        """Test database dependency as context manager."""
        mock_session = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.AsyncSessionLocal') as mock_session_local:
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session_local.return_value.__aexit__.return_value = None
            
            db_generator = get_db()
            db = await db_generator.__anext__()
            
            assert db == mock_session
            
            # Should handle cleanup
            try:
                await db_generator.__anext__()
            except StopAsyncIteration:
                pass

    @pytest.mark.asyncio
    async def test_get_db_error_handling(self):
        """Test database dependency error handling."""
        with patch('src.cve_feed_service.core.dependencies.AsyncSessionLocal') as mock_session_local:
            mock_session_local.side_effect = Exception("DB Connection Error")
            
            try:
                async for db in get_db():
                    pass
            except Exception as e:
                assert "DB Connection Error" in str(e)

    @pytest.mark.asyncio
    async def test_get_db_multiple_calls(self):
        """Test multiple calls to database dependency."""
        mock_session1 = MagicMock(spec=AsyncSession)
        mock_session2 = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.AsyncSessionLocal') as mock_session_local:
            mock_session_local.return_value.__aenter__.side_effect = [mock_session1, mock_session2]
            mock_session_local.return_value.__aexit__.return_value = None
            
            # First call
            async for db1 in get_db():
                assert db1 == mock_session1
                break
            
            # Second call
            async for db2 in get_db():
                assert db2 == mock_session2
                break


class TestUserDependencies:
    """Test user authentication dependencies."""

    @pytest.mark.asyncio
    async def test_get_current_user_dependency_valid_token(self):
        """Test current user dependency with valid token."""
        mock_user = MagicMock()
        mock_user.username = "testuser"
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_current_user') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            dependency = get_current_user_dependency()
            user = await dependency("valid_token", mock_db)
            
            assert user == mock_user
            mock_get_user.assert_called_once_with("valid_token", mock_db)

    @pytest.mark.asyncio
    async def test_get_current_user_dependency_invalid_token(self):
        """Test current user dependency with invalid token."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_current_user') as mock_get_user:
            mock_get_user.side_effect = HTTPException(status_code=401, detail="Invalid token")
            
            dependency = get_current_user_dependency()
            
            with pytest.raises(HTTPException) as exc_info:
                await dependency("invalid_token", mock_db)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_dependency_no_token(self):
        """Test current user dependency with no token."""
        mock_db = MagicMock(spec=AsyncSession)
        
        dependency = get_current_user_dependency()
        
        with pytest.raises(HTTPException) as exc_info:
            await dependency(None, mock_db)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_active_user_dependency_active(self):
        """Test current active user dependency with active user."""
        mock_user = MagicMock()
        mock_user.is_active = True
        
        with patch('src.cve_feed_service.core.dependencies.get_current_active_user') as mock_get_active:
            mock_get_active.return_value = mock_user
            
            dependency = get_current_active_user_dependency()
            user = await dependency(mock_user)
            
            assert user == mock_user
            mock_get_active.assert_called_once_with(mock_user)

    @pytest.mark.asyncio
    async def test_get_current_active_user_dependency_inactive(self):
        """Test current active user dependency with inactive user."""
        mock_user = MagicMock()
        mock_user.is_active = False
        
        with patch('src.cve_feed_service.core.dependencies.get_current_active_user') as mock_get_active:
            mock_get_active.side_effect = HTTPException(status_code=400, detail="Inactive user")
            
            dependency = get_current_active_user_dependency()
            
            with pytest.raises(HTTPException) as exc_info:
                await dependency(mock_user)
            
            assert exc_info.value.status_code == 400

    @pytest.mark.asyncio
    async def test_get_current_active_user_dependency_none_user(self):
        """Test current active user dependency with None user."""
        dependency = get_current_active_user_dependency()
        
        with pytest.raises(HTTPException) as exc_info:
            await dependency(None)
        
        assert exc_info.value.status_code == 401


class TestSettingsDependency:
    """Test settings dependency injection."""

    def test_get_settings_basic(self):
        """Test basic settings dependency."""
        settings = get_settings()
        
        assert isinstance(settings, Settings)
        assert hasattr(settings, 'app_name')
        assert hasattr(settings, 'debug')

    def test_get_settings_caching(self):
        """Test settings dependency caching."""
        settings1 = get_settings()
        settings2 = get_settings()
        
        # Should return the same instance (cached)
        assert settings1 is settings2

    def test_get_settings_properties(self):
        """Test settings dependency properties."""
        settings = get_settings()
        
        # Check that essential properties exist
        essential_properties = [
            'app_name',
            'debug',
            'secret_key',
            'algorithm',
            'access_token_expire_minutes'
        ]
        
        for prop in essential_properties:
            assert hasattr(settings, prop)

    @patch.dict('os.environ', {'APP_NAME': 'Test App'})
    def test_get_settings_environment_override(self):
        """Test settings dependency with environment override."""
        # Clear the cache first
        get_settings.cache_clear()
        
        settings = get_settings()
        
        # Should pick up environment variable
        assert settings.app_name == 'Test App'

    def test_get_settings_validation(self):
        """Test settings dependency validation."""
        settings = get_settings()
        
        # Check that settings are valid
        assert isinstance(settings.debug, bool)
        assert isinstance(settings.access_token_expire_minutes, int)
        assert settings.access_token_expire_minutes > 0


class TestAPIKeyDependency:
    """Test API key verification dependency."""

    @pytest.mark.asyncio
    async def test_verify_api_key_valid(self):
        """Test API key verification with valid key."""
        mock_api_key = MagicMock()
        mock_api_key.is_active = True
        mock_api_key.expires_at = None
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_api_key_by_key') as mock_get_key:
            mock_get_key.return_value = mock_api_key
            
            result = await verify_api_key("valid_api_key", mock_db)
            
            assert result == mock_api_key
            mock_get_key.assert_called_once_with(mock_db, "valid_api_key")

    @pytest.mark.asyncio
    async def test_verify_api_key_invalid(self):
        """Test API key verification with invalid key."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_api_key_by_key') as mock_get_key:
            mock_get_key.return_value = None
            
            with pytest.raises(HTTPException) as exc_info:
                await verify_api_key("invalid_api_key", mock_db)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_verify_api_key_inactive(self):
        """Test API key verification with inactive key."""
        mock_api_key = MagicMock()
        mock_api_key.is_active = False
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_api_key_by_key') as mock_get_key:
            mock_get_key.return_value = mock_api_key
            
            with pytest.raises(HTTPException) as exc_info:
                await verify_api_key("inactive_api_key", mock_db)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_verify_api_key_expired(self):
        """Test API key verification with expired key."""
        from datetime import datetime, timedelta
        
        mock_api_key = MagicMock()
        mock_api_key.is_active = True
        mock_api_key.expires_at = datetime.utcnow() - timedelta(days=1)  # Expired
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_api_key_by_key') as mock_get_key:
            mock_get_key.return_value = mock_api_key
            
            with pytest.raises(HTTPException) as exc_info:
                await verify_api_key("expired_api_key", mock_db)
            
            assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_verify_api_key_none_key(self):
        """Test API key verification with None key."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with pytest.raises(HTTPException) as exc_info:
            await verify_api_key(None, mock_db)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_verify_api_key_empty_key(self):
        """Test API key verification with empty key."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with pytest.raises(HTTPException) as exc_info:
            await verify_api_key("", mock_db)
        
        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_verify_api_key_database_error(self):
        """Test API key verification with database error."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_api_key_by_key') as mock_get_key:
            mock_get_key.side_effect = Exception("Database error")
            
            with pytest.raises(Exception):
                await verify_api_key("api_key", mock_db)


class TestDependencyIntegration:
    """Test dependency integration scenarios."""

    @pytest.mark.asyncio
    async def test_dependency_chain(self):
        """Test chaining of dependencies."""
        mock_db = MagicMock(spec=AsyncSession)
        mock_user = MagicMock()
        mock_user.is_active = True
        
        with patch('src.cve_feed_service.core.dependencies.get_current_user') as mock_get_user:
            with patch('src.cve_feed_service.core.dependencies.get_current_active_user') as mock_get_active:
                mock_get_user.return_value = mock_user
                mock_get_active.return_value = mock_user
                
                # Test dependency chain
                user_dep = get_current_user_dependency()
                active_user_dep = get_current_active_user_dependency()
                
                user = await user_dep("token", mock_db)
                active_user = await active_user_dep(user)
                
                assert user == mock_user
                assert active_user == mock_user

    @pytest.mark.asyncio
    async def test_dependency_error_propagation(self):
        """Test error propagation through dependencies."""
        mock_db = MagicMock(spec=AsyncSession)
        
        with patch('src.cve_feed_service.core.dependencies.get_current_user') as mock_get_user:
            mock_get_user.side_effect = HTTPException(status_code=401, detail="Token expired")
            
            user_dep = get_current_user_dependency()
            
            with pytest.raises(HTTPException) as exc_info:
                await user_dep("expired_token", mock_db)
            
            assert exc_info.value.status_code == 401
            assert "Token expired" in str(exc_info.value.detail)

    def test_dependency_configuration(self):
        """Test dependency configuration consistency."""
        settings = get_settings()
        
        # Settings should be consistent across calls
        settings2 = get_settings()
        assert settings is settings2
        
        # Settings should have required configuration
        assert settings.secret_key is not None
        assert len(settings.secret_key) > 0

    @pytest.mark.asyncio
    async def test_dependency_performance(self):
        """Test dependency performance."""
        import time
        
        # Test settings dependency performance
        start_time = time.time()
        for _ in range(100):
            settings = get_settings()
        end_time = time.time()
        
        # Should be very fast due to caching
        assert (end_time - start_time) < 0.1

    @pytest.mark.asyncio
    async def test_dependency_memory_usage(self):
        """Test dependency memory usage."""
        import gc
        
        # Test that dependencies don't create memory leaks
        initial_objects = len(gc.get_objects())
        
        # Create many dependency instances
        for _ in range(100):
            settings = get_settings()
            del settings
        
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # Should not significantly increase object count
        assert final_objects - initial_objects < 50
