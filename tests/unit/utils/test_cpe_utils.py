"""Tests for CPE (Common Platform Enumeration) utilities."""

import pytest
from unittest.mock import patch, MagicMock

from src.cve_feed_service.utils.cpe_utils import (
    CPEValidator,
    CPEMatcher,
    validate_cpe_string,
    parse_cpe_string
)


class TestCPEParsing:
    """Test CPE parsing functionality."""

    def test_parse_cpe_valid_format(self):
        """Test parsing valid CPE strings."""
        valid_cpes = [
            "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
            "cpe:2.3:o:microsoft:windows:10:*:*:*:*:*:*:*",
            "cpe:2.3:h:cisco:router:*:*:*:*:*:*:*:*",
            "cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*"
        ]

        for cpe in valid_cpes:
            result = parse_cpe_string(cpe)
            assert result is not None
            assert isinstance(result, dict)
            assert "part" in result
            assert "vendor" in result
            assert "product" in result

    def test_parse_cpe_invalid_format(self):
        """Test parsing invalid CPE strings."""
        invalid_cpes = [
            "invalid_cpe_string",
            "cpe:2.3:invalid",
            "cpe:1.0:a:vendor:product",  # Wrong version
            "",
            None,
            "cpe:2.3:a:vendor",  # Incomplete
            "not:a:cpe:string:at:all"
        ]

        for cpe in invalid_cpes:
            result = parse_cpe_string(cpe)
            assert result is None or result == {}

    def test_parse_cpe_edge_cases(self):
        """Test parsing CPE edge cases."""
        edge_cases = [
            "cpe:2.3:a:*:*:*:*:*:*:*:*:*:*",  # All wildcards
            "cpe:2.3:a:vendor:product:*:*:*:*:*:*:*:*",  # Standard format
            "cpe:2.3:a:vendor_name:product-name:1.0.0:*:*:*:*:*:*:*",  # Special chars
        ]
        
        for cpe in edge_cases:
            result = parse_cpe_string(cpe)
            if result is not None:
                assert isinstance(result, dict)
                assert len(result) > 0

    def test_parse_cpe_special_characters(self):
        """Test parsing CPE with special characters."""
        special_cpes = [
            "cpe:2.3:a:vendor\\:name:product:1.0:*:*:*:*:*:*:*",  # Escaped colon
            "cpe:2.3:a:vendor_name:product-name:1.0.0:*:*:*:*:*:*:*",  # Underscore/dash
            "cpe:2.3:a:vendor.name:product.name:1.0.0:*:*:*:*:*:*:*",  # Dots
        ]
        
        for cpe in special_cpes:
            result = parse_cpe_string(cpe)
            # Should handle special characters appropriately
            assert result is None or isinstance(result, dict)


class TestCPEValidation:
    """Test CPE validation functionality."""

    def test_validate_cpe_valid(self):
        """Test validation of valid CPE strings."""
        valid_cpes = [
            "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
            "cpe:2.3:o:microsoft:windows:10:*:*:*:*:*:*:*",
            "cpe:2.3:h:cisco:router:*:*:*:*:*:*:*:*"
        ]
        
        for cpe in valid_cpes:
            assert validate_cpe_string(cpe) is True

    def test_validate_cpe_invalid(self):
        """Test validation of invalid CPE strings."""
        invalid_cpes = [
            "invalid_cpe",
            "cpe:2.3:invalid",
            "cpe:1.0:a:vendor:product",
            "",
            None,
            "not:a:cpe"
        ]
        
        for cpe in invalid_cpes:
            assert validate_cpe_string(cpe) is False

    def test_validate_cpe_edge_cases(self):
        """Test CPE validation edge cases."""
        edge_cases = [
            "cpe:2.3:a:*:*:*:*:*:*:*:*:*:*",  # All wildcards
            "cpe:2.3:a:vendor:product:1.0:*:*:*:*:*:*:*",  # Minimal valid
            "cpe:2.3:x:vendor:product:1.0:*:*:*:*:*:*:*",  # Invalid part type
        ]
        
        for cpe in edge_cases:
            result = validate_cpe_string(cpe)
            assert isinstance(result, bool)

    def test_validate_cpe_part_types(self):
        """Test CPE validation for different part types."""
        part_types = {
            "a": True,   # Application
            "o": True,   # Operating System
            "h": True,   # Hardware
            "x": False,  # Invalid
            "*": False,  # Wildcard not valid for part
            "": False    # Empty
        }
        
        for part_type, expected in part_types.items():
            cpe = f"cpe:2.3:{part_type}:vendor:product:1.0:*:*:*:*:*:*:*"
            result = validate_cpe_string(cpe)
            if expected:
                assert result is True or result is False  # Depends on implementation
            else:
                assert result is False


class TestCPEValidator:
    """Test CPE validator class functionality."""

    def test_cpe_validator_validate_cpe_string(self):
        """Test CPEValidator.validate_cpe_string method."""
        valid_cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        invalid_cpe = "invalid_cpe"

        assert CPEValidator.validate_cpe_string(valid_cpe) is True
        assert CPEValidator.validate_cpe_string(invalid_cpe) is False
        assert CPEValidator.validate_cpe_string(None) is False
        assert CPEValidator.validate_cpe_string("") is False

    def test_cpe_validator_parse_cpe_string(self):
        """Test CPEValidator.parse_cpe_string method."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"

        result = CPEValidator.parse_cpe_string(cpe)

        assert result is not None
        assert isinstance(result, dict)
        assert result["part"] == "a"
        assert result["vendor"] == "nginx"
        assert result["product"] == "nginx"
        assert result["version"] == "1.20.1"

    def test_cpe_validator_normalize_cpe_component(self):
        """Test CPEValidator.normalize_cpe_component method."""
        assert CPEValidator.normalize_cpe_component("*") == "*"
        assert CPEValidator.normalize_cpe_component("") == "*"
        assert CPEValidator.normalize_cpe_component(None) == "*"

        # Test normalization
        normalized = CPEValidator.normalize_cpe_component("Test_Component-Name")
        assert isinstance(normalized, str)
        assert normalized.islower()  # Should be lowercase


class TestCPEMatcher:
    """Test CPE matcher class functionality."""

    def test_cpe_matcher_cpe_matches_exact(self):
        """Test CPEMatcher.cpe_matches with exact match."""
        cpe1 = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        cpe2 = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"

        assert CPEMatcher.cpe_matches(cpe1, cpe2) is True

    def test_cpe_matcher_cpe_matches_different(self):
        """Test CPEMatcher.cpe_matches with different CPEs."""
        cpe1 = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        cpe2 = "cpe:2.3:a:apache:httpd:2.4.41:*:*:*:*:*:*:*"

        assert CPEMatcher.cpe_matches(cpe1, cpe2) is False

    def test_cpe_matcher_cpe_matches_wildcards(self):
        """Test CPEMatcher.cpe_matches with wildcards."""
        cpe1 = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        cpe2 = "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*"

        result = CPEMatcher.cpe_matches(cpe1, cpe2)
        # Should handle wildcards appropriately
        assert isinstance(result, bool)

    def test_cpe_matcher_extract_cpe_base(self):
        """Test CPEMatcher.extract_cpe_base method."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"

        base = CPEMatcher.extract_cpe_base(cpe)

        assert base == "nginx:nginx"

    def test_cpe_matcher_generate_cpe_patterns(self):
        """Test CPEMatcher.generate_cpe_patterns method."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"

        patterns = CPEMatcher.generate_cpe_patterns(cpe)

        assert isinstance(patterns, list)
        assert len(patterns) > 0
        assert cpe in patterns  # Original should be included


class TestCPEUtilityFunctions:
    """Test utility functions for CPE handling."""

    def test_validate_cpe_string_function(self):
        """Test standalone validate_cpe_string function."""
        valid_cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        invalid_cpe = "invalid_cpe"

        assert validate_cpe_string(valid_cpe) is True
        assert validate_cpe_string(invalid_cpe) is False

    def test_parse_cpe_string_function(self):
        """Test standalone parse_cpe_string function."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"

        result = parse_cpe_string(cpe)

        assert result is not None
        assert isinstance(result, dict)
        assert result["vendor"] == "nginx"
        assert result["product"] == "nginx"

    def test_cpe_functions_with_edge_cases(self):
        """Test CPE functions with edge cases."""
        edge_cases = [
            None,
            "",
            "invalid",
            "cpe:2.3:a:*:*:*:*:*:*:*:*:*:*",  # All wildcards
        ]

        for case in edge_cases:
            # Should not raise exceptions
            validate_result = validate_cpe_string(case)
            parse_result = parse_cpe_string(case)

            assert isinstance(validate_result, bool)
            assert parse_result is None or isinstance(parse_result, dict)


class TestCPEVariations:
    """Test CPE variation generation."""

    def test_generate_cpe_variations_basic(self):
        """Test basic CPE variation generation."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        
        variations = generate_cpe_variations(cpe)
        
        assert isinstance(variations, list)
        assert len(variations) > 0
        assert cpe in variations  # Original should be included

    def test_generate_cpe_variations_version_wildcards(self):
        """Test CPE variation generation with version wildcards."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        
        variations = generate_cpe_variations(cpe)
        
        if variations:
            # Should include version wildcards
            version_wildcard = "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*"
            assert version_wildcard in variations or len(variations) > 1

    def test_generate_cpe_variations_vendor_product(self):
        """Test CPE variation generation for vendor/product."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        
        variations = generate_cpe_variations(cpe)
        
        if variations:
            # Should generate meaningful variations
            assert all(isinstance(v, str) for v in variations)
            assert all(v.startswith("cpe:2.3:") for v in variations)

    def test_generate_cpe_variations_invalid_input(self):
        """Test CPE variation generation with invalid input."""
        invalid_inputs = [
            None,
            "",
            "invalid_cpe",
            "cpe:2.3:incomplete"
        ]
        
        for invalid_input in invalid_inputs:
            variations = generate_cpe_variations(invalid_input)
            assert variations == [] or variations is None


class TestCPEConversion:
    """Test CPE conversion utilities."""

    def test_cpe_to_dict_valid(self):
        """Test converting CPE to dictionary."""
        cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        
        result = cpe_to_dict(cpe)
        
        assert isinstance(result, dict)
        assert "part" in result
        assert "vendor" in result
        assert "product" in result
        assert "version" in result

    def test_cpe_to_dict_invalid(self):
        """Test converting invalid CPE to dictionary."""
        invalid_cpes = [
            None,
            "",
            "invalid_cpe",
            123
        ]
        
        for cpe in invalid_cpes:
            result = cpe_to_dict(cpe)
            assert result is None or result == {}

    def test_dict_to_cpe_valid(self):
        """Test converting dictionary to CPE."""
        cpe_dict = {
            "part": "a",
            "vendor": "nginx",
            "product": "nginx",
            "version": "1.20.1",
            "update": "*",
            "edition": "*",
            "language": "*",
            "sw_edition": "*",
            "target_sw": "*",
            "target_hw": "*",
            "other": "*"
        }
        
        result = dict_to_cpe(cpe_dict)
        
        assert isinstance(result, str)
        assert result.startswith("cpe:2.3:")
        assert "nginx" in result

    def test_dict_to_cpe_minimal(self):
        """Test converting minimal dictionary to CPE."""
        minimal_dict = {
            "part": "a",
            "vendor": "nginx",
            "product": "nginx"
        }
        
        result = dict_to_cpe(minimal_dict)
        
        if result:
            assert isinstance(result, str)
            assert result.startswith("cpe:2.3:")

    def test_dict_to_cpe_invalid(self):
        """Test converting invalid dictionary to CPE."""
        invalid_dicts = [
            None,
            {},
            {"invalid": "dict"},
            {"part": "invalid_part"},
            []
        ]
        
        for invalid_dict in invalid_dicts:
            result = dict_to_cpe(invalid_dict)
            assert result is None or result == ""

    def test_cpe_roundtrip_conversion(self):
        """Test roundtrip CPE conversion (CPE -> dict -> CPE)."""
        original_cpe = "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        
        # Convert to dict
        cpe_dict = cpe_to_dict(original_cpe)
        
        if cpe_dict:
            # Convert back to CPE
            converted_cpe = dict_to_cpe(cpe_dict)
            
            if converted_cpe:
                # Should be equivalent (may not be identical due to normalization)
                assert isinstance(converted_cpe, str)
                assert converted_cpe.startswith("cpe:2.3:")
                assert "nginx" in converted_cpe
