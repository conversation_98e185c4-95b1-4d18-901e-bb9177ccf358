{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "quality-check": "npm run type-check && npm run lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.34.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.1", "jest-environment-jsdom": "^30.0.1", "msw": "^2.10.2", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^4.1.10", "ts-jest": "^29.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}