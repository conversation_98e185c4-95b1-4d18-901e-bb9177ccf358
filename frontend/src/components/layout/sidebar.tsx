'use client';

import { 
  LayoutDashboard, 
  Shield, 
  Package, 
  Users, 
  Settings, 
  FileText,
  Search,
  AlertTriangle,
  Database,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface SidebarProps {
  className?: string;
  user?: {
    role: string;
  } | null;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  adminOnly?: boolean;
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'CVE Feed',
    href: '/cves',
    icon: Shield,
    badge: 'New',
  },
  {
    title: 'Applications',
    href: '/applications',
    icon: Package,
  },
  {
    title: 'Components',
    href: '/components',
    icon: Database,
  },
  {
    title: 'Search',
    href: '/search',
    icon: Search,
  },
  {
    title: 'Reports',
    href: '/reports',
    icon: FileText,
  },
  {
    title: 'Activity',
    href: '/activity',
    icon: Activity,
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
    adminOnly: true,
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
];

export function Sidebar({ className, user }: SidebarProps) {
  const pathname = usePathname();
  const isAdmin = user?.role === 'admin' || user?.role === 'security_admin';

  const filteredItems = navigationItems.filter(item => 
    !item.adminOnly || isAdmin
  );

  return (
    <div className={cn("pb-12 w-64", className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight text-slate-50">
            Navigation
          </h2>
          <div className="space-y-1">
            {filteredItems.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/dashboard' && pathname.startsWith(item.href));
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-slate-800 hover:text-slate-50",
                    isActive 
                      ? "bg-slate-800 text-slate-50 border-l-4 border-cyan-400" 
                      : "text-slate-400"
                  )}
                  data-testid={`nav-${item.title.toLowerCase().replace(/\s+/g, '-')}`}
                >
                  <div className="flex items-center">
                    <item.icon className="mr-3 h-4 w-4" />
                    {item.title}
                  </div>
                  {item.badge && (
                    <Badge 
                      variant="secondary" 
                      className="ml-auto bg-cyan-600 text-white text-xs"
                    >
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              );
            })}
          </div>
        </div>

        {/* Quick Stats Section */}
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight text-slate-50">
            Quick Stats
          </h2>
          <div className="space-y-2 px-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Critical CVEs</span>
              <Badge variant="destructive" className="text-xs">
                12
              </Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">High Severity</span>
              <Badge className="bg-orange-600 text-white text-xs">
                45
              </Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Applications</span>
              <span className="text-slate-50 font-medium">127</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Components</span>
              <span className="text-slate-50 font-medium">1,234</span>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight text-slate-50">
            System Status
          </h2>
          <div className="space-y-2 px-4">
            <div className="flex items-center space-x-2 text-sm">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="text-slate-400">CVE Feed</span>
              <span className="text-green-400 text-xs ml-auto">Online</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="text-slate-400">Database</span>
              <span className="text-green-400 text-xs ml-auto">Healthy</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
              <span className="text-slate-400">API</span>
              <span className="text-yellow-400 text-xs ml-auto">Slow</span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight text-slate-50">
            Recent Activity
          </h2>
          <div className="space-y-2 px-4">
            <div className="text-xs text-slate-400">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-3 w-3 text-red-400" />
                <span>New critical CVE detected</span>
              </div>
              <span className="text-slate-500">2 minutes ago</span>
            </div>
            <div className="text-xs text-slate-400">
              <div className="flex items-center space-x-2">
                <Package className="h-3 w-3 text-blue-400" />
                <span>Application updated</span>
              </div>
              <span className="text-slate-500">15 minutes ago</span>
            </div>
            <div className="text-xs text-slate-400">
              <div className="flex items-center space-x-2">
                <Users className="h-3 w-3 text-green-400" />
                <span>User logged in</span>
              </div>
              <span className="text-slate-500">1 hour ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
