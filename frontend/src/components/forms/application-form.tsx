'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import type { ApplicationCreateRequest } from '@/types';

const applicationSchema = z.object({
  name: z
    .string()
    .min(1, 'Application name is required')
    .min(3, 'Application name must be at least 3 characters')
    .max(100, 'Application name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  environment: z
    .enum(['development', 'staging', 'production'], {
      required_error: 'Environment is required',
    }),
  criticality: z
    .enum(['low', 'medium', 'high', 'critical'], {
      required_error: 'Criticality level is required',
    }),
  owner: z
    .string()
    .email('Please enter a valid email address')
    .optional()
    .or(z.literal('')),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

export interface ApplicationFormProps {
  onSubmit: (data: ApplicationCreateRequest) => void | Promise<void>;
  loading?: boolean;
  error?: string | null;
  initialData?: Partial<ApplicationCreateRequest>;
  mode?: 'create' | 'edit';
  className?: string;
}

export function ApplicationForm({
  onSubmit,
  loading = false,
  error,
  initialData,
  mode = 'create',
  className,
}: ApplicationFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    clearErrors,
    reset,
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
    mode: 'onChange',
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      environment: initialData?.environment || 'development',
      criticality: initialData?.criticality || 'medium',
      owner: initialData?.owner || '',
    },
  });

  const handleFormSubmit = async (data: ApplicationFormData) => {
    try {
      await onSubmit(data);
      if (mode === 'create') {
        reset(); // Reset form after successful creation
      }
    } catch (err) {
      // Error handling is done by parent component
    }
  };

  // Clear errors when user starts typing
  const handleInputChange = () => {
    if (error) {
      clearErrors();
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Create New Application' : 'Edit Application'}
        </CardTitle>
        <CardDescription>
          {mode === 'create' 
            ? 'Add a new application to monitor for vulnerabilities'
            : 'Update application information and settings'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <div
              className="bg-destructive/15 border border-destructive/20 text-destructive px-4 py-3 rounded-lg"
              role="alert"
              data-testid="error-message"
            >
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Application Name *</Label>
            <Input
              {...register('name')}
              id="name"
              placeholder="Enter application name"
              error={errors.name?.message}
              onChange={(e) => {
                register('name').onChange(e);
                handleInputChange();
              }}
              data-testid="name-input"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <textarea
              {...register('description')}
              id="description"
              placeholder="Enter application description (optional)"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              onChange={(e) => {
                register('description').onChange(e);
                handleInputChange();
              }}
              data-testid="description-input"
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="environment">Environment *</Label>
              <select
                {...register('environment')}
                id="environment"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                onChange={(e) => {
                  register('environment').onChange(e);
                  handleInputChange();
                }}
                data-testid="environment-select"
                required
              >
                <option value="development">Development</option>
                <option value="staging">Staging</option>
                <option value="production">Production</option>
              </select>
              {errors.environment && (
                <p className="text-sm text-destructive">{errors.environment.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="criticality">Criticality Level *</Label>
              <select
                {...register('criticality')}
                id="criticality"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                onChange={(e) => {
                  register('criticality').onChange(e);
                  handleInputChange();
                }}
                data-testid="criticality-select"
                required
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
              {errors.criticality && (
                <p className="text-sm text-destructive">{errors.criticality.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="owner">Owner Email</Label>
            <Input
              {...register('owner')}
              id="owner"
              type="email"
              placeholder="Enter owner email (optional)"
              error={errors.owner?.message}
              onChange={(e) => {
                register('owner').onChange(e);
                handleInputChange();
              }}
              data-testid="owner-input"
            />
          </div>

          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              loading={loading}
              disabled={!isValid || loading}
              data-testid="submit-button"
              className="flex-1"
            >
              {loading 
                ? (mode === 'create' ? 'Creating...' : 'Updating...') 
                : (mode === 'create' ? 'Create Application' : 'Update Application')
              }
            </Button>
            
            {mode === 'create' && (
              <Button
                type="button"
                variant="outline"
                onClick={() => reset()}
                disabled={loading}
                data-testid="reset-button"
              >
                Reset
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
