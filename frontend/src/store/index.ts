/**
 * Redux store configuration for CVE Feed Service
 */

import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { authApi } from './api/authApi';
import { dashboardApi } from './api/dashboardApi';
import { cveApi } from './api/cveApi';
import { applicationApi } from './api/applicationApi';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import cveSlice from './slices/cveSlice';

export const store = configureStore({
  reducer: {
    // Feature slices
    auth: authSlice,
    ui: uiSlice,
    cve: cveSlice,
    
    // API slices
    [authApi.reducerPath]: authApi.reducer,
    [dashboardApi.reducerPath]: dashboardApi.reducer,
    [cveApi.reducerPath]: cveApi.reducer,
    [applicationApi.reducerPath]: applicationApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // Ignore these action types
          'persist/PERSIST',
          'persist/REHYDRATE',
        ],
        ignoredPaths: [
          // Ignore these paths in the state
          'auth.lastActivity',
        ],
      },
    }).concat(
      authApi.middleware,
      dashboardApi.middleware,
      cveApi.middleware,
      applicationApi.middleware
    ),
  devTools: process.env.NODE_ENV !== 'production',
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export hooks for use in components
export { useAppDispatch, useAppSelector } from './hooks';

// Store persistence configuration (if needed)
export const persistConfig = {
  key: 'cve-feed-service',
  storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  whitelist: ['auth', 'ui'], // Only persist these slices
  blacklist: ['api'], // Don't persist API cache
};

// Development helpers
if (process.env.NODE_ENV === 'development') {
  // Add store to window for debugging
  (window as any).__store__ = store;
  
  // Log store state changes in development
  store.subscribe(() => {
    const state = store.getState();
    console.debug('Store state updated:', {
      auth: state.auth.isAuthenticated,
      ui: state.ui.theme,
      timestamp: new Date().toISOString(),
    });
  });
}
