/**
 * Login page component
 */

import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { LoginForm } from '../components/molecules/LoginForm';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { useLoginMutation } from '../store/api/authApi';
import { loginStart, loginSuccess, loginFailure } from '../store/slices/authSlice';
import { selectIsAuthenticated, selectAuthError, selectAuthLoading } from '../store/slices/authSlice';
import type { LoginRequest } from '../types/auth';

export const LoginPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const authError = useAppSelector(selectAuthError);
  const authLoading = useAppSelector(selectAuthLoading);
  
  const [login, { isLoading: loginMutationLoading }] = useLoginMutation();
  
  const isLoading = authLoading || loginMutationLoading;
  
  // Redirect to intended page after login
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  const handleLogin = async (loginData: LoginRequest) => {
    try {
      dispatch(loginStart());
      
      const result = await login(loginData).unwrap();
      
      dispatch(loginSuccess({
        user: result.user,
        token: result.access_token,
        refreshToken: result.refresh_token,
      }));
      
      // Navigation will happen automatically due to isAuthenticated change
    } catch (error: any) {
      dispatch(loginFailure({
        code: error.status || 'LOGIN_ERROR',
        message: error.data?.detail || 'Login failed. Please check your credentials.',
      }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gray-800 py-8 px-4 shadow-xl sm:rounded-lg sm:px-10 border border-gray-700">
          <LoginForm
            onSubmit={handleLogin}
            loading={isLoading}
            error={authError}
          />
        </div>
      </div>
      
      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">
          CVE Feed Service v1.0.0 - Secure Vulnerability Management
        </p>
      </div>
    </div>
  );
};
