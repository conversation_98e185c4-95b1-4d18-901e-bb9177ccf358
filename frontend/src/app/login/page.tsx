'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/forms/login-form';
import { useNotifications } from '@/hooks/use-notifications';

interface LoginData {
  username: string;
  password: string;
  rememberMe: boolean;
}

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { showSuccess, showError } = useNotifications();

  const handleLogin = async (data: LoginData) => {
    setLoading(true);
    setError(null);

    try {
      // Call our CVE Feed Service API
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: data.username,
          password: data.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Login failed');
      }

      const loginResponse = await response.json();

      // Store the token (you might want to use a proper auth store here)
      localStorage.setItem('access_token', loginResponse.access_token);
      localStorage.setItem('user', JSON.stringify(loginResponse.user));

      showSuccess('Login successful', `Welcome back, ${loginResponse.user.full_name}!`);
      router.push('/dashboard');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
      showError('Login failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <LoginForm
          onSubmit={handleLogin}
          loading={loading}
          error={error}
        />
        
        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">CVE Feed Service</h3>
          <p className="text-sm text-muted-foreground">
            Secure vulnerability management and monitoring platform
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Contact your administrator for access credentials
          </p>
        </div>
      </div>
    </div>
  );
}
