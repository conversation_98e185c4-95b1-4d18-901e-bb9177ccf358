/**
 * CVE-related type definitions
 */

export type CVESeverity = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';

export interface CVE {
  id: string;
  title: string;
  description: string;
  severity: CVESeverity;
  cvss_score: number;
  cvss_vector?: string;
  published_date: string;
  modified_date: string;
  cwe_ids: string[];
  references: CVEReference[];
  affected_components: AffectedComponent[];
  remediation?: RemediationInfo;
  tags: string[];
  source: string;
  status: CVEStatus;
}

export type CVEStatus = 'NEW' | 'ACKNOWLEDGED' | 'IN_PROGRESS' | 'RESOLVED' | 'DISMISSED';

export interface CVEReference {
  url: string;
  source: string;
  type: 'ADVISORY' | 'PATCH' | 'VENDOR' | 'EXPLOIT' | 'OTHER';
  description?: string;
}

export interface AffectedComponent {
  component_id: string;
  component_name: string;
  component_version: string;
  cpe_string: string;
  application_id: string;
  application_name: string;
  environment: string;
  criticality: ApplicationCriticality;
}

export interface RemediationInfo {
  description: string;
  steps: string[];
  patch_available: boolean;
  patch_url?: string;
  workaround?: string;
  estimated_effort: 'LOW' | 'MEDIUM' | 'HIGH';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface CVEFeedRequest {
  limit?: number;
  offset?: number;
  severity?: CVESeverity[];
  application_id?: string;
  component_id?: string;
  date_range?: DateRange;
  status?: CVEStatus[];
  search?: string;
  sort_by?: CVESortField;
  sort_order?: 'asc' | 'desc';
}

export type CVESortField = 
  | 'published_date'
  | 'modified_date'
  | 'cvss_score'
  | 'severity'
  | 'title';

export interface CVEFeedResponse {
  cves: CVE[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
  filters_applied: CVEFeedRequest;
}

export interface CVETimelineItem {
  cve_id: string;
  cve_title: string;
  severity: CVESeverity;
  cvss_score: number;
  published_date: string;
  affected_components_count: number;
  status: CVEStatus;
}

export interface CVETimeline {
  items: CVETimelineItem[];
  date_range: DateRange;
  total_count: number;
}

export interface CVEExportRequest {
  format: 'CSV' | 'JSON' | 'PDF';
  filters: CVEFeedRequest;
  include_fields?: string[];
}

export interface CVEExportResponse {
  download_url: string;
  filename: string;
  expires_at: string;
  file_size: number;
}

// Search and filtering types
export interface CVESearchRequest {
  query: string;
  filters?: CVEFeedRequest;
  highlight?: boolean;
}

export interface CVESearchResponse {
  results: CVESearchResult[];
  total: number;
  query: string;
  suggestions?: string[];
}

export interface CVESearchResult {
  cve: CVE;
  score: number;
  highlights: Record<string, string[]>;
}

// Statistics and metrics
export interface CVEMetrics {
  total_cves: number;
  critical_cves: number;
  high_cves: number;
  medium_cves: number;
  low_cves: number;
  new_cves_last_7_days: number;
  new_cves_last_30_days: number;
  resolved_cves_last_30_days: number;
  average_resolution_time: number; // in days
  top_affected_components: ComponentVulnerabilityCount[];
}

export interface ComponentVulnerabilityCount {
  component_name: string;
  component_id: string;
  vulnerability_count: number;
  critical_count: number;
  high_count: number;
}

// Common utility types
export interface DateRange {
  start_date: string;
  end_date: string;
}

export type ApplicationCriticality = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

// UI-specific types
export interface CVEListState {
  cves: CVE[];
  loading: boolean;
  error: string | null;
  filters: CVEFeedRequest;
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  selected_cves: string[];
}

export interface CVEDetailsState {
  cve: CVE | null;
  loading: boolean;
  error: string | null;
  related_cves: CVE[];
  timeline: CVETimelineItem[];
}
