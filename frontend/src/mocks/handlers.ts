/**
 * Mock API handlers using MSW (Mock Service Worker)
 */

import { http, HttpResponse } from 'msw';
import { config } from '../config/environment';
import type { LoginRequest, LoginResponse, User } from '../types/auth';

const baseUrl = config.apiBaseUrl;

// Mock user data
const mockUser: User = {
  id: 'user-123',
  username: '<EMAIL>',
  email: '<EMAIL>',
  first_name: 'Security',
  last_name: 'Analyst',
  role: 'security_analyst',
  is_active: true,
  is_verified: true,
  created_at: '2023-01-01T00:00:00Z',
  last_login: new Date().toISOString(),
};

// Mock JWT token
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.mock-signature';

export const handlers = [
  // Authentication endpoints
  http.post(`${baseUrl}/auth/login`, async ({ request }) => {
    const body = await request.json() as LoginRequest;
    
    // Simulate validation
    if (!body.username || !body.password) {
      return HttpResponse.json(
        { detail: 'Email and password are required' },
        { status: 400 }
      );
    }
    
    // Mock successful login for specific credentials
    if (body.username === '<EMAIL>' && body.password === 'password123') {
      const response: LoginResponse = {
        access_token: mockToken,
        token_type: 'bearer',
        expires_in: 3600,
        user: mockUser,
        refresh_token: 'mock-refresh-token',
      };
      
      return HttpResponse.json(response);
    }
    
    // Mock failed login
    return HttpResponse.json(
      { detail: 'Incorrect username or password' },
      { status: 401 }
    );
  }),

  http.post(`${baseUrl}/auth/logout`, () => {
    return HttpResponse.json({ message: 'Successfully logged out' });
  }),

  http.get(`${baseUrl}/auth/me`, ({ request }) => {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { detail: 'Authentication required' },
        { status: 401 }
      );
    }
    
    return HttpResponse.json(mockUser);
  }),

  // Dashboard endpoints
  http.get(`${baseUrl}/dashboard/metrics`, ({ request }) => {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { detail: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const metrics = {
      total_cves: 1234,
      critical_cves: 23,
      high_cves: 156,
      medium_cves: 445,
      low_cves: 610,
      applications_count: 42,
      components_count: 187,
      last_updated: new Date().toISOString(),
      trend_data: {
        cves_last_7_days: 8,
        cves_last_30_days: 32,
        new_applications_last_30_days: 2,
      },
    };
    
    return HttpResponse.json(metrics);
  }),

  // CVE endpoints
  http.get(`${baseUrl}/cves/feed`, ({ request }) => {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { detail: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const severity = url.searchParams.get('severity');
    
    // Mock CVE data
    const mockCVEs = Array.from({ length: 100 }, (_, i) => ({
      id: `CVE-2023-${String(1000 + i).padStart(4, '0')}`,
      title: `Sample vulnerability ${i + 1}`,
      description: `This is a mock vulnerability description for CVE-2023-${String(1000 + i).padStart(4, '0')}. It demonstrates various security issues that could affect applications.`,
      severity: ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'][i % 4] as any,
      cvss_score: Math.round((Math.random() * 10) * 10) / 10,
      published_date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      modified_date: new Date().toISOString(),
      cwe_ids: [`CWE-${Math.floor(Math.random() * 1000)}`],
      references: [
        {
          url: `https://nvd.nist.gov/vuln/detail/CVE-2023-${String(1000 + i).padStart(4, '0')}`,
          source: 'NVD',
          type: 'ADVISORY' as any,
        },
      ],
      affected_components: [],
      tags: ['web', 'security'],
      source: 'NVD',
      status: 'NEW' as any,
    }));
    
    // Apply severity filter if provided
    let filteredCVEs = mockCVEs;
    if (severity) {
      filteredCVEs = mockCVEs.filter(cve => cve.severity === severity);
    }
    
    // Apply pagination
    const paginatedCVEs = filteredCVEs.slice(offset, offset + limit);
    
    const response = {
      cves: paginatedCVEs,
      total: filteredCVEs.length,
      limit,
      offset,
      has_more: offset + limit < filteredCVEs.length,
      filters_applied: {
        limit,
        offset,
        severity,
      },
    };
    
    return HttpResponse.json(response);
  }),

  // Applications endpoints
  http.get(`${baseUrl}/applications`, ({ request }) => {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { detail: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const mockApplications = [
      {
        id: 'app-1',
        name: 'WebApp-Production',
        description: 'Main production web application',
        environment: 'production',
        criticality: 'HIGH',
        components_count: 25,
        vulnerabilities_count: 12,
        last_scan: new Date().toISOString(),
      },
      {
        id: 'app-2',
        name: 'API-Gateway',
        description: 'API gateway service',
        environment: 'production',
        criticality: 'CRITICAL',
        components_count: 15,
        vulnerabilities_count: 8,
        last_scan: new Date().toISOString(),
      },
      {
        id: 'app-3',
        name: 'Mobile-Backend',
        description: 'Backend services for mobile app',
        environment: 'production',
        criticality: 'MEDIUM',
        components_count: 18,
        vulnerabilities_count: 5,
        last_scan: new Date().toISOString(),
      },
    ];
    
    return HttpResponse.json(mockApplications);
  }),

  // Global search endpoint
  http.get(`${baseUrl}/search`, ({ request }) => {
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { detail: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    
    // Mock search results
    const mockResults = [
      {
        type: 'cve',
        id: 'CVE-2023-1234',
        title: 'Critical vulnerability in nginx',
        description: 'Remote code execution vulnerability',
        severity: 'CRITICAL',
        score: 0.95,
      },
      {
        type: 'application',
        id: 'app-1',
        title: 'WebApp-Production',
        description: 'Main production web application',
        score: 0.8,
      },
    ].filter(result => 
      result.title.toLowerCase().includes(query.toLowerCase()) ||
      result.description.toLowerCase().includes(query.toLowerCase())
    );
    
    return HttpResponse.json({
      results: mockResults,
      total: mockResults.length,
      query,
    });
  }),
];
