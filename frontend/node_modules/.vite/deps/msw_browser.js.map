{"version": 3, "sources": ["../../msw/src/core/utils/toResponseInit.ts", "../../msw/src/core/utils/internal/isHandlerKind.ts", "../../msw/src/core/utils/internal/isObject.ts", "../../msw/src/core/utils/internal/mergeRight.ts", "../../@mswjs/interceptors/src/utils/hasConfigurableGlobal.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/utils/bindEvent.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/utils/events.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/WebSocketClientConnection.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/WebSocketServerConnection.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/WebSocketOverride.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/WebSocketClassTransport.ts", "../../@mswjs/interceptors/src/interceptors/WebSocket/index.ts", "../../msw/src/core/ws/webSocketInterceptor.ts", "../../msw/src/core/ws/handleWebSocketEvent.ts", "../../msw/src/core/ws/utils/getMessageLength.ts", "../../msw/src/core/ws/utils/truncateMessage.ts", "../../msw/src/core/ws/utils/getPublicData.ts", "../../msw/src/core/ws/utils/attachWebSocketLogger.ts", "../../msw/node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/format.ts", "../../msw/node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/invariant.ts", "../../msw/node_modules/.pnpm/is-node-process@1.2.0/node_modules/is-node-process/src/index.ts", "../../msw/src/browser/setupWorker/start/createStartHandler.ts", "../../msw/node_modules/.pnpm/@open-draft+until@2.1.0/node_modules/@open-draft/until/src/until.ts", "../../msw/src/browser/setupWorker/start/utils/getWorkerInstance.ts", "../../msw/src/browser/utils/getAbsoluteWorkerUrl.ts", "../../msw/src/browser/setupWorker/start/utils/getWorkerByRegistration.ts", "../../msw/src/browser/setupWorker/start/utils/enableMocking.ts", "../../msw/src/browser/setupWorker/start/utils/printStartMessage.ts", "../../msw/src/browser/setupWorker/start/utils/createMessageChannel.ts", "../../msw/src/browser/utils/pruneGetRequestBody.ts", "../../msw/src/browser/utils/deserializeRequest.ts", "../../msw/src/browser/setupWorker/start/createRequestListener.ts", "../../msw/src/browser/utils/checkWorkerIntegrity.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/bufferUtils.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/glossary.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/canParseUrl.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/getValueBySymbol.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/fetchUtils.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/getRawRequest.ts", "../../msw/node_modules/.pnpm/@open-draft+logger@0.3.0/node_modules/@open-draft/logger/lib/index.mjs", "../../msw/node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/MemoryLeakError.ts", "../../msw/node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/Emitter.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/Interceptor.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/createRequestId.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/BatchInterceptor.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/getCleanUrl.ts", "../../msw/src/browser/setupWorker/start/createResponseListener.ts", "../../msw/src/browser/setupWorker/start/utils/validateWorkerScope.ts", "../../msw/src/browser/setupWorker/stop/createStop.ts", "../../msw/src/browser/setupWorker/stop/utils/printStopMessage.ts", "../../msw/src/browser/setupWorker/start/utils/prepareStartHandler.ts", "../../msw/node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/createDeferredExecutor.ts", "../../msw/node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/DeferredPromise.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/RequestController.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/InterceptorError.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/emitAsync.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/handleRequest.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isObject.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isPropertyAccessible.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/responseUtils.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isNodeLikeError.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/hasConfigurableGlobal.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/index.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/createNetworkError.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/followRedirect.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/brotli-decompress.browser.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/decompression.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/index.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/concatArrayBuffer.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/EventPolyfill.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/ProgressEventPolyfill.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createEvent.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/findPropertySource.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/createProxy.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/isDomParserSupportedType.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/parseJson.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createResponse.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/getBodyByteLength.ts", "../../msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts", "../../msw/src/browser/setupWorker/start/createFallbackRequestListener.ts", "../../msw/src/browser/setupWorker/start/createFallbackStart.ts", "../../msw/src/browser/setupWorker/stop/createFallbackStop.ts", "../../msw/src/browser/setupWorker/setupWorker.ts", "../../msw/src/browser/utils/supportsReadableStreamTransfer.ts"], "sourcesContent": ["export function toResponseInit(response: Response): ResponseInit {\n  return {\n    status: response.status,\n    statusText: response.statusText,\n    headers: Object.fromEntries(response.headers.entries()),\n  }\n}\n", "import type { HandlerKind } from '../../handlers/common'\nimport type { RequestHandler } from '../../handlers/RequestHandler'\nimport type { WebSocketHandler } from '../../handlers/WebSocketHandler'\n\n/**\n * A filter function that ensures that the provided argument\n * is a handler of the given kind. This helps differentiate\n * between different kinds of handlers, e.g. request and event handlers.\n */\nexport function isHandlerKind<K extends HandlerKind>(kind: K) {\n  return (\n    input: unknown,\n  ): input is K extends 'EventHandler' ? WebSocketHandler : RequestHandler => {\n    return (\n      input != null &&\n      typeof input === 'object' &&\n      '__kind' in input &&\n      input.__kind === kind\n    )\n  }\n}\n", "/**\n * Determines if the given value is an object.\n */\nexport function isObject(value: any): boolean {\n  return value != null && typeof value === 'object' && !Array.isArray(value)\n}\n", "import { isObject } from './isObject'\n\n/**\n * Deeply merges two given objects with the right one\n * having a priority during property assignment.\n */\nexport function mergeRight(\n  left: Record<string, any>,\n  right: Record<string, any>,\n) {\n  return Object.entries(right).reduce(\n    (result, [key, rightValue]) => {\n      const leftValue = result[key]\n\n      if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n        result[key] = leftValue.concat(rightValue)\n        return result\n      }\n\n      if (isObject(leftValue) && isObject(rightValue)) {\n        result[key] = mergeRight(leftValue, rightValue)\n        return result\n      }\n\n      result[key] = rightValue\n      return result\n    },\n    Object.assign({}, left),\n  )\n}\n", "/**\n * Returns a boolean indicating whether the given global property\n * is defined and is configurable.\n */\nexport function hasConfigurableGlobal(propertyName: string): boolean {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName)\n\n  // The property is not set at all.\n  if (typeof descriptor === 'undefined') {\n    return false\n  }\n\n  // The property is set to a getter that returns undefined.\n  if (\n    typeof descriptor.get === 'function' &&\n    typeof descriptor.get() === 'undefined'\n  ) {\n    return false\n  }\n\n  // The property is set to a value equal to undefined.\n  if (typeof descriptor.get === 'undefined' && descriptor.value == null) {\n    return false\n  }\n\n  if (typeof descriptor.set === 'undefined' && !descriptor.configurable) {\n    console.error(\n      `[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`\n    )\n    return false\n  }\n\n  return true\n}\n", "type EventWithTarget<E extends Event, T> = E & { target: T }\n\nexport function bindEvent<E extends Event, T>(\n  target: T,\n  event: E\n): EventWithTarget<E, T> {\n  Object.defineProperties(event, {\n    target: {\n      value: target,\n      enumerable: true,\n      writable: true,\n    },\n    currentTarget: {\n      value: target,\n      enumerable: true,\n      writable: true,\n    },\n  })\n\n  return event as EventWithTarget<E, T>\n}\n", "const kCancelable = Symbol('kCancelable')\nconst kDefaultPrevented = Symbol('kDefaultPrevented')\n\n/**\n * A `MessageEvent` superset that supports event cancellation\n * in Node.js. It's rather non-intrusive so it can be safely\n * used in the browser as well.\n *\n * @see https://github.com/nodejs/node/issues/51767\n */\nexport class CancelableMessageEvent<T = any> extends MessageEvent<T> {\n  [kCancelable]: boolean;\n  [kDefaultPrevented]: boolean\n\n  constructor(type: string, init: MessageEventInit<T>) {\n    super(type, init)\n    this[kCancelable] = !!init.cancelable\n    this[kDefaultPrevented] = false\n  }\n\n  get cancelable() {\n    return this[kCancelable]\n  }\n\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable\n  }\n\n  get defaultPrevented() {\n    return this[kDefaultPrevented]\n  }\n\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented\n  }\n\n  public preventDefault(): void {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true\n    }\n  }\n}\n\ninterface CloseEventInit extends EventInit {\n  code?: number\n  reason?: string\n  wasClean?: boolean\n}\n\nexport class CloseEvent extends Event {\n  public code: number\n  public reason: string\n  public wasClean: boolean\n\n  constructor(type: string, init: CloseEventInit = {}) {\n    super(type, init)\n    this.code = init.code === undefined ? 0 : init.code\n    this.reason = init.reason === undefined ? '' : init.reason\n    this.wasClean = init.wasClean === undefined ? false : init.wasClean\n  }\n}\n\nexport class CancelableCloseEvent extends CloseEvent {\n  [kCancelable]: boolean;\n  [kDefaultPrevented]: boolean\n\n  constructor(type: string, init: CloseEventInit = {}) {\n    super(type, init)\n    this[kCancelable] = !!init.cancelable\n    this[kDefaultPrevented] = false\n  }\n\n  get cancelable() {\n    return this[kCancelable]\n  }\n\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable\n  }\n\n  get defaultPrevented() {\n    return this[kDefaultPrevented]\n  }\n\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented\n  }\n\n  public preventDefault(): void {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true\n    }\n  }\n}\n", "import type { WebSocketData, WebSocketTransport } from './WebSocketTransport'\nimport type { WebSocketEventListener } from './WebSocketOverride'\nimport { bindEvent } from './utils/bindEvent'\nimport { CancelableMessageEvent, CloseEvent } from './utils/events'\nimport { createRequestId } from '../../createRequestId'\n\nconst kEmitter = Symbol('kEmitter')\nconst kBoundListener = Symbol('kBoundListener')\n\nexport interface WebSocketClientEventMap {\n  message: MessageEvent<WebSocketData>\n  close: CloseEvent\n}\n\nexport abstract class WebSocketClientConnectionProtocol {\n  abstract id: string\n  abstract url: URL\n  public abstract send(data: WebSocketData): void\n  public abstract close(code?: number, reason?: string): void\n\n  public abstract addEventListener<\n    EventType extends keyof WebSocketClientEventMap\n  >(\n    type: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void\n\n  public abstract removeEventListener<\n    EventType extends keyof WebSocketClientEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void\n}\n\n/**\n * The WebSocket client instance represents an incoming\n * client connection. The user can control the connection,\n * send and receive events.\n */\nexport class WebSocketClientConnection\n  implements WebSocketClientConnectionProtocol\n{\n  public readonly id: string\n  public readonly url: URL\n\n  private [kEmitter]: EventTarget\n\n  constructor(\n    public readonly socket: WebSocket,\n    private readonly transport: WebSocketTransport\n  ) {\n    this.id = createRequestId()\n    this.url = new URL(socket.url)\n    this[kEmitter] = new EventTarget()\n\n    // Emit outgoing client data (\"ws.send()\") as \"message\"\n    // events on the \"client\" connection.\n    this.transport.addEventListener('outgoing', (event) => {\n      const message = bindEvent(\n        this.socket,\n        new CancelableMessageEvent('message', {\n          data: event.data,\n          origin: event.origin,\n          cancelable: true,\n        })\n      )\n\n      this[kEmitter].dispatchEvent(message)\n\n      // This is a bit silly but forward the cancellation state\n      // of the \"client\" message event to the \"outgoing\" transport event.\n      // This way, other agens (like \"server\" connection) can know\n      // whether the client listener has pervented the default.\n      if (message.defaultPrevented) {\n        event.preventDefault()\n      }\n    })\n\n    /**\n     * Emit the \"close\" event on the \"client\" connection\n     * whenever the underlying transport is closed.\n     * @note \"client.close()\" does NOT dispatch the \"close\"\n     * event on the WebSocket because it uses non-configurable\n     * close status code. Thus, we listen to the transport\n     * instead of the WebSocket's \"close\" event.\n     */\n    this.transport.addEventListener('close', (event) => {\n      this[kEmitter].dispatchEvent(\n        bindEvent(this.socket, new CloseEvent('close', event))\n      )\n    })\n  }\n\n  /**\n   * Listen for the outgoing events from the connected WebSocket client.\n   */\n  public addEventListener<EventType extends keyof WebSocketClientEventMap>(\n    type: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void {\n    if (!Reflect.has(listener, kBoundListener)) {\n      const boundListener = listener.bind(this.socket)\n\n      // Store the bound listener on the original listener\n      // so the exact bound function can be accessed in \"removeEventListener()\".\n      Object.defineProperty(listener, kBoundListener, {\n        value: boundListener,\n        enumerable: false,\n        configurable: false,\n      })\n    }\n\n    this[kEmitter].addEventListener(\n      type,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Removes the listener for the given event.\n   */\n  public removeEventListener<EventType extends keyof WebSocketClientEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void {\n    this[kEmitter].removeEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Send data to the connected client.\n   */\n  public send(data: WebSocketData): void {\n    this.transport.send(data)\n  }\n\n  /**\n   * Close the WebSocket connection.\n   * @param {number} code A status code (see https://www.rfc-editor.org/rfc/rfc6455#section-7.4.1).\n   * @param {string} reason A custom connection close reason.\n   */\n  public close(code?: number, reason?: string): void {\n    this.transport.close(code, reason)\n  }\n}\n", "import { invariant } from 'outvariant'\nimport {\n  kClose,\n  WebSocketEventListener,\n  WebSocketOverride,\n} from './WebSocketOverride'\nimport type { WebSocketData } from './WebSocketTransport'\nimport type { WebSocketClassTransport } from './WebSocketClassTransport'\nimport { bindEvent } from './utils/bindEvent'\nimport {\n  CancelableMessageEvent,\n  CancelableCloseEvent,\n  CloseEvent,\n} from './utils/events'\n\nconst kEmitter = Symbol('kEmitter')\nconst kBoundListener = Symbol('kBoundListener')\nconst kSend = Symbol('kSend')\n\nexport interface WebSocketServerEventMap {\n  open: Event\n  message: MessageEvent<WebSocketData>\n  error: Event\n  close: CloseEvent\n}\n\nexport abstract class WebSocketServerConnectionProtocol {\n  public abstract connect(): void\n  public abstract send(data: WebSocketData): void\n  public abstract close(): void\n\n  public abstract addEventListener<\n    EventType extends keyof WebSocketServerEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void\n\n  public abstract removeEventListener<\n    EventType extends keyof WebSocketServerEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void\n}\n\n/**\n * The WebSocket server instance represents the actual production\n * WebSocket server connection. It's idle by default but you can\n * establish it by calling `server.connect()`.\n */\nexport class WebSocketServerConnection\n  implements WebSocketServerConnectionProtocol\n{\n  /**\n   * A WebSocket instance connected to the original server.\n   */\n  private realWebSocket?: WebSocket\n  private mockCloseController: AbortController\n  private realCloseController: AbortController\n  private [kEmitter]: EventTarget\n\n  constructor(\n    private readonly client: WebSocketOverride,\n    private readonly transport: WebSocketClassTransport,\n    private readonly createConnection: () => WebSocket\n  ) {\n    this[kEmitter] = new EventTarget()\n    this.mockCloseController = new AbortController()\n    this.realCloseController = new AbortController()\n\n    // Automatically forward outgoing client events\n    // to the actual server unless the outgoing message event\n    // has been prevented. The \"outgoing\" transport event it\n    // dispatched by the \"client\" connection.\n    this.transport.addEventListener('outgoing', (event) => {\n      // Ignore client messages if the server connection\n      // hasn't been established yet. Nowhere to forward.\n      if (typeof this.realWebSocket === 'undefined') {\n        return\n      }\n\n      // Every outgoing client message can prevent this forwarding\n      // by preventing the default of the outgoing message event.\n      // This listener will be added before user-defined listeners,\n      // so execute the logic on the next tick.\n      queueMicrotask(() => {\n        if (!event.defaultPrevented) {\n          /**\n           * @note Use the internal send mechanism so consumers can tell\n           * apart direct user calls to `server.send()` and internal calls.\n           * E.g. MSW has to ignore this internal call to log out messages correctly.\n           */\n          this[kSend](event.data)\n        }\n      })\n    })\n\n    this.transport.addEventListener(\n      'incoming',\n      this.handleIncomingMessage.bind(this)\n    )\n  }\n\n  /**\n   * The `WebSocket` instance connected to the original server.\n   * Accessing this before calling `server.connect()` will throw.\n   */\n  public get socket(): WebSocket {\n    invariant(\n      this.realWebSocket,\n      'Cannot access \"socket\" on the original WebSocket server object: the connection is not open. Did you forget to call `server.connect()`?'\n    )\n\n    return this.realWebSocket\n  }\n\n  /**\n   * Open connection to the original WebSocket server.\n   */\n  public connect(): void {\n    invariant(\n      !this.realWebSocket || this.realWebSocket.readyState !== WebSocket.OPEN,\n      'Failed to call \"connect()\" on the original WebSocket instance: the connection already open'\n    )\n\n    const realWebSocket = this.createConnection()\n\n    // Inherit the binary type from the mock WebSocket client.\n    realWebSocket.binaryType = this.client.binaryType\n\n    // Allow the interceptor to listen to when the server connection\n    // has been established. This isn't necessary to operate with the connection\n    // but may be beneficial in some cases (like conditionally adding logging).\n    realWebSocket.addEventListener(\n      'open',\n      (event) => {\n        this[kEmitter].dispatchEvent(\n          bindEvent(this.realWebSocket!, new Event('open', event))\n        )\n      },\n      { once: true }\n    )\n\n    realWebSocket.addEventListener('message', (event) => {\n      // Dispatch the \"incoming\" transport event instead of\n      // invoking the internal handler directly. This way,\n      // anyone can listen to the \"incoming\" event but this\n      // class is the one resulting in it.\n      this.transport.dispatchEvent(\n        bindEvent(\n          this.realWebSocket!,\n          new MessageEvent('incoming', {\n            data: event.data,\n            origin: event.origin,\n          })\n        )\n      )\n    })\n\n    // Close the original connection when the mock client closes.\n    // E.g. \"client.close()\" was called. This is never forwarded anywhere.\n    this.client.addEventListener(\n      'close',\n      (event) => {\n        this.handleMockClose(event)\n      },\n      {\n        signal: this.mockCloseController.signal,\n      }\n    )\n\n    // Forward the \"close\" event to let the interceptor handle\n    // closures initiated by the original server.\n    realWebSocket.addEventListener(\n      'close',\n      (event) => {\n        this.handleRealClose(event)\n      },\n      {\n        signal: this.realCloseController.signal,\n      }\n    )\n\n    realWebSocket.addEventListener('error', () => {\n      const errorEvent = bindEvent(\n        realWebSocket,\n        new Event('error', { cancelable: true })\n      )\n\n      // Emit the \"error\" event on the `server` connection\n      // to let the interceptor react to original server errors.\n      this[kEmitter].dispatchEvent(errorEvent)\n\n      // If the error event from the original server hasn't been prevented,\n      // forward it to the underlying client.\n      if (!errorEvent.defaultPrevented) {\n        this.client.dispatchEvent(bindEvent(this.client, new Event('error')))\n      }\n    })\n\n    this.realWebSocket = realWebSocket\n  }\n\n  /**\n   * Listen for the incoming events from the original WebSocket server.\n   */\n  public addEventListener<EventType extends keyof WebSocketServerEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void {\n    if (!Reflect.has(listener, kBoundListener)) {\n      const boundListener = listener.bind(this.client)\n\n      // Store the bound listener on the original listener\n      // so the exact bound function can be accessed in \"removeEventListener()\".\n      Object.defineProperty(listener, kBoundListener, {\n        value: boundListener,\n        enumerable: false,\n      })\n    }\n\n    this[kEmitter].addEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Remove the listener for the given event.\n   */\n  public removeEventListener<EventType extends keyof WebSocketServerEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void {\n    this[kEmitter].removeEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Send data to the original WebSocket server.\n   * @example\n   * server.send('hello')\n   * server.send(new Blob(['hello']))\n   * server.send(new TextEncoder().encode('hello'))\n   */\n  public send(data: WebSocketData): void {\n    this[kSend](data)\n  }\n\n  private [kSend](data: WebSocketData): void {\n    const { realWebSocket } = this\n\n    invariant(\n      realWebSocket,\n      'Failed to call \"server.send()\" for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?',\n      this.client.url\n    )\n\n    // Silently ignore writes on the closed original WebSocket.\n    if (\n      realWebSocket.readyState === WebSocket.CLOSING ||\n      realWebSocket.readyState === WebSocket.CLOSED\n    ) {\n      return\n    }\n\n    // Delegate the send to when the original connection is open.\n    // Unlike the mock, connecting to the original server may take time\n    // so we cannot call this on the next tick.\n    if (realWebSocket.readyState === WebSocket.CONNECTING) {\n      realWebSocket.addEventListener(\n        'open',\n        () => {\n          realWebSocket.send(data)\n        },\n        { once: true }\n      )\n      return\n    }\n\n    // Send the data to the original WebSocket server.\n    realWebSocket.send(data)\n  }\n\n  /**\n   * Close the actual server connection.\n   */\n  public close(): void {\n    const { realWebSocket } = this\n\n    invariant(\n      realWebSocket,\n      'Failed to close server connection for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?',\n      this.client.url\n    )\n\n    // Remove the \"close\" event listener from the server\n    // so it doesn't close the underlying WebSocket client\n    // when you call \"server.close()\". This also prevents the\n    // `close` event on the `server` connection from being dispatched twice.\n    this.realCloseController.abort()\n\n    if (\n      realWebSocket.readyState === WebSocket.CLOSING ||\n      realWebSocket.readyState === WebSocket.CLOSED\n    ) {\n      return\n    }\n\n    // Close the actual client connection.\n    realWebSocket.close()\n\n    // Dispatch the \"close\" event on the `server` connection.\n    queueMicrotask(() => {\n      this[kEmitter].dispatchEvent(\n        bindEvent(\n          this.realWebSocket,\n          new CancelableCloseEvent('close', {\n            /**\n             * @note `server.close()` in the interceptor\n             * always results in clean closures.\n             */\n            code: 1000,\n            cancelable: true,\n          })\n        )\n      )\n    })\n  }\n\n  private handleIncomingMessage(event: MessageEvent<WebSocketData>): void {\n    // Clone the event to dispatch it on this class\n    // once again and prevent the \"already being dispatched\"\n    // exception. Clone it here so we can observe this event\n    // being prevented in the \"server.on()\" listeners.\n    const messageEvent = bindEvent(\n      event.target,\n      new CancelableMessageEvent('message', {\n        data: event.data,\n        origin: event.origin,\n        cancelable: true,\n      })\n    )\n\n    /**\n     * @note Emit \"message\" event on the server connection\n     * instance to let the interceptor know about these\n     * incoming events from the original server. In that listener,\n     * the interceptor can modify or skip the event forwarding\n     * to the mock WebSocket instance.\n     */\n    this[kEmitter].dispatchEvent(messageEvent)\n\n    /**\n     * @note Forward the incoming server events to the client.\n     * Preventing the default on the message event stops this.\n     */\n    if (!messageEvent.defaultPrevented) {\n      this.client.dispatchEvent(\n        bindEvent(\n          /**\n           * @note Bind the forwarded original server events\n           * to the mock WebSocket instance so it would\n           * dispatch them straight away.\n           */\n          this.client,\n          // Clone the message event again to prevent\n          // the \"already being dispatched\" exception.\n          new MessageEvent('message', {\n            data: event.data,\n            origin: event.origin,\n          })\n        )\n      )\n    }\n  }\n\n  private handleMockClose(_event: Event): void {\n    // Close the original connection if the mock client closes.\n    if (this.realWebSocket) {\n      this.realWebSocket.close()\n    }\n  }\n\n  private handleRealClose(event: CloseEvent): void {\n    // For closures originating from the original server,\n    // remove the \"close\" listener from the mock client.\n    // original close -> (?) client[kClose]() --X--> \"close\" (again).\n    this.mockCloseController.abort()\n\n    const closeEvent = bindEvent(\n      this.realWebSocket,\n      new CancelableCloseEvent('close', {\n        code: event.code,\n        reason: event.reason,\n        wasClean: event.wasClean,\n        cancelable: true,\n      })\n    )\n\n    this[kEmitter].dispatchEvent(closeEvent)\n\n    // If the close event from the server hasn't been prevented,\n    // forward the closure to the mock client.\n    if (!closeEvent.defaultPrevented) {\n      // Close the intercepted client forcefully to\n      // allow non-configurable status codes from the server.\n      // If the socket has been closed by now, no harm calling\n      // this again—it will have no effect.\n      this.client[kClose](event.code, event.reason)\n    }\n  }\n}\n", "import { invariant } from 'outvariant'\nimport type { WebSocketData } from './WebSocketTransport'\nimport { bindEvent } from './utils/bindEvent'\nimport { CloseEvent } from './utils/events'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\n\nexport type WebSocketEventListener<\n  EventType extends WebSocketEventMap[keyof WebSocketEventMap] = Event\n> = (this: WebSocket, event: EventType) => void\n\nconst WEBSOCKET_CLOSE_CODE_RANGE_ERROR =\n  'InvalidAccessError: close code out of user configurable range'\n\nexport const kPassthroughPromise = Symbol('kPassthroughPromise')\nexport const kOnSend = Symbol('kOnSend')\nexport const kClose = Symbol('kClose')\n\nexport class WebSocketOverride extends EventTarget implements WebSocket {\n  static readonly CONNECTING = 0\n  static readonly OPEN = 1\n  static readonly CLOSING = 2\n  static readonly CLOSED = 3\n  readonly CONNECTING = 0\n  readonly OPEN = 1\n  readonly CLOSING = 2\n  readonly CLOSED = 3\n\n  public url: string\n  public protocol: string\n  public extensions: string\n  public binaryType: BinaryType\n  public readyState: number\n  public bufferedAmount: number\n\n  private _onopen: WebSocketEventListener | null = null\n  private _onmessage: WebSocketEventListener<\n    MessageEvent<WebSocketData>\n  > | null = null\n  private _onerror: WebSocketEventListener | null = null\n  private _onclose: WebSocketEventListener<CloseEvent> | null = null\n\n  private [kPassthroughPromise]: DeferredPromise<boolean>\n  private [kOnSend]?: (data: WebSocketData) => void\n\n  constructor(url: string | URL, protocols?: string | Array<string>) {\n    super()\n    this.url = url.toString()\n    this.protocol = ''\n    this.extensions = ''\n    this.binaryType = 'blob'\n    this.readyState = this.CONNECTING\n    this.bufferedAmount = 0\n\n    this[kPassthroughPromise] = new DeferredPromise<boolean>()\n\n    queueMicrotask(async () => {\n      if (await this[kPassthroughPromise]) {\n        return\n      }\n\n      this.protocol =\n        typeof protocols === 'string'\n          ? protocols\n          : Array.isArray(protocols) && protocols.length > 0\n          ? protocols[0]\n          : ''\n\n      /**\n       * @note Check that nothing has prevented this connection\n       * (e.g. called `client.close()` in the connection listener).\n       * If the connection has been prevented, never dispatch the open event,.\n       */\n      if (this.readyState === this.CONNECTING) {\n        this.readyState = this.OPEN\n        this.dispatchEvent(bindEvent(this, new Event('open')))\n      }\n    })\n  }\n\n  set onopen(listener: WebSocketEventListener | null) {\n    this.removeEventListener('open', this._onopen)\n    this._onopen = listener\n    if (listener !== null) {\n      this.addEventListener('open', listener)\n    }\n  }\n  get onopen(): WebSocketEventListener | null {\n    return this._onopen\n  }\n\n  set onmessage(\n    listener: WebSocketEventListener<MessageEvent<WebSocketData>> | null\n  ) {\n    this.removeEventListener(\n      'message',\n      this._onmessage as WebSocketEventListener\n    )\n    this._onmessage = listener\n    if (listener !== null) {\n      this.addEventListener('message', listener)\n    }\n  }\n  get onmessage(): WebSocketEventListener<MessageEvent<WebSocketData>> | null {\n    return this._onmessage\n  }\n\n  set onerror(listener: WebSocketEventListener | null) {\n    this.removeEventListener('error', this._onerror)\n    this._onerror = listener\n    if (listener !== null) {\n      this.addEventListener('error', listener)\n    }\n  }\n  get onerror(): WebSocketEventListener | null {\n    return this._onerror\n  }\n\n  set onclose(listener: WebSocketEventListener<CloseEvent> | null) {\n    this.removeEventListener('close', this._onclose as WebSocketEventListener)\n    this._onclose = listener\n    if (listener !== null) {\n      this.addEventListener('close', listener)\n    }\n  }\n  get onclose(): WebSocketEventListener<CloseEvent> | null {\n    return this._onclose\n  }\n\n  /**\n   * @see https://websockets.spec.whatwg.org/#ref-for-dom-websocket-send%E2%91%A0\n   */\n  public send(data: WebSocketData): void {\n    if (this.readyState === this.CONNECTING) {\n      this.close()\n      throw new DOMException('InvalidStateError')\n    }\n\n    // Sending when the socket is about to close\n    // discards the sent data.\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return\n    }\n\n    // Buffer the data to send in this even loop\n    // but send it in the next.\n    this.bufferedAmount += getDataSize(data)\n\n    queueMicrotask(() => {\n      // This is a bit optimistic but since no actual data transfer\n      // is involved, all the data will be \"sent\" on the next tick.\n      this.bufferedAmount = 0\n\n      /**\n       * @note Notify the parent about outgoing data.\n       * This notifies the transport and the connection\n       * listens to the outgoing data to emit the \"message\" event.\n       */\n      this[kOnSend]?.(data)\n    })\n  }\n\n  public close(code: number = 1000, reason?: string): void {\n    invariant(code, WEBSOCKET_CLOSE_CODE_RANGE_ERROR)\n    invariant(\n      code === 1000 || (code >= 3000 && code <= 4999),\n      WEBSOCKET_CLOSE_CODE_RANGE_ERROR\n    )\n\n    this[kClose](code, reason)\n  }\n\n  private [kClose](\n    code: number = 1000,\n    reason?: string,\n    wasClean = true\n  ): void {\n    /**\n     * @note Move this check here so that even internall closures,\n     * like those triggered by the `server` connection, are not\n     * performed twice.\n     */\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return\n    }\n\n    this.readyState = this.CLOSING\n\n    queueMicrotask(() => {\n      this.readyState = this.CLOSED\n\n      this.dispatchEvent(\n        bindEvent(\n          this,\n          new CloseEvent('close', {\n            code,\n            reason,\n            wasClean,\n          })\n        )\n      )\n\n      // Remove all event listeners once the socket is closed.\n      this._onopen = null\n      this._onmessage = null\n      this._onerror = null\n      this._onclose = null\n    })\n  }\n\n  public addEventListener<K extends keyof WebSocketEventMap>(\n    type: K,\n    listener: (this: WebSocket, event: WebSocketEventMap[K]) => void,\n    options?: boolean | AddEventListenerOptions\n  ): void\n  public addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ): void\n  public addEventListener(\n    type: unknown,\n    listener: unknown,\n    options?: unknown\n  ): void {\n    return super.addEventListener(\n      type as string,\n      listener as EventListener,\n      options as AddEventListenerOptions\n    )\n  }\n\n  removeEventListener<K extends keyof WebSocketEventMap>(\n    type: K,\n    callback: EventListenerOrEventListenerObject | null,\n    options?: boolean | EventListenerOptions\n  ): void {\n    return super.removeEventListener(type, callback, options)\n  }\n}\n\nfunction getDataSize(data: WebSocketData): number {\n  if (typeof data === 'string') {\n    return data.length\n  }\n\n  if (data instanceof Blob) {\n    return data.size\n  }\n\n  return data.byteLength\n}\n", "import { bindEvent } from './utils/bindEvent'\nimport {\n  StrictEventListenerOrEventListenerObject,\n  WebSocketData,\n  WebSocketTransport,\n  WebSocketTransportEventMap,\n} from './WebSocketTransport'\nimport { kOnSend, kClose, WebSocketOverride } from './WebSocketOverride'\nimport { CancelableMessageEvent, CloseEvent } from './utils/events'\n\n/**\n * Abstraction over the given mock `WebSocket` instance that allows\n * for controlling that instance (e.g. sending and receiving messages).\n */\nexport class WebSocketClassTransport\n  extends EventTarget\n  implements WebSocketTransport\n{\n  constructor(protected readonly socket: WebSocketOverride) {\n    super()\n\n    // Emit the \"close\" event on the transport if the close\n    // originates from the WebSocket client. E.g. the application\n    // calls \"ws.close()\", not the interceptor.\n    this.socket.addEventListener('close', (event) => {\n      this.dispatchEvent(bindEvent(this.socket, new CloseEvent('close', event)))\n    })\n\n    /**\n     * Emit the \"outgoing\" event on the transport\n     * whenever the WebSocket client sends data (\"ws.send()\").\n     */\n    this.socket[kOnSend] = (data) => {\n      this.dispatchEvent(\n        bindEvent(\n          this.socket,\n          // Dispatch this as cancelable because \"client\" connection\n          // re-creates this message event (cannot dispatch the same event).\n          new CancelableMessageEvent('outgoing', {\n            data,\n            origin: this.socket.url,\n            cancelable: true,\n          })\n        )\n      )\n    }\n  }\n\n  public addEventListener<EventType extends keyof WebSocketTransportEventMap>(\n    type: EventType,\n    callback: StrictEventListenerOrEventListenerObject<\n      WebSocketTransportEventMap[EventType]\n    > | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    return super.addEventListener(type, callback as EventListener, options)\n  }\n\n  public dispatchEvent<EventType extends keyof WebSocketTransportEventMap>(\n    event: WebSocketTransportEventMap[EventType]\n  ): boolean {\n    return super.dispatchEvent(event)\n  }\n\n  public send(data: WebSocketData): void {\n    queueMicrotask(() => {\n      if (\n        this.socket.readyState === this.socket.CLOSING ||\n        this.socket.readyState === this.socket.CLOSED\n      ) {\n        return\n      }\n\n      const dispatchEvent = () => {\n        this.socket.dispatchEvent(\n          bindEvent(\n            /**\n             * @note Setting this event's \"target\" to the\n             * WebSocket override instance is important.\n             * This way it can tell apart original incoming events\n             * (must be forwarded to the transport) from the\n             * mocked message events like the one below\n             * (must be dispatched on the client instance).\n             */\n            this.socket,\n            new MessageEvent('message', {\n              data,\n              origin: this.socket.url,\n            })\n          )\n        )\n      }\n\n      if (this.socket.readyState === this.socket.CONNECTING) {\n        this.socket.addEventListener(\n          'open',\n          () => {\n            dispatchEvent()\n          },\n          { once: true }\n        )\n      } else {\n        dispatchEvent()\n      }\n    })\n  }\n\n  public close(code: number, reason?: string): void {\n    /**\n     * @note Call the internal close method directly\n     * to allow closing the connection with the status codes\n     * that are non-configurable by the user (> 1000 <= 1015).\n     */\n    this.socket[kClose](code, reason)\n  }\n}\n", "import { Interceptor } from '../../Interceptor'\nimport {\n  WebSocketClientConnectionProtocol,\n  WebSocketClientConnection,\n  type WebSocketClientEventMap,\n} from './WebSocketClientConnection'\nimport {\n  WebSocketServerConnectionProtocol,\n  WebSocketServerConnection,\n  type WebSocketServerEventMap,\n} from './WebSocketServerConnection'\nimport { WebSocketClassTransport } from './WebSocketClassTransport'\nimport {\n  kClose,\n  kPassthroughPromise,\n  WebSocketOverride,\n} from './WebSocketOverride'\nimport { bindEvent } from './utils/bindEvent'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\n\nexport { type WebSocketData, WebSocketTransport } from './WebSocketTransport'\nexport {\n  WebSocketClientEventMap,\n  WebSocketClientConnectionProtocol,\n  WebSocketClientConnection,\n  WebSocketServerEventMap,\n  WebSocketServerConnectionProtocol,\n  WebSocketServerConnection,\n}\n\nexport {\n  CloseEvent,\n  CancelableCloseEvent,\n  CancelableMessageEvent,\n} from './utils/events'\n\nexport type WebSocketEventMap = {\n  connection: [args: WebSocketConnectionData]\n}\n\nexport type WebSocketConnectionData = {\n  /**\n   * The incoming WebSocket client connection.\n   */\n  client: WebSocketClientConnection\n\n  /**\n   * The original WebSocket server connection.\n   */\n  server: WebSocketServerConnection\n\n  /**\n   * The connection information.\n   */\n  info: {\n    /**\n     * The protocols supported by the WebSocket client.\n     */\n    protocols: string | Array<string> | undefined\n  }\n}\n\n/**\n * Intercept the outgoing WebSocket connections created using\n * the global `WebSocket` class.\n */\nexport class WebSocketInterceptor extends Interceptor<WebSocketEventMap> {\n  static symbol = Symbol('websocket')\n\n  constructor() {\n    super(WebSocketInterceptor.symbol)\n  }\n\n  protected checkEnvironment(): boolean {\n    return hasConfigurableGlobal('WebSocket')\n  }\n\n  protected setup(): void {\n    const originalWebSocketDescriptor = Object.getOwnPropertyDescriptor(\n      globalThis,\n      'WebSocket'\n    )\n\n    const WebSocketProxy = new Proxy(globalThis.WebSocket, {\n      construct: (\n        target,\n        args: ConstructorParameters<typeof globalThis.WebSocket>,\n        newTarget\n      ) => {\n        const [url, protocols] = args\n\n        const createConnection = (): WebSocket => {\n          return Reflect.construct(target, args, newTarget)\n        }\n\n        // All WebSocket instances are mocked and don't forward\n        // any events to the original server (no connection established).\n        // To forward the events, the user must use the \"server.send()\" API.\n        const socket = new WebSocketOverride(url, protocols)\n        const transport = new WebSocketClassTransport(socket)\n\n        // Emit the \"connection\" event to the interceptor on the next tick\n        // so the client can modify WebSocket options, like \"binaryType\"\n        // while the connection is already pending.\n        queueMicrotask(() => {\n          try {\n            const server = new WebSocketServerConnection(\n              socket,\n              transport,\n              createConnection\n            )\n\n            // The \"globalThis.WebSocket\" class stands for\n            // the client-side connection. Assume it's established\n            // as soon as the WebSocket instance is constructed.\n            const hasConnectionListeners = this.emitter.emit('connection', {\n              client: new WebSocketClientConnection(socket, transport),\n              server,\n              info: {\n                protocols,\n              },\n            })\n\n            if (hasConnectionListeners) {\n              socket[kPassthroughPromise].resolve(false)\n            } else {\n              socket[kPassthroughPromise].resolve(true)\n\n              server.connect()\n\n              // Forward the \"open\" event from the original server\n              // to the mock WebSocket client in the case of a passthrough connection.\n              server.addEventListener('open', () => {\n                socket.dispatchEvent(bindEvent(socket, new Event('open')))\n\n                // Forward the original connection protocol to the\n                // mock WebSocket client.\n                if (server['realWebSocket']) {\n                  socket.protocol = server['realWebSocket'].protocol\n                }\n              })\n            }\n          } catch (error) {\n            /**\n             * @note Translate unhandled exceptions during the connection\n             * handling (i.e. interceptor exceptions) as WebSocket connection\n             * closures with error. This prevents from the exceptions occurring\n             * in `queueMicrotask` from being process-wide and uncatchable.\n             */\n            if (error instanceof Error) {\n              socket.dispatchEvent(new Event('error'))\n\n              // No need to close the connection if it's already being closed.\n              // E.g. the interceptor called `client.close()` and then threw an error.\n              if (\n                socket.readyState !== WebSocket.CLOSING &&\n                socket.readyState !== WebSocket.CLOSED\n              ) {\n                socket[kClose](1011, error.message, false)\n              }\n\n              console.error(error)\n            }\n          }\n        })\n\n        return socket\n      },\n    })\n\n    Object.defineProperty(globalThis, 'WebSocket', {\n      value: WebSocketProxy,\n      configurable: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(\n        globalThis,\n        'WebSocket',\n        originalWebSocketDescriptor!\n      )\n    })\n  }\n}\n", "import { WebSocketInterceptor } from '@mswjs/interceptors/WebSocket'\n\nexport const webSocketInterceptor = new WebSocketInterceptor()\n", "import type { WebSocketConnectionData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { WebSocketHandler } from '../handlers/WebSocketHandler'\nimport { webSocketInterceptor } from './webSocketInterceptor'\nimport {\n  onUnhandledRequest,\n  UnhandledRequestStrategy,\n} from '../utils/request/onUnhandledRequest'\nimport { isHandlerKind } from '../utils/internal/isHandlerKind'\n\ninterface HandleWebSocketEventOptions {\n  getUnhandledRequestStrategy: () => UnhandledRequestStrategy\n  getHandlers: () => Array<RequestHandler | WebSocketHandler>\n  onMockedConnection: (connection: WebSocketConnectionData) => void\n  onPassthroughConnection: (onnection: WebSocketConnectionData) => void\n}\n\nexport function handleWebSocketEvent(options: HandleWebSocketEventOptions) {\n  webSocketInterceptor.on('connection', async (connection) => {\n    const handlers = options.getHandlers().filter(isHandlerKind('EventHandler'))\n\n    // Ignore this connection if the user hasn't defined any handlers.\n    if (handlers.length > 0) {\n      options?.onMockedConnection(connection)\n\n      await Promise.all(\n        handlers.map((handler) => {\n          // Iterate over the handlers and forward the connection\n          // event to WebSocket event handlers. This is equivalent\n          // to dispatching that event onto multiple listeners.\n          return handler.run(connection)\n        }),\n      )\n\n      return\n    }\n\n    // Construct a request representing this WebSocket connection.\n    const request = new Request(connection.client.url, {\n      headers: {\n        upgrade: 'websocket',\n        connection: 'upgrade',\n      },\n    })\n    await onUnhandledRequest(\n      request,\n      options.getUnhandledRequestStrategy(),\n    ).catch((error) => {\n      const errorEvent = new Event('error')\n      Object.defineProperty(errorEvent, 'cause', {\n        enumerable: true,\n        configurable: false,\n        value: error,\n      })\n      connection.client.socket.dispatchEvent(errorEvent)\n    })\n\n    options?.onPassthroughConnection(connection)\n\n    // If none of the \"ws\" handlers matched,\n    // establish the WebSocket connection as-is.\n    connection.server.connect()\n  })\n}\n", "import type { WebSocketData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\n\n/**\n * Returns the byte length of the given WebSocket message.\n * @example\n * getMessageLength('hello') // 5\n * getMessageLength(new Blob(['hello'])) // 5\n */\nexport function getMessageLength(data: WebSocketData): number {\n  if (data instanceof Blob) {\n    return data.size\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return data.byteLength\n  }\n\n  return new Blob([data as any]).size\n}\n", "const MAX_LENGTH = 24\n\nexport function truncateMessage(message: string): string {\n  if (message.length <= MAX_LENGTH) {\n    return message\n  }\n\n  return `${message.slice(0, MAX_LENGTH)}…`\n}\n", "import { WebSocketData } from '@mswjs/interceptors/WebSocket'\nimport { truncateMessage } from './truncateMessage'\n\nexport async function getPublicData(data: WebSocketData): Promise<string> {\n  if (data instanceof Blob) {\n    const text = await data.text()\n    return `Blob(${truncateMessage(text)})`\n  }\n\n  // Handle all ArrayBuffer-like objects.\n  if (typeof data === 'object' && 'byteLength' in data) {\n    const text = new TextDecoder().decode(data as ArrayBuffer)\n    return `ArrayBuffer(${truncateMessage(text)})`\n  }\n\n  return truncateMessage(data)\n}\n", "import type {\n  WebSocketClientConnection,\n  WebSocketConnectionData,\n  WebSocketData,\n} from '@mswjs/interceptors/WebSocket'\nimport { devUtils } from '../../utils/internal/devUtils'\nimport { getTimestamp } from '../../utils/logging/getTimestamp'\nimport { toPublicUrl } from '../../utils/request/toPublicUrl'\nimport { getMessageLength } from './getMessageLength'\nimport { getPublicData } from './getPublicData'\n\nconst colors = {\n  system: '#3b82f6',\n  outgoing: '#22c55e',\n  incoming: '#ef4444',\n  mocked: '#ff6a33',\n}\n\nexport function attachWebSocketLogger(\n  connection: WebSocketConnectionData,\n): void {\n  const { client, server } = connection\n\n  logConnectionOpen(client)\n\n  // Log the events sent from the WebSocket client.\n  // WebSocket client connection object is written from the\n  // server's perspective so these message events are outgoing.\n  /**\n   * @todo Provide the reference to the exact event handler\n   * that called this `client.send()`.\n   */\n  client.addEventListener('message', (event) => {\n    logOutgoingClientMessage(event)\n  })\n\n  client.addEventListener('close', (event) => {\n    logConnectionClose(event)\n  })\n\n  // Log client errors (connection closures due to errors).\n  client.socket.addEventListener('error', (event) => {\n    logClientError(event)\n  })\n\n  client.send = new Proxy(client.send, {\n    apply(target, thisArg, args) {\n      const [data] = args\n      const messageEvent = new MessageEvent('message', { data })\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: client.socket,\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: client.socket,\n        },\n      })\n\n      queueMicrotask(() => {\n        logIncomingMockedClientMessage(messageEvent)\n      })\n\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n\n  server.addEventListener(\n    'open',\n    () => {\n      server.addEventListener('message', (event) => {\n        logIncomingServerMessage(event)\n      })\n    },\n    { once: true },\n  )\n\n  // Log outgoing client events initiated by the event handler.\n  // The actual client never sent these but the handler did.\n  server.send = new Proxy(server.send, {\n    apply(target, thisArg, args) {\n      const [data] = args\n      const messageEvent = new MessageEvent('message', { data })\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: server.socket,\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: server.socket,\n        },\n      })\n\n      logOutgoingMockedClientMessage(messageEvent)\n\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n}\n\n/**\n * Prints the WebSocket connection.\n * This is meant to be logged by every WebSocket handler\n * that intercepted this connection. This helps you see\n * what handlers observe this connection.\n */\nexport function logConnectionOpen(client: WebSocketClientConnection) {\n  const publicUrl = toPublicUrl(client.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(`${getTimestamp()} %c▶%c ${publicUrl}`),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log('Client:', client.socket)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nfunction logConnectionClose(event: CloseEvent) {\n  const target = event.target as WebSocket\n  const publicUrl = toPublicUrl(target.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c■%c ${publicUrl}`,\n    ),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nfunction logClientError(event: Event) {\n  const socket = event.target as WebSocket\n  const publicUrl = toPublicUrl(socket.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c\\u00D7%c ${publicUrl}`,\n    ),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message.\n */\nasync function logOutgoingClientMessage(event: MessageEvent<WebSocketData>) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n  const arrow = event.defaultPrevented ? '⇡' : '⬆'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c${arrow}%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.outgoing}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message initiated\n * by `server.send()` in the event handler.\n */\nasync function logOutgoingMockedClientMessage(\n  event: MessageEvent<WebSocketData>,\n) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c⬆%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.mocked}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message initiated\n * by `client.send()` in the event handler.\n */\nasync function logIncomingMockedClientMessage(\n  event: MessageEvent<WebSocketData>,\n) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c⬇%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.mocked}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nasync function logIncomingServerMessage(event: MessageEvent<WebSocketData>) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n  const arrow = event.defaultPrevented ? '⇣' : '⬇'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c${arrow}%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.incoming}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n", "const POSITIONALS_EXP = /(%?)(%([sdijo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage =\n      positionals.length === 0 ? message : format(message, ...positionals)\n    let error: Error\n\n    try {\n      error = Reflect.construct(ErrorConstructor as CustomErrorConstructor, [\n        formatMessage,\n      ])\n    } catch (err) {\n      error = (ErrorConstructor as CustomErrorFactory)(formatMessage)\n    }\n\n    throw error\n  }\n}\n", "/**\n * Determines if the current process is a Node.js process.\n */\nexport function isNodeProcess(): boolean {\n  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    return true\n  }\n\n  if (typeof process !== 'undefined') {\n    // Electron (https://www.electronjs.org/docs/latest/api/process#processtype-readonly)\n    const type = (process as any).type\n    if (type === 'renderer' || type === 'worker') {\n      return false\n    }\n\n\n    return !!(\n      process.versions &&\n      process.versions.node\n    )\n  }\n\n  return false\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { getWorkerInstance } from './utils/getWorkerInstance'\nimport { enableMocking } from './utils/enableMocking'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { createRequestListener } from './createRequestListener'\nimport { checkWorkerIntegrity } from '../../utils/checkWorkerIntegrity'\nimport { createResponseListener } from './createResponseListener'\nimport { validateWorkerScope } from './utils/validateWorkerScope'\n\nexport const createStartHandler = (\n  context: SetupWorkerInternalContext,\n): StartHandler => {\n  return function start(options, customOptions) {\n    const startWorkerInstance = async () => {\n      // Remove all previously existing event listeners.\n      // This way none of the listeners persists between Fast refresh\n      // of the application's code.\n      context.events.removeAllListeners()\n\n      // Handle requests signaled by the worker.\n      context.workerChannel.on(\n        'REQUEST',\n        createRequestListener(context, options),\n      )\n\n      // Handle responses signaled by the worker.\n      context.workerChannel.on('RESPONSE', createResponseListener(context))\n\n      const instance = await getWorkerInstance(\n        options.serviceWorker.url,\n        options.serviceWorker.options,\n        options.findWorker,\n      )\n\n      const [worker, registration] = instance\n\n      if (!worker) {\n        const missingWorkerMessage = customOptions?.findWorker\n          ? devUtils.formatMessage(\n              `Failed to locate the Service Worker registration using a custom \"findWorker\" predicate.\n\nPlease ensure that the custom predicate properly locates the Service Worker registration at \"%s\".\nMore details: https://mswjs.io/docs/api/setup-worker/start#findworker\n`,\n              options.serviceWorker.url,\n            )\n          : devUtils.formatMessage(\n              `Failed to locate the Service Worker registration.\n\nThis most likely means that the worker script URL \"%s\" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.\n\nPlease consider using a custom \"serviceWorker.url\" option to point to the actual worker script location, or a custom \"findWorker\" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`,\n              options.serviceWorker.url,\n              location.host,\n            )\n\n        throw new Error(missingWorkerMessage)\n      }\n\n      context.worker = worker\n      context.registration = registration\n\n      context.events.addListener(window, 'beforeunload', () => {\n        if (worker.state !== 'redundant') {\n          // Notify the Service Worker that this client has closed.\n          // Internally, it's similar to disabling the mocking, only\n          // client close event has a handler that self-terminates\n          // the Service Worker when there are no open clients.\n          context.workerChannel.send('CLIENT_CLOSED')\n        }\n        // Make sure we're always clearing the interval - there are reports that not doing this can\n        // cause memory leaks in headless browser environments.\n        window.clearInterval(context.keepAliveInterval)\n\n        // Notify others about this client disconnecting.\n        // E.g. this will purge the in-memory WebSocket clients since\n        // starting the worker again will assign them new IDs.\n        window.postMessage({ type: 'msw/worker:stop' })\n      })\n\n      // Check if the active Service Worker has been generated\n      // by the currently installed version of MSW.\n      await checkWorkerIntegrity(context).catch((error) => {\n        devUtils.error(\n          'Error while checking the worker script integrity. Please report this on GitHub (https://github.com/mswjs/msw/issues), including the original error below.',\n        )\n        // eslint-disable-next-line no-console\n        console.error(error)\n      })\n\n      context.keepAliveInterval = window.setInterval(\n        () => context.workerChannel.send('KEEPALIVE_REQUEST'),\n        5000,\n      )\n\n      // Warn the user when loading the page that lies outside\n      // of the worker's scope.\n      validateWorkerScope(registration, context.startOptions)\n\n      return registration\n    }\n\n    const workerRegistration = startWorkerInstance().then(\n      async (registration) => {\n        const pendingInstance = registration.installing || registration.waiting\n\n        // Wait until the worker is activated.\n        // Assume the worker is already activated if there's no pending registration\n        // (i.e. when reloading the page after a successful activation).\n        if (pendingInstance) {\n          await new Promise<void>((resolve) => {\n            pendingInstance.addEventListener('statechange', () => {\n              if (pendingInstance.state === 'activated') {\n                return resolve()\n              }\n            })\n          })\n        }\n\n        // Print the activation message only after the worker has been activated.\n        await enableMocking(context, options).catch((error) => {\n          throw new Error(`Failed to enable mocking: ${error?.message}`)\n        })\n\n        return registration\n      },\n    )\n\n    return workerRegistration\n  }\n}\n", "export type AsyncTuple<\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n> =\n  | {\n      error: ErrorType\n      data: null\n    }\n  | { error: null; data: DataType }\n\n/**\n * Gracefully handles a given Promise factory.\n * @example\n * const { error, data } = await until(() => asyncAction())\n */\nexport const until = async <\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n>(\n  promise: () => Promise<DataType>,\n): Promise<AsyncTuple<ErrorType, DataType>> => {\n  try {\n    const data = await promise().catch((error) => {\n      throw error\n    })\n    return { error: null, data }\n  } catch (error) {\n    return { error, data: null }\n  }\n}\n", "import { until } from '@open-draft/until'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { getAbsoluteWorkerUrl } from '../../../utils/getAbsoluteWorkerUrl'\nimport { getWorkerByRegistration } from './getWorkerByRegistration'\nimport { ServiceWorkerInstanceTuple, FindWorker } from '../../glossary'\n\n/**\n * Returns an active Service Worker instance.\n * When not found, registers a new Service Worker.\n */\nexport const getWorkerInstance = async (\n  url: string,\n  options: RegistrationOptions = {},\n  findWorker: FindWorker,\n): Promise<ServiceWorkerInstanceTuple> => {\n  // Resolve the absolute Service Worker URL.\n  const absoluteWorkerUrl = getAbsoluteWorkerUrl(url)\n\n  const mockRegistrations = await navigator.serviceWorker\n    .getRegistrations()\n    .then((registrations) =>\n      registrations.filter((registration) =>\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n      ),\n    )\n  if (!navigator.serviceWorker.controller && mockRegistrations.length > 0) {\n    // Reload the page when it has associated workers, but no active controller.\n    // The absence of a controller can mean either:\n    // - page has no Service Worker associated with it\n    // - page has been hard-reloaded and its workers won't be used until the next reload.\n    // Since we've checked that there are registrations associated with this page,\n    // at this point we are sure it's hard reload that falls into this clause.\n    location.reload()\n  }\n\n  const [existingRegistration] = mockRegistrations\n\n  if (existingRegistration) {\n    // Schedule the worker update in the background.\n    // Update ensures the existing worker is up-to-date.\n    existingRegistration.update()\n\n    // Return the worker reference immediately.\n    return [\n      getWorkerByRegistration(\n        existingRegistration,\n        absoluteWorkerUrl,\n        findWorker,\n      ),\n      existingRegistration,\n    ]\n  }\n\n  // When the Service Worker wasn't found, register it anew and return the reference.\n  const registrationResult = await until<Error, ServiceWorkerInstanceTuple>(\n    async () => {\n      const registration = await navigator.serviceWorker.register(url, options)\n      return [\n        // Compare existing worker registration by its worker URL,\n        // to prevent irrelevant workers to resolve here (such as Codesandbox worker).\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n        registration,\n      ]\n    },\n  )\n\n  // Handle Service Worker registration errors.\n  if (registrationResult.error) {\n    const isWorkerMissing = registrationResult.error.message.includes('(404)')\n\n    // Produce a custom error message when given a non-existing Service Worker url.\n    // Suggest developers to check their setup.\n    if (isWorkerMissing) {\n      const scopeUrl = new URL(options?.scope || '/', location.href)\n\n      throw new Error(\n        devUtils.formatMessage(`\\\nFailed to register a Service Worker for scope ('${scopeUrl.href}') with script ('${absoluteWorkerUrl}'): Service Worker script does not exist at the given path.\n\nDid you forget to run \"npx msw init <PUBLIC_DIR>\"?\n\nLearn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`),\n      )\n    }\n\n    // Fallback error message for any other registration errors.\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to register the Service Worker:\\n\\n%s',\n        registrationResult.error.message,\n      ),\n    )\n  }\n\n  return registrationResult.data\n}\n", "/**\n * Returns an absolute Service Worker URL based on the given\n * relative URL (known during the registration).\n */\nexport function getAbsoluteWorkerUrl(workerUrl: string): string {\n  return new URL(workerUrl, location.href).href\n}\n", "import { FindWorker } from '../../glossary'\n\n/**\n * Attempts to resolve a Service Worker instance from a given registration,\n * regardless of its state (active, installing, waiting).\n */\nexport function getWorkerByRegistration(\n  registration: ServiceWorkerRegistration,\n  absoluteWorkerUrl: string,\n  findWorker: FindWorker,\n): ServiceWorker | null {\n  const allStates = [\n    registration.active,\n    registration.installing,\n    registration.waiting,\n  ]\n  const relevantStates = allStates.filter((state): state is ServiceWorker => {\n    return state != null\n  })\n  const worker = relevantStates.find((worker) => {\n    return findWorker(worker.scriptURL, absoluteWorkerUrl)\n  })\n\n  return worker || null\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { StartOptions, SetupWorkerInternalContext } from '../../glossary'\nimport { printStartMessage } from './printStartMessage'\n\n/**\n * Signals the worker to enable the interception of requests.\n */\nexport async function enableMocking(\n  context: SetupWorkerInternalContext,\n  options: StartOptions,\n) {\n  context.workerChannel.send('MOCK_ACTIVATE')\n  const { payload } = await context.events.once('MOCKING_ENABLED')\n\n  // Warn the developer on multiple \"worker.start()\" calls.\n  // While this will not affect the worker in any way,\n  // it likely indicates an issue with the developer's code.\n  if (context.isMockingEnabled) {\n    devUtils.warn(\n      `Found a redundant \"worker.start()\" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this \"worker.start()\" call.`,\n    )\n    return\n  }\n\n  context.isMockingEnabled = true\n\n  printStartMessage({\n    quiet: options.quiet,\n    workerScope: context.registration?.scope,\n    workerUrl: context.worker?.scriptURL,\n    client: payload.client,\n  })\n}\n", "import type { ServiceWorkerIncomingEventsMap } from '../../glossary'\nimport { devUtils } from '~/core/utils/internal/devUtils'\n\ninterface PrintStartMessageArgs {\n  quiet?: boolean\n  message?: string\n  workerUrl?: string\n  workerScope?: string\n  client?: ServiceWorkerIncomingEventsMap['MOCKING_ENABLED']['client']\n}\n\n/**\n * Prints a worker activation message in the browser's console.\n */\nexport function printStartMessage(args: PrintStartMessageArgs = {}) {\n  if (args.quiet) {\n    return\n  }\n\n  const message = args.message || 'Mocking enabled.'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    `%c${devUtils.formatMessage(message)}`,\n    'color:orangered;font-weight:bold;',\n  )\n  // eslint-disable-next-line no-console\n  console.log(\n    '%cDocumentation: %chttps://mswjs.io/docs',\n    'font-weight:bold',\n    'font-weight:normal',\n  )\n  // eslint-disable-next-line no-console\n  console.log('Found an issue? https://github.com/mswjs/msw/issues')\n\n  if (args.workerUrl) {\n    // eslint-disable-next-line no-console\n    console.log('Worker script URL:', args.workerUrl)\n  }\n\n  if (args.workerScope) {\n    // eslint-disable-next-line no-console\n    console.log('Worker scope:', args.workerScope)\n  }\n\n  if (args.client) {\n    // eslint-disable-next-line no-console\n    console.log('Client ID: %s (%s)', args.client.id, args.client.frameType)\n  }\n\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n", "import {\n  StringifiedResponse,\n  ServiceWorkerIncomingEventsMap,\n} from '../../glossary'\n\nexport interface ServiceWorkerMessage<\n  EventType extends keyof ServiceWorkerIncomingEventsMap,\n  EventPayload,\n> {\n  type: EventType\n  payload: EventPayload\n}\n\ninterface WorkerChannelEventsMap {\n  MOCK_RESPONSE: [\n    data: StringifiedResponse,\n    transfer?: [ReadableStream<Uint8Array>],\n  ]\n  PASSTHROUGH: []\n}\n\nexport class WorkerChannel {\n  constructor(private readonly port: MessagePort) {}\n\n  public postMessage<Event extends keyof WorkerChannelEventsMap>(\n    event: Event,\n    ...rest: WorkerChannelEventsMap[Event]\n  ): void {\n    const [data, transfer] = rest\n    this.port.postMessage({ type: event, data }, { transfer })\n  }\n}\n", "import type { ServiceWorkerIncomingRequest } from '../setupWorker/glossary'\n\ntype Input = Pick<ServiceWorkerIncomingRequest, 'method' | 'body'>\n\n/**\n * Ensures that an empty GET request body is always represented as `undefined`.\n */\nexport function pruneGetRequestBody(\n  request: Input,\n): ServiceWorkerIncomingRequest['body'] {\n  // Force HEAD/GET request body to always be empty.\n  // The worker reads any request's body as <PERSON><PERSON><PERSON><PERSON>uff<PERSON>,\n  // and you cannot re-construct a GET/HEAD Request\n  // with an ArrayBuffer, even if empty. Also note that\n  // \"request.body\" is always undefined in the worker.\n  if (['HEAD', 'GET'].includes(request.method)) {\n    return undefined\n  }\n\n  return request.body\n}\n", "import { pruneGetRequestBody } from './pruneGetRequestBody'\nimport type { ServiceWorkerIncomingRequest } from '../setupWorker/glossary'\n\n/**\n * Converts a given request received from the Service Worker\n * into a Fetch `Request` instance.\n */\nexport function deserializeRequest(\n  serializedRequest: ServiceWorkerIncomingRequest,\n): Request {\n  return new Request(serializedRequest.url, {\n    ...serializedRequest,\n    body: pruneGetRequestBody(serializedRequest),\n  })\n}\n", "import {\n  StartOptions,\n  SetupWorkerInternalContext,\n  ServiceWorkerIncomingEventsMap,\n} from '../glossary'\nimport {\n  ServiceWorkerMessage,\n  WorkerChannel,\n} from './utils/createMessageChannel'\nimport { deserializeRequest } from '../../utils/deserializeRequest'\nimport { RequestHandler } from '~/core/handlers/RequestHandler'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport { RequiredDeep } from '~/core/typeUtils'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { toResponseInit } from '~/core/utils/toResponseInit'\nimport { isHandlerKind } from '~/core/utils/internal/isHandlerKind'\n\nexport const createRequestListener = (\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n) => {\n  return async (\n    event: MessageEvent,\n    message: ServiceWorkerMessage<\n      'REQUEST',\n      ServiceWorkerIncomingEventsMap['REQUEST']\n    >,\n  ) => {\n    const messageChannel = new WorkerChannel(event.ports[0])\n\n    const requestId = message.payload.id\n    const request = deserializeRequest(message.payload)\n    const requestCloneForLogs = request.clone()\n\n    // Make this the first request clone before the\n    // request resolution pipeline even starts.\n    // Store the clone in cache so the first matching\n    // request handler would skip the cloning phase.\n    const requestClone = request.clone()\n    RequestHandler.cache.set(request, requestClone)\n\n    try {\n      await handleRequest(\n        request,\n        requestId,\n        context.getRequestHandlers().filter(isHandlerKind('RequestHandler')),\n        options,\n        context.emitter,\n        {\n          onPassthroughResponse() {\n            messageChannel.postMessage('PASSTHROUGH')\n          },\n          async onMockedResponse(response, { handler, parsedResult }) {\n            // Clone the mocked response so its body could be read\n            // to buffer to be sent to the worker and also in the\n            // \".log()\" method of the request handler.\n            const responseClone = response.clone()\n            const responseCloneForLogs = response.clone()\n            const responseInit = toResponseInit(response)\n\n            /**\n             * @note Safari doesn't support transferring a \"ReadableStream\".\n             * Check that the browser supports that before sending it to the worker.\n             */\n            if (context.supports.readableStreamTransfer) {\n              const responseStreamOrNull = response.body\n\n              messageChannel.postMessage(\n                'MOCK_RESPONSE',\n                {\n                  ...responseInit,\n                  body: responseStreamOrNull,\n                },\n                responseStreamOrNull ? [responseStreamOrNull] : undefined,\n              )\n            } else {\n              /**\n               * @note If we are here, this means the current environment doesn't\n               * support \"ReadableStream\" as transferable. In that case,\n               * attempt to read the non-empty response body as ArrayBuffer, if it's not empty.\n               * @see https://github.com/mswjs/msw/issues/1827\n               */\n              const responseBufferOrNull =\n                response.body === null\n                  ? null\n                  : await responseClone.arrayBuffer()\n\n              messageChannel.postMessage('MOCK_RESPONSE', {\n                ...responseInit,\n                body: responseBufferOrNull,\n              })\n            }\n\n            if (!options.quiet) {\n              context.emitter.once('response:mocked', () => {\n                handler.log({\n                  request: requestCloneForLogs,\n                  response: responseCloneForLogs,\n                  parsedResult,\n                })\n              })\n            }\n          },\n        },\n      )\n    } catch (error) {\n      if (error instanceof Error) {\n        devUtils.error(\n          `Uncaught exception in the request handler for \"%s %s\":\n\n%s\n\nThis exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/http/mocking-responses/error-responses`,\n          request.method,\n          request.url,\n          error.stack ?? error,\n        )\n\n        // Treat all other exceptions in a request handler as unintended,\n        // alerting that there is a problem that needs fixing.\n        messageChannel.postMessage('MOCK_RESPONSE', {\n          status: 500,\n          statusText: 'Request Handler Error',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            name: error.name,\n            message: error.message,\n            stack: error.stack,\n          }),\n        })\n      }\n    }\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport type { SetupWorkerInternalContext } from '../setupWorker/glossary'\n\n/**\n * Check whether the registered Service Worker has been\n * generated by the installed version of the library.\n * Prints a warning message if the worker scripts mismatch.\n */\nexport async function checkWorkerIntegrity(\n  context: SetupWorkerInternalContext,\n): Promise<void> {\n  // Request the integrity checksum from the registered worker.\n  context.workerChannel.send('INTEGRITY_CHECK_REQUEST')\n\n  const { payload } = await context.events.once('INTEGRITY_CHECK_RESPONSE')\n\n  // Compare the response from the Service Worker and the\n  // global variable set during the build.\n\n  // The integrity is validated based on the worker script's checksum\n  // that's derived from its minified content during the build.\n  // The \"SERVICE_WORKER_CHECKSUM\" global variable is injected by the build.\n  if (payload.checksum !== SERVICE_WORKER_CHECKSUM) {\n    devUtils.warn(\n      `The currently registered Service Worker has been generated by a different version of MSW (${payload.packageVersion}) and may not be fully compatible with the installed version.\n\nIt's recommended you update your worker script by running this command:\n\n  \\u2022 npx msw init <PUBLIC_DIR>\n\nYou can also automate this process and make the worker script update automatically upon the library installations. Read more: https://mswjs.io/docs/cli/init.`,\n    )\n  }\n}\n", "const encoder = new TextEncoder()\n\nexport function encodeBuffer(text: string): Uint8Array {\n  return encoder.encode(text)\n}\n\nexport function decodeBuffer(buffer: <PERSON>rrayBuffer, encoding?: string): string {\n  const decoder = new TextDecoder(encoding)\n  return decoder.decode(buffer)\n}\n\n/**\n * Create an `ArrayBuffer` from the given `Uint8Array`.\n * Takes the byte offset into account to produce the right buffer\n * in the case when the buffer is bigger than the data view.\n */\nexport function toArrayBuffer(array: Uint8Array): ArrayBuffer {\n  return array.buffer.slice(\n    array.byteOffset,\n    array.byteOffset + array.byteLength\n  )\n}\n", "import type { RequestController } from './RequestController'\n\nexport const IS_PATCHED_MODULE: unique symbol = Symbol('isPatchedModule')\n\n/**\n * @note Export `RequestController` as a type only.\n * It's never meant to be created in the userland.\n */\nexport type { RequestController }\n\nexport type RequestCredentials = 'omit' | 'include' | 'same-origin'\n\nexport type HttpRequestEventMap = {\n  request: [\n    args: {\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n  response: [\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ]\n  unhandledException: [\n    args: {\n      error: unknown\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n}\n", "/**\n * Returns a boolean indicating whether the given URL string\n * can be parsed into a `URL` instance.\n * A substitute for `URL.canParse()` for Node.js 18.\n */\nexport function canParseUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n", "/**\n * Returns the value behind the symbol with the given name.\n */\nexport function getValueBySymbol<T>(\n  symbolName: string,\n  source: object\n): T | undefined {\n  const ownSymbols = Object.getOwnPropertySymbols(source)\n\n  const symbol = ownSymbols.find((symbol) => {\n    return symbol.description === symbolName\n  })\n\n  if (symbol) {\n    return Reflect.get(source, symbol)\n  }\n\n  return\n}\n", "import { canParseUrl } from './canParseUrl'\nimport { getValueBySymbol } from './getValueBySymbol'\n\nexport interface FetchResponseInit extends ResponseInit {\n  url?: string\n}\n\ninterface UndiciFetchInternalState {\n  aborted: boolean\n  rangeRequested: boolean\n  timingAllowPassed: boolean\n  requestIncludesCredentials: boolean\n  type: ResponseType\n  status: number\n  statusText: string\n  timingInfo: unknown\n  cacheState: unknown\n  headersList: Record<symbol, Map<string, unknown>>\n  urlList: Array<URL>\n  body?: {\n    stream: ReadableStream\n    source: unknown\n    length: number\n  }\n}\n\nexport class FetchResponse extends Response {\n  /**\n   * Response status codes for responses that cannot have body.\n   * @see https://fetch.spec.whatwg.org/#statuses\n   */\n  static readonly STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304]\n\n  static readonly STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308]\n\n  static isConfigurableStatusCode(status: number): boolean {\n    return status >= 200 && status <= 599\n  }\n\n  static isRedirectResponse(status: number): boolean {\n    return FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status)\n  }\n\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status: number): boolean {\n    return !FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status)\n  }\n\n  static setUrl(url: string | undefined, response: Response): void {\n    if (!url || url === 'about:' || !canParseUrl(url)) {\n      return\n    }\n\n    const state = getValueBySymbol<UndiciFetchInternalState>('state', response)\n\n    if (state) {\n      // In Undici, push the URL to the internal list of URLs.\n      // This will respect the `response.url` getter logic correctly.\n      state.urlList.push(new URL(url))\n    } else {\n      // In other libraries, redefine the `url` property directly.\n      Object.defineProperty(response, 'url', {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false,\n      })\n    }\n  }\n\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders: Array<string>): Headers {\n    const headers = new Headers()\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1])\n    }\n    return headers\n  }\n\n  constructor(body?: BodyInit | null, init: FetchResponseInit = {}) {\n    const status = init.status ?? 200\n    const safeStatus = FetchResponse.isConfigurableStatusCode(status)\n      ? status\n      : 200\n    const finalBody = FetchResponse.isResponseWithBody(status) ? body : null\n\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers,\n    })\n\n    if (status !== safeStatus) {\n      /**\n       * @note Undici keeps an internal \"Symbol(state)\" that holds\n       * the actual value of response status. Update that in Node.js.\n       */\n      const state = getValueBySymbol<UndiciFetchInternalState>('state', this)\n\n      if (state) {\n        state.status = status\n      } else {\n        Object.defineProperty(this, 'status', {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false,\n        })\n      }\n    }\n\n    FetchResponse.setUrl(init.url, this)\n  }\n}\n", "const kRawRequest = Symbol('kRawRequest')\n\n/**\n * Returns a raw request instance associated with this request.\n *\n * @example\n * interceptor.on('request', ({ request }) => {\n *   const rawRequest = getRawRequest(request)\n *\n *   if (rawRequest instanceof http.ClientRequest) {\n *     console.log(rawRequest.rawHeaders)\n *   }\n * })\n */\nexport function getRawRequest(request: Request): unknown | undefined {\n  return Reflect.get(request, kRawRequest)\n}\n\nexport function setRawRequest(request: Request, rawRequest: unknown): void {\n  Reflect.set(request, kRawRequest, rawRequest)\n}\n", "var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/index.ts\nimport { isNodeProcess } from \"is-node-process\";\nimport { format } from \"outvariant\";\n\n// src/colors.ts\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\n\n// src/index.ts\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */ new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write(\n      [colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"),\n      ...positionals.map(serializeInput)\n    );\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\n      \"en-GB\"\n    )}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\": {\n        return log;\n      }\n      case \"warning\": {\n        return warn;\n      }\n      case \"error\": {\n        return error;\n      }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\nexport {\n  Logger\n};\n", "import type { Emitter } from './Emitter'\n\nexport class MemoryLeakError extends Error {\n  constructor(\n    public readonly emitter: Emitter<any>,\n    public readonly type: string | number | symbol,\n    public readonly count: number\n  ) {\n    super(\n      `Possible EventEmitter memory leak detected. ${count} ${type.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`\n    )\n    this.name = 'MaxListenersExceededWarning'\n  }\n}\n", "import { MemoryLeakError } from './MemoryLeakError'\n\nexport type EventMap = {\n  [eventName: string]: Array<unknown>\n}\n\nexport type InternalEventNames = 'newListener' | 'removeListener'\n\nexport type InternalListener<Events extends EventMap> = Listener<\n  [eventName: keyof Events, listener: Listener<Array<unknown>>]\n>\n\nexport type Listener<Data extends Array<unknown>> = (...data: Data) => void\n\n/**\n * Node.js-compatible implementation of `EventEmitter`.\n *\n * @example\n * const emitter = new Emitter<{ hello: [string] }>()\n * emitter.on('hello', (name) => console.log(name))\n * emitter.emit('hello', 'John')\n */\nexport class Emitter<Events extends EventMap> {\n  private events: Map<keyof Events, Array<Listener<any>>>\n  private maxListeners: number\n  private hasWarnedAboutPotentialMemoryLeak: boolean\n\n  static defaultMaxListeners = 10\n\n  static listenerCount<Events extends EventMap>(\n    emitter: Emitter<EventMap>,\n    eventName: keyof Events\n  ): number {\n    return emitter.listenerCount<any>(eventName)\n  }\n\n  constructor() {\n    this.events = new Map()\n    this.maxListeners = Emitter.defaultMaxListeners\n    this.hasWarnedAboutPotentialMemoryLeak = false\n  }\n\n  private _emitInternalEvent(\n    internalEventName: InternalEventNames,\n    eventName: keyof Events,\n    listener: Listener<Array<unknown>>\n  ): void {\n    this.emit(\n      internalEventName,\n      // Anything to make TypeScript happy.\n      ...([eventName, listener] as Events['newListener'] &\n        Events['removeListener'])\n    )\n  }\n\n  private _getListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Array<unknown>>> {\n    // Always return a copy of the listeners array\n    // so they are fixed at the time of the \"_getListeners\" call.\n    return Array.prototype.concat.apply([], this.events.get(eventName)) || []\n  }\n\n  private _removeListener<EventName extends keyof Events>(\n    listeners: Array<Listener<Events[EventName]>>,\n    listener: Listener<Events[EventName]>\n  ): Array<Listener<Events[EventName]>> {\n    const index = listeners.indexOf(listener)\n\n    if (index > -1) {\n      listeners.splice(index, 1)\n    }\n\n    return []\n  }\n\n  private _wrapOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): Listener<Events[EventName]> {\n    const onceListener = (...data: Events[keyof Events]) => {\n      this.removeListener(eventName, onceListener)\n\n      /**\n       * @note Return the result of the original listener.\n       * This way this wrapped preserves listeners that are async.\n       */\n      return listener.apply(this, data)\n    }\n\n    // Inherit the name of the original listener.\n    Object.defineProperty(onceListener, 'name', { value: listener.name })\n\n    return onceListener\n  }\n\n  public setMaxListeners(maxListeners: number): this {\n    this.maxListeners = maxListeners\n    return this\n  }\n\n  /**\n   * Returns the current max listener value for the `Emitter` which is\n   * either set by `emitter.setMaxListeners(n)` or defaults to\n   * `Emitter.defaultMaxListeners`.\n   */\n  public getMaxListeners(): number {\n    return this.maxListeners\n  }\n\n  /**\n   * Returns an array listing the events for which the emitter has registered listeners.\n   * The values in the array will be strings or Symbols.\n   */\n  public eventNames(): Array<keyof Events> {\n    return Array.from(this.events.keys())\n  }\n\n  /**\n   * Synchronously calls each of the listeners registered for the event named `eventName`,\n   * in the order they were registered, passing the supplied arguments to each.\n   * Returns `true` if the event has listeners, `false` otherwise.\n   *\n   * @example\n   * const emitter = new Emitter<{ hello: [string] }>()\n   * emitter.emit('hello', 'John')\n   */\n  public emit<EventName extends keyof Events>(\n    eventName: EventName,\n    ...data: Events[EventName]\n  ): boolean {\n    const listeners = this._getListeners(eventName)\n    listeners.forEach((listener) => {\n      listener.apply(this, data)\n    })\n\n    return listeners.length > 0\n  }\n\n  public addListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public addListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public addListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: InternalListener<Events> | Listener<Events[any]>\n  ): this {\n    // Emit the `newListener` event before adding the listener.\n    this._emitInternalEvent('newListener', eventName, listener)\n\n    const nextListeners = this._getListeners(eventName).concat(listener)\n    this.events.set(eventName, nextListeners)\n\n    if (\n      this.maxListeners > 0 &&\n      this.listenerCount(eventName) > this.maxListeners &&\n      !this.hasWarnedAboutPotentialMemoryLeak\n    ) {\n      this.hasWarnedAboutPotentialMemoryLeak = true\n\n      const memoryLeakWarning = new MemoryLeakError(\n        this,\n        eventName,\n        this.listenerCount(eventName)\n      )\n      console.warn(memoryLeakWarning)\n    }\n\n    return this\n  }\n\n  public on(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: 'removeListener' | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(eventName, listener)\n  }\n\n  public once(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: InternalEventNames | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public prependListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      const nextListeners = [listener].concat(listeners)\n      this.events.set(eventName, nextListeners)\n    } else {\n      this.events.set(eventName, listeners.concat(listener))\n    }\n\n    return this\n  }\n\n  public prependOnceListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependOnceListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.prependListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public removeListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public removeListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public removeListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      this._removeListener(listeners, listener)\n      this.events.set(eventName, listeners)\n\n      // Emit the `removeListener` event after removing the listener.\n      this._emitInternalEvent('removeListener', eventName, listener)\n    }\n\n    return this\n  }\n\n  public off(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public off<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  /**\n   * Alias for `emitter.removeListener()`.\n   *\n   * @example\n   * emitter.off('hello', listener)\n   */\n  public off(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.removeListener(eventName, listener)\n  }\n\n  public removeAllListeners(eventName?: InternalEventNames): this\n  public removeAllListeners<EventName extends keyof Events>(\n    eventName?: EventName\n  ): this\n  public removeAllListeners(\n    eventName?: InternalEventNames | keyof Events\n  ): this {\n    if (eventName) {\n      this.events.delete(eventName)\n    } else {\n      this.events.clear()\n    }\n\n    return this\n  }\n\n  public listeners(eventName: InternalEventNames): Array<Listener<any>>\n  public listeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>>\n  /**\n   * Returns a copy of the array of listeners for the event named `eventName`.\n   */\n  public listeners(eventName: InternalEventNames | keyof Events) {\n    return Array.from(this._getListeners(eventName))\n  }\n\n  public listenerCount(eventName: InternalEventNames): number\n  public listenerCount<EventName extends keyof Events>(\n    eventName: EventName\n  ): number\n  /**\n   * Returns the number of listeners listening to the event named `eventName`.\n   */\n  public listenerCount(eventName: InternalEventNames | keyof Events): number {\n    return this._getListeners(eventName).length\n  }\n\n  public rawListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>> {\n    return this.listeners(eventName)\n  }\n}\n", "import { Logger } from '@open-draft/logger'\nimport { Emitter, Listener } from 'strict-event-emitter'\n\nexport type InterceptorEventMap = Record<string, any>\nexport type InterceptorSubscription = () => void\n\n/**\n * Request header name to detect when a single request\n * is being handled by nested interceptors (XHR -> ClientRequest).\n * Obscure by design to prevent collisions with user-defined headers.\n * Ideally, come up with the Interceptor-level mechanism for this.\n * @see https://github.com/mswjs/interceptors/issues/378\n */\nexport const INTERNAL_REQUEST_ID_HEADER_NAME =\n  'x-interceptors-internal-request-id'\n\nexport function getGlobalSymbol<V>(symbol: Symbol): V | undefined {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || undefined\n  )\n}\n\nfunction setGlobalSymbol(symbol: Symbol, value: any): void {\n  // @ts-ignore\n  globalThis[symbol] = value\n}\n\nexport function deleteGlobalSymbol(symbol: Symbol): void {\n  // @ts-ignore\n  delete globalThis[symbol]\n}\n\nexport enum InterceptorReadyState {\n  INACTIVE = 'INACTIVE',\n  APPLYING = 'APPLYING',\n  APPLIED = 'APPLIED',\n  DISPOSING = 'DISPOSING',\n  DISPOSED = 'DISPOSED',\n}\n\nexport type ExtractEventNames<Events extends Record<string, any>> =\n  Events extends Record<infer EventName, any> ? EventName : never\n\nexport class Interceptor<Events extends InterceptorEventMap> {\n  protected emitter: Emitter<Events>\n  protected subscriptions: Array<InterceptorSubscription>\n  protected logger: Logger\n\n  public readyState: InterceptorReadyState\n\n  constructor(private readonly symbol: symbol) {\n    this.readyState = InterceptorReadyState.INACTIVE\n\n    this.emitter = new Emitter()\n    this.subscriptions = []\n    this.logger = new Logger(symbol.description!)\n\n    // Do not limit the maximum number of listeners\n    // so not to limit the maximum amount of parallel events emitted.\n    this.emitter.setMaxListeners(0)\n\n    this.logger.info('constructing the interceptor...')\n  }\n\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  protected checkEnvironment(): boolean {\n    return true\n  }\n\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  public apply(): void {\n    const logger = this.logger.extend('apply')\n    logger.info('applying the interceptor...')\n\n    if (this.readyState === InterceptorReadyState.APPLIED) {\n      logger.info('intercepted already applied!')\n      return\n    }\n\n    const shouldApply = this.checkEnvironment()\n\n    if (!shouldApply) {\n      logger.info('the interceptor cannot be applied in this environment!')\n      return\n    }\n\n    this.readyState = InterceptorReadyState.APPLYING\n\n    // Whenever applying a new interceptor, check if it hasn't been applied already.\n    // This enables to apply the same interceptor multiple times, for example from a different\n    // interceptor, only proxying events but keeping the stubs in a single place.\n    const runningInstance = this.getInstance()\n\n    if (runningInstance) {\n      logger.info('found a running instance, reusing...')\n\n      // Proxy any listeners you set on this instance to the running instance.\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event)\n\n        // Add listeners to the running instance so they appear\n        // at the top of the event listeners list and are executed first.\n        runningInstance.emitter.addListener(event, listener)\n\n        // Ensure that once this interceptor instance is disposed,\n        // it removes all listeners it has appended to the running interceptor instance.\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener)\n          logger.info('removed proxied \"%s\" listener!', event)\n        })\n\n        return this\n      }\n\n      this.readyState = InterceptorReadyState.APPLIED\n\n      return\n    }\n\n    logger.info('no running instance found, setting up a new instance...')\n\n    // Setup the interceptor.\n    this.setup()\n\n    // Store the newly applied interceptor instance globally.\n    this.setInstance()\n\n    this.readyState = InterceptorReadyState.APPLIED\n  }\n\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  protected setup(): void {}\n\n  /**\n   * Listen to the interceptor's public events.\n   */\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    const logger = this.logger.extend('on')\n\n    if (\n      this.readyState === InterceptorReadyState.DISPOSING ||\n      this.readyState === InterceptorReadyState.DISPOSED\n    ) {\n      logger.info('cannot listen to events, already disposed!')\n      return this\n    }\n\n    logger.info('adding \"%s\" event listener:', event, listener)\n\n    this.emitter.on(event, listener)\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.once(event, listener)\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.off(event, listener)\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName\n  ): this {\n    this.emitter.removeAllListeners(event)\n    return this\n  }\n\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  public dispose(): void {\n    const logger = this.logger.extend('dispose')\n\n    if (this.readyState === InterceptorReadyState.DISPOSED) {\n      logger.info('cannot dispose, already disposed!')\n      return\n    }\n\n    logger.info('disposing the interceptor...')\n    this.readyState = InterceptorReadyState.DISPOSING\n\n    if (!this.getInstance()) {\n      logger.info('no interceptors running, skipping dispose...')\n      return\n    }\n\n    // Delete the global symbol as soon as possible,\n    // indicating that the interceptor is no longer running.\n    this.clearInstance()\n\n    logger.info('global symbol deleted:', getGlobalSymbol(this.symbol))\n\n    if (this.subscriptions.length > 0) {\n      logger.info('disposing of %d subscriptions...', this.subscriptions.length)\n\n      for (const dispose of this.subscriptions) {\n        dispose()\n      }\n\n      this.subscriptions = []\n\n      logger.info('disposed of all subscriptions!', this.subscriptions.length)\n    }\n\n    this.emitter.removeAllListeners()\n    logger.info('destroyed the listener!')\n\n    this.readyState = InterceptorReadyState.DISPOSED\n  }\n\n  private getInstance(): this | undefined {\n    const instance = getGlobalSymbol<this>(this.symbol)\n    this.logger.info('retrieved global instance:', instance?.constructor?.name)\n    return instance\n  }\n\n  private setInstance(): void {\n    setGlobalSymbol(this.symbol, this)\n    this.logger.info('set global instance!', this.symbol.description)\n  }\n\n  private clearInstance(): void {\n    deleteGlobalSymbol(this.symbol)\n    this.logger.info('cleared global instance!', this.symbol.description)\n  }\n}\n", "/**\n * Generate a random ID string to represent a request.\n * @example\n * createRequestId()\n * // \"f774b6c9c600f\"\n */\nexport function createRequestId(): string {\n  return Math.random().toString(16).slice(2)\n}\n", "import { EventMap, Listener } from 'strict-event-emitter'\nimport { Interceptor, ExtractEventNames } from './Interceptor'\n\nexport interface BatchInterceptorOptions<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> {\n  name: string\n  interceptors: InterceptorList\n}\n\nexport type ExtractEventMapType<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> = InterceptorList extends ReadonlyArray<infer InterceptorType>\n  ? InterceptorType extends Interceptor<infer EventMap>\n    ? EventMap\n    : never\n  : never\n\n/**\n * A batch interceptor that exposes a single interface\n * to apply and operate with multiple interceptors at once.\n */\nexport class BatchInterceptor<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>,\n  Events extends EventMap = ExtractEventMapType<InterceptorList>\n> extends Interceptor<Events> {\n  static symbol: symbol\n\n  private interceptors: InterceptorList\n\n  constructor(options: BatchInterceptorOptions<InterceptorList>) {\n    BatchInterceptor.symbol = Symbol(options.name)\n    super(BatchInterceptor.symbol)\n    this.interceptors = options.interceptors\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('applying all %d interceptors...', this.interceptors.length)\n\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name)\n      interceptor.apply()\n\n      logger.info('adding interceptor dispose subscription')\n      this.subscriptions.push(() => interceptor.dispose())\n    }\n  }\n\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    // Instead of adding a listener to the batch interceptor,\n    // propagate the listener to each of the individual interceptors.\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener)\n    }\n\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener)\n    }\n\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener)\n    }\n\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName | undefined\n  ): this {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event)\n    }\n\n    return this\n  }\n}\n", "/**\n * Removes query parameters and hashes from a given URL.\n */\nexport function getCleanUrl(url: URL, isAbsolute: boolean = true): string {\n  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join('')\n}\n", "import { FetchResponse } from '@mswjs/interceptors'\nimport type {\n  ServiceWorkerIncomingEventsMap,\n  SetupWorkerInternalContext,\n} from '../glossary'\nimport type { ServiceWorkerMessage } from './utils/createMessageChannel'\nimport { deserializeRequest } from '../../utils/deserializeRequest'\n\nexport function createResponseListener(context: SetupWorkerInternalContext) {\n  return (\n    _: MessageEvent,\n    message: ServiceWorkerMessage<\n      'RESPONSE',\n      ServiceWorkerIncomingEventsMap['RESPONSE']\n    >,\n  ) => {\n    const { payload: responseJson } = message\n    const request = deserializeRequest(responseJson.request)\n\n    /**\n     * CORS requests with `mode: \"no-cors\"` result in \"opaque\" responses.\n     * That kind of responses cannot be manipulated in JavaScript due\n     * to the security considerations.\n     * @see https://fetch.spec.whatwg.org/#concept-filtered-response-opaque\n     * @see https://github.com/mswjs/msw/issues/529\n     */\n    if (responseJson.response.type?.includes('opaque')) {\n      return\n    }\n\n    const response =\n      responseJson.response.status === 0\n        ? Response.error()\n        : new FetchResponse(\n            /**\n             * Responses may be streams here, but when we create a response object\n             * with null-body status codes, like 204, 205, 304 Response will\n             * throw when passed a non-null body, so ensure it's null here\n             * for those codes\n             */\n            FetchResponse.isResponseWithBody(responseJson.response.status)\n              ? responseJson.response.body\n              : null,\n            {\n              ...responseJson,\n              /**\n               * Set response URL if it's not set already.\n               * @see https://github.com/mswjs/msw/issues/2030\n               * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/url\n               */\n              url: request.url,\n            },\n          )\n\n    context.emitter.emit(\n      responseJson.isMockedResponse ? 'response:mocked' : 'response:bypass',\n      {\n        requestId: responseJson.request.id,\n        request,\n        response,\n      },\n    )\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { StartOptions } from '../../glossary'\n\nexport function validateWorkerScope(\n  registration: ServiceWorkerRegistration,\n  options?: StartOptions,\n): void {\n  if (!options?.quiet && !location.href.startsWith(registration.scope)) {\n    devUtils.warn(\n      `\\\nCannot intercept requests on this page because it's outside of the worker's scope (\"${registration.scope}\"). If you wish to mock API requests on this page, you must resolve this scope issue.\n\n- (Recommended) Register the worker at the root level (\"/\") of your application.\n- Set the \"Service-Worker-Allowed\" response header to allow out-of-scope workers.\\\n`,\n    )\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport const createStop = (\n  context: SetupWorkerInternalContext,\n): StopHandler => {\n  return function stop() {\n    // Warn developers calling \"worker.stop()\" more times than necessary.\n    // This likely indicates a mistake in their code.\n    if (!context.isMockingEnabled) {\n      devUtils.warn(\n        'Found a redundant \"worker.stop()\" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this \"worker.stop()\" call.',\n      )\n      return\n    }\n\n    /**\n     * Signal the Service Worker to disable mocking for this client.\n     * Use this an an explicit way to stop the mocking, while preserving\n     * the worker-client relation. Does not affect the worker's lifecycle.\n     */\n    context.workerChannel.send('MOCK_DEACTIVATE')\n    context.isMockingEnabled = false\n    window.clearInterval(context.keepAliveInterval)\n\n    // Post the internal stop message on the window\n    // to let any logic know when the worker has stopped.\n    // E.g. the WebSocket client manager needs this to know\n    // when to clear its in-memory clients list.\n    window.postMessage({ type: 'msw/worker:stop' })\n\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\n\nexport function printStopMessage(args: { quiet?: boolean } = {}): void {\n  if (args.quiet) {\n    return\n  }\n\n  // eslint-disable-next-line no-console\n  console.log(\n    `%c${devUtils.formatMessage('Mocking disabled.')}`,\n    'color:orangered;font-weight:bold;',\n  )\n}\n", "import { RequiredDeep } from '~/core/typeUtils'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport {\n  SetupWorker,\n  SetupWorkerInternalContext,\n  StartHandler,\n  StartOptions,\n} from '../../glossary'\n\nexport const DEFAULT_START_OPTIONS: RequiredDeep<StartOptions> = {\n  serviceWorker: {\n    url: '/mockServiceWorker.js',\n    options: null as any,\n  },\n  quiet: false,\n  waitUntilReady: true,\n  onUnhandledRequest: 'warn',\n  findWorker(scriptURL, mockServiceWorkerUrl) {\n    return scriptURL === mockServiceWorkerUrl\n  },\n}\n\n/**\n * Returns resolved worker start options, merging the default options\n * with the given custom options.\n */\nexport function resolveStartOptions(\n  initialOptions?: StartOptions,\n): RequiredDeep<StartOptions> {\n  return mergeRight(\n    DEFAULT_START_OPTIONS,\n    initialOptions || {},\n  ) as RequiredDeep<StartOptions>\n}\n\nexport function prepareStartHandler(\n  handler: <PERSON><PERSON><PERSON><PERSON>,\n  context: SetupWorkerInternalContext,\n): SetupWorker['start'] {\n  return (initialOptions) => {\n    context.startOptions = resolveStartOptions(initialOptions)\n    return handler(context.startOptions, initialOptions || {})\n  }\n}\n", "export type PromiseState = 'pending' | 'fulfilled' | 'rejected'\n\nexport type Executor<Value> = ConstructorParameters<typeof Promise<Value>>[0]\nexport type ResolveFunction<Value> = Parameters<Executor<Value>>[0]\nexport type RejectFunction<Reason> = Parameters<Executor<Reason>>[1]\n\nexport type DeferredPromiseExecutor<Input = never, Output = Input> = {\n  (resolve?: ResolveFunction<Input>, reject?: RejectFunction<any>): void\n\n  resolve: ResolveFunction<Input>\n  reject: RejectFunction<any>\n  result?: Output\n  state: PromiseState\n  rejectionReason?: unknown\n}\nexport function createDeferredExecutor<\n  Input = never,\n  Output = Input\n>(): DeferredPromiseExecutor<Input, Output> {\n  const executor = <DeferredPromiseExecutor<Input, Output>>((\n    resolve,\n    reject\n  ) => {\n    executor.state = 'pending'\n\n    executor.resolve = (data) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      executor.result = data as Output\n\n      const onFulfilled = <Value>(value: Value) => {\n        executor.state = 'fulfilled'\n        return value\n      }\n\n      return resolve(\n        data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled)\n      )\n    }\n\n    executor.reject = (reason) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      queueMicrotask(() => {\n        executor.state = 'rejected'\n      })\n\n      return reject((executor.rejectionReason = reason))\n    }\n  })\n\n  return executor\n}\n", "import {\n  type Executor,\n  type RejectFunction,\n  type ResolveFunction,\n  type DeferredPromiseExecutor,\n  createDeferredExecutor,\n} from './createDeferredExecutor'\n\nexport class DeferredPromise<Input, Output = Input> extends Promise<Input> {\n  #executor: DeferredPromiseExecutor\n\n  public resolve: ResolveFunction<Output>\n  public reject: RejectFunction<Output>\n\n  constructor(executor: Executor<Input> | null = null) {\n    const deferredExecutor = createDeferredExecutor()\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject)\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject)\n    })\n\n    this.#executor = deferredExecutor\n    this.resolve = this.#executor.resolve\n    this.reject = this.#executor.reject\n  }\n\n  public get state() {\n    return this.#executor.state\n  }\n\n  public get rejectionReason() {\n    return this.#executor.rejectionReason\n  }\n\n  public then<ThenResult = Input, CatchResult = never>(\n    onFulfilled?: (value: Input) => ThenResult | PromiseLike<ThenResult>,\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.then(onFulfilled, onRejected))\n  }\n\n  public catch<CatchResult = never>(\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.catch(onRejected))\n  }\n\n  public finally(onfinally?: () => void | Promise<any>) {\n    return this.#decorate(super.finally(onfinally))\n  }\n\n  #decorate<ChildInput>(\n    promise: Promise<ChildInput>\n  ): DeferredPromise<ChildInput, Output> {\n    return Object.defineProperties(promise, {\n      resolve: { configurable: true, value: this.resolve },\n      reject: { configurable: true, value: this.reject },\n    }) as DeferredPromise<ChildInput, Output>\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { InterceptorError } from './InterceptorError'\n\nconst kRequestHandled = Symbol('kRequestHandled')\nexport const kResponsePromise = Symbol('kResponsePromise')\n\nexport class RequestController {\n  /**\n   * Internal response promise.\n   * Available only for the library internals to grab the\n   * response instance provided by the developer.\n   * @note This promise cannot be rejected. It's either infinitely\n   * pending or resolved with whichever Response was passed to `respondWith()`.\n   */\n  [kResponsePromise]: DeferredPromise<\n    Response | Record<string, any> | undefined\n  >;\n\n  /**\n   * Internal flag indicating if this request has been handled.\n   * @note The response promise becomes \"fulfilled\" on the next tick.\n   */\n  [kRequestHandled]: boolean\n\n  constructor(private request: Request) {\n    this[kRequestHandled] = false\n    this[kResponsePromise] = new DeferredPromise()\n  }\n\n  /**\n   * Respond to this request with the given `Response` instance.\n   * @example\n   * controller.respondWith(new Response())\n   * controller.respondWith(Response.json({ id }))\n   * controller.respondWith(Response.error())\n   */\n  public respondWith(response: Response): void {\n    invariant.as(\n      InterceptorError,\n      !this[kRequestHandled],\n      'Failed to respond to the \"%s %s\" request: the \"request\" event has already been handled.',\n      this.request.method,\n      this.request.url\n    )\n\n    this[kRequestHandled] = true\n    this[kResponsePromise].resolve(response)\n\n    /**\n     * @note The request controller doesn't do anything\n     * apart from letting the interceptor await the response\n     * provided by the developer through the response promise.\n     * Each interceptor implements the actual respondWith/errorWith\n     * logic based on that interceptor's needs.\n     */\n  }\n\n  /**\n   * Error this request with the given reason.\n   *\n   * @example\n   * controller.errorWith()\n   * controller.errorWith(new Error('Oops!'))\n   * controller.errorWith({ message: 'Oops!'})\n   */\n  public errorWith(reason?: Error | Record<string, any>): void {\n    invariant.as(\n      InterceptorError,\n      !this[kRequestHandled],\n      'Failed to error the \"%s %s\" request: the \"request\" event has already been handled.',\n      this.request.method,\n      this.request.url\n    )\n\n    this[kRequestHandled] = true\n\n    /**\n     * @note Resolve the response promise, not reject.\n     * This helps us differentiate between unhandled exceptions\n     * and intended errors (\"errorWith\") while waiting for the response.\n     */\n    this[kResponsePromise].resolve(reason)\n  }\n}\n", "export class InterceptorError extends Error {\n  constructor(message?: string) {\n    super(message)\n    this.name = 'InterceptorError'\n    Object.setPrototypeOf(this, InterceptorError.prototype)\n  }\n}\n", "import { Emitter, EventMap } from 'strict-event-emitter'\n\n/**\n * Emits an event on the given emitter but executes\n * the listeners sequentially. This accounts for asynchronous\n * listeners (e.g. those having \"sleep\" and handling the request).\n */\nexport async function emitAsync<\n  Events extends EventMap,\n  EventName extends keyof Events\n>(\n  emitter: Emitter<Events>,\n  eventName: EventName,\n  ...data: Events[EventName]\n): Promise<void> {\n  const listners = emitter.listeners(eventName)\n\n  if (listners.length === 0) {\n    return\n  }\n\n  for (const listener of listners) {\n    await listener.apply(emitter, data)\n  }\n}\n", "import type { Emitter } from 'strict-event-emitter'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { until } from '@open-draft/until'\nimport type { HttpRequestEventMap } from '../glossary'\nimport { emitAsync } from './emitAsync'\nimport { k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request<PERSON>ontroller } from '../RequestController'\nimport {\n  createServerErrorResponse,\n  isResponseError,\n  isResponseLike,\n  ResponseError,\n} from './responseUtils'\nimport { InterceptorError } from '../InterceptorError'\nimport { isNodeLikeError } from './isNodeLikeError'\nimport { isObject } from './isObject'\n\ninterface HandleRequestOptions {\n  requestId: string\n  request: Request\n  emitter: Emitter<HttpRequestEventMap>\n  controller: RequestController\n\n  /**\n   * Called when the request has been handled\n   * with the given `Response` instance.\n   */\n  onResponse: (response: Response) => void | Promise<void>\n\n  /**\n   * Called when the request has been handled\n   * with the given `Response.error()` instance.\n   */\n  onRequestError: (response: ResponseError) => void\n\n  /**\n   * Called when an unhandled error happens during the\n   * request handling. This is never a thrown error/response.\n   */\n  onError: (error: unknown) => void\n}\n\n/**\n * @returns {Promise<boolean>} Indicates whether the request has been handled.\n */\nexport async function handleRequest(\n  options: HandleRequestOptions\n): Promise<boolean> {\n  const handleResponse = async (\n    response: Response | Error | Record<string, any>\n  ) => {\n    if (response instanceof Error) {\n      options.onError(response)\n      return true\n    }\n\n    // Handle \"Response.error()\" instances.\n    if (isResponseError(response)) {\n      options.onRequestError(response)\n      return true\n    }\n\n    /**\n     * Handle normal responses or response-like objects.\n     * @note This must come before the arbitrary object check\n     * since Response instances are, in fact, objects.\n     */\n    if (isResponseLike(response)) {\n      await options.onResponse(response)\n      return true\n    }\n\n    // Handle arbitrary objects provided to `.errorWith(reason)`.\n    if (isObject(response)) {\n      options.onError(response)\n      return true\n    }\n\n    return false\n  }\n\n  const handleResponseError = async (error: unknown): Promise<boolean> => {\n    // Forward the special interceptor error instances\n    // to the developer. These must not be handled in any way.\n    if (error instanceof InterceptorError) {\n      throw result.error\n    }\n\n    // Support mocking Node.js-like errors.\n    if (isNodeLikeError(error)) {\n      options.onError(error)\n      return true\n    }\n\n    // Handle thrown responses.\n    if (error instanceof Response) {\n      return await handleResponse(error)\n    }\n\n    return false\n  }\n\n  // Add the last \"request\" listener to check if the request\n  // has been handled in any way. If it hasn't, resolve the\n  // response promise with undefined.\n  options.emitter.once('request', ({ requestId: pendingRequestId }) => {\n    if (pendingRequestId !== options.requestId) {\n      return\n    }\n\n    if (options.controller[kResponsePromise].state === 'pending') {\n      options.controller[kResponsePromise].resolve(undefined)\n    }\n  })\n\n  const requestAbortPromise = new DeferredPromise<void, unknown>()\n\n  /**\n   * @note `signal` is not always defined in React Native.\n   */\n  if (options.request.signal) {\n    if (options.request.signal.aborted) {\n      requestAbortPromise.reject(options.request.signal.reason)\n    } else {\n      options.request.signal.addEventListener(\n        'abort',\n        () => {\n          requestAbortPromise.reject(options.request.signal.reason)\n        },\n        { once: true }\n      )\n    }\n  }\n\n  const result = await until(async () => {\n    // Emit the \"request\" event and wait until all the listeners\n    // for that event are finished (e.g. async listeners awaited).\n    // By the end of this promise, the developer cannot affect the\n    // request anymore.\n    const requestListenersPromise = emitAsync(options.emitter, 'request', {\n      requestId: options.requestId,\n      request: options.request,\n      controller: options.controller,\n    })\n\n    await Promise.race([\n      // Short-circuit the request handling promise if the request gets aborted.\n      requestAbortPromise,\n      requestListenersPromise,\n      options.controller[kResponsePromise],\n    ])\n\n    // The response promise will settle immediately once\n    // the developer calls either \"respondWith\" or \"errorWith\".\n    return await options.controller[kResponsePromise]\n  })\n\n  // Handle the request being aborted while waiting for the request listeners.\n  if (requestAbortPromise.state === 'rejected') {\n    options.onError(requestAbortPromise.rejectionReason)\n    return true\n  }\n\n  if (result.error) {\n    // Handle the error during the request listener execution.\n    // These can be thrown responses or request errors.\n    if (await handleResponseError(result.error)) {\n      return true\n    }\n\n    // If the developer has added \"unhandledException\" listeners,\n    // allow them to handle the error. They can translate it to a\n    // mocked response, network error, or forward it as-is.\n    if (options.emitter.listenerCount('unhandledException') > 0) {\n      // Create a new request controller just for the unhandled exception case.\n      // This is needed because the original controller might have been already\n      // interacted with (e.g. \"respondWith\" or \"errorWith\" called on it).\n      const unhandledExceptionController = new RequestController(\n        options.request\n      )\n\n      await emitAsync(options.emitter, 'unhandledException', {\n        error: result.error,\n        request: options.request,\n        requestId: options.requestId,\n        controller: unhandledExceptionController,\n      }).then(() => {\n        // If all the \"unhandledException\" listeners have finished\n        // but have not handled the response in any way, preemptively\n        // resolve the pending response promise from the new controller.\n        // This prevents it from hanging forever.\n        if (\n          unhandledExceptionController[kResponsePromise].state === 'pending'\n        ) {\n          unhandledExceptionController[kResponsePromise].resolve(undefined)\n        }\n      })\n\n      const nextResult = await until(\n        () => unhandledExceptionController[kResponsePromise]\n      )\n\n      /**\n       * @note Handle the result of the unhandled controller\n       * in the same way as the original request controller.\n       * The exception here is that thrown errors within the\n       * \"unhandledException\" event do NOT result in another\n       * emit of the same event. They are forwarded as-is.\n       */\n      if (nextResult.error) {\n        return handleResponseError(nextResult.error)\n      }\n\n      if (nextResult.data) {\n        return handleResponse(nextResult.data)\n      }\n    }\n\n    // Otherwise, coerce unhandled exceptions to a 500 Internal Server Error response.\n    options.onResponse(createServerErrorResponse(result.error))\n    return true\n  }\n\n  /**\n   * Handle a mocked Response instance.\n   * @note That this can also be an Error in case\n   * the developer called \"errorWith\". This differentiates\n   * unhandled exceptions from intended errors.\n   */\n  if (result.data) {\n    return handleResponse(result.data)\n  }\n\n  // In all other cases, consider the request unhandled.\n  return false\n}\n", "/**\n * Determines if a given value is an instance of object.\n */\nexport function isObject<T>(value: any, loose = false): value is T {\n  return loose\n    ? Object.prototype.toString.call(value).startsWith('[object ')\n    : Object.prototype.toString.call(value) === '[object Object]'\n}\n", "/**\n * A function that validates if property access is possible on an object\n * without throwing. It returns `true` if the property access is possible\n * and `false` otherwise.\n *\n * Environments like miniflare will throw on property access on certain objects\n * like Request and Response, for unimplemented properties.\n */\nexport function isPropertyAccessible<Obj extends Record<string, any>>(\n  obj: Obj,\n  key: keyof Obj\n) {\n  try {\n    obj[key]\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { isObject } from './isObject'\nimport { isPropertyAccessible } from './isPropertyAccessible'\n\n/**\n * Creates a generic 500 Unhandled Exception response.\n */\nexport function createServerErrorResponse(body: unknown): Response {\n  return new Response(\n    JSON.stringify(\n      body instanceof Error\n        ? {\n            name: body.name,\n            message: body.message,\n            stack: body.stack,\n          }\n        : body\n    ),\n    {\n      status: 500,\n      statusText: 'Unhandled Exception',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    }\n  )\n}\n\nexport type ResponseError = Response & { type: 'error' }\n\n/**\n * Check if the given response is a `Response.error()`.\n *\n * @note Some environments, like Miniflare (Cloudflare) do not\n * implement the \"Response.type\" property and throw on its access.\n * Safely check if we can access \"type\" on \"Response\" before continuing.\n * @see https://github.com/mswjs/msw/issues/1834\n */\nexport function isResponseError(response: unknown): response is ResponseError {\n  return (\n    response != null &&\n    response instanceof Response &&\n    isPropertyAccessible(response, 'type') &&\n    response.type === 'error'\n  )\n}\n\n/**\n * Check if the given value is a `Response` or a Response-like object.\n * This is different from `value instanceof Response` because it supports\n * custom `Response` constructors, like the one when using Undici directly.\n */\nexport function isResponseLike(value: unknown): value is Response {\n  return (\n    isObject<Record<string, any>>(value, true) &&\n    isPropertyAccessible(value, 'status') &&\n    isPropertyAccessible(value, 'statusText') &&\n    isPropertyAccessible(value, 'bodyUsed')\n  )\n}\n", "export function isNodeLikeError(\n  error: unknown\n): error is NodeJS.ErrnoException {\n  if (error == null) {\n    return false\n  }\n\n  if (!(error instanceof Error)) {\n    return false\n  }\n\n  return 'code' in error && 'errno' in error\n}\n", "/**\n * Returns a boolean indicating whether the given global property\n * is defined and is configurable.\n */\nexport function hasConfigurableGlobal(propertyName: string): boolean {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName)\n\n  // The property is not set at all.\n  if (typeof descriptor === 'undefined') {\n    return false\n  }\n\n  // The property is set to a getter that returns undefined.\n  if (\n    typeof descriptor.get === 'function' &&\n    typeof descriptor.get() === 'undefined'\n  ) {\n    return false\n  }\n\n  // The property is set to a value equal to undefined.\n  if (typeof descriptor.get === 'undefined' && descriptor.value == null) {\n    return false\n  }\n\n  if (typeof descriptor.set === 'undefined' && !descriptor.configurable) {\n    console.error(\n      `[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`\n    )\n    return false\n  }\n\n  return true\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { RequestController } from '../../RequestController'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { handleRequest } from '../../utils/handleRequest'\nimport { canParseUrl } from '../../utils/canParseUrl'\nimport { createRequestId } from '../../createRequestId'\nimport { createNetworkError } from './utils/createNetworkError'\nimport { followFetchRedirect } from './utils/followRedirect'\nimport { decompressResponse } from './utils/decompression'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\nimport { FetchResponse } from '../../utils/fetchUtils'\nimport { setRawRequest } from '../../getRawRequest'\n\nexport class FetchInterceptor extends Interceptor<HttpRequestEventMap> {\n  static symbol = Symbol('fetch')\n\n  constructor() {\n    super(FetchInterceptor.symbol)\n  }\n\n  protected checkEnvironment() {\n    return hasConfigurableGlobal('fetch')\n  }\n\n  protected async setup() {\n    const pureFetch = globalThis.fetch\n\n    invariant(\n      !(pureFetch as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"fetch\" module: already patched.'\n    )\n\n    globalThis.fetch = async (input, init) => {\n      const requestId = createRequestId()\n\n      /**\n       * @note Resolve potentially relative request URL\n       * against the present `location`. This is mainly\n       * for native `fetch` in JSDOM.\n       * @see https://github.com/mswjs/msw/issues/1625\n       */\n      const resolvedInput =\n        typeof input === 'string' &&\n        typeof location !== 'undefined' &&\n        !canParseUrl(input)\n          ? new URL(input, location.href)\n          : input\n\n      const request = new Request(resolvedInput, init)\n\n      /**\n       * @note Set the raw request only if a Request instance was provided to fetch.\n       */\n      if (input instanceof Request) {\n        setRawRequest(request, input)\n      }\n\n      const responsePromise = new DeferredPromise<Response>()\n      const controller = new RequestController(request)\n\n      this.logger.info('[%s] %s', request.method, request.url)\n      this.logger.info('awaiting for the mocked response...')\n\n      this.logger.info(\n        'emitting the \"request\" event for %s listener(s)...',\n        this.emitter.listenerCount('request')\n      )\n\n      const isRequestHandled = await handleRequest({\n        request,\n        requestId,\n        emitter: this.emitter,\n        controller,\n        onResponse: async (rawResponse) => {\n          this.logger.info('received mocked response!', {\n            rawResponse,\n          })\n\n          // Decompress the mocked response body, if applicable.\n          const decompressedStream = decompressResponse(rawResponse)\n          const response =\n            decompressedStream === null\n              ? rawResponse\n              : new FetchResponse(decompressedStream, rawResponse)\n\n          FetchResponse.setUrl(request.url, response)\n\n          /**\n           * Undici's handling of following redirect responses.\n           * Treat the \"manual\" redirect mode as a regular mocked response.\n           * This way, the client can manually follow the redirect it receives.\n           * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/index.js#L1173\n           */\n          if (FetchResponse.isRedirectResponse(response.status)) {\n            // Reject the request promise if its `redirect` is set to `error`\n            // and it receives a mocked redirect response.\n            if (request.redirect === 'error') {\n              responsePromise.reject(createNetworkError('unexpected redirect'))\n              return\n            }\n\n            if (request.redirect === 'follow') {\n              followFetchRedirect(request, response).then(\n                (response) => {\n                  responsePromise.resolve(response)\n                },\n                (reason) => {\n                  responsePromise.reject(reason)\n                }\n              )\n              return\n            }\n          }\n\n          if (this.emitter.listenerCount('response') > 0) {\n            this.logger.info('emitting the \"response\" event...')\n\n            // Await the response listeners to finish before resolving\n            // the response promise. This ensures all your logic finishes\n            // before the interceptor resolves the pending response.\n            await emitAsync(this.emitter, 'response', {\n              // Clone the mocked response for the \"response\" event listener.\n              // This way, the listener can read the response and not lock its body\n              // for the actual fetch consumer.\n              response: response.clone(),\n              isMockedResponse: true,\n              request,\n              requestId,\n            })\n          }\n\n          responsePromise.resolve(response)\n        },\n        onRequestError: (response) => {\n          this.logger.info('request has errored!', { response })\n          responsePromise.reject(createNetworkError(response))\n        },\n        onError: (error) => {\n          this.logger.info('request has been aborted!', { error })\n          responsePromise.reject(error)\n        },\n      })\n\n      if (isRequestHandled) {\n        this.logger.info('request has been handled, returning mock promise...')\n        return responsePromise\n      }\n\n      this.logger.info(\n        'no mocked response received, performing request as-is...'\n      )\n\n      /**\n       * @note Clone the request instance right before performing it.\n       * This preserves any modifications made to the intercepted request\n       * in the \"request\" listener. This also allows the user to read the\n       * request body in the \"response\" listener (otherwise \"unusable\").\n       */\n      const requestCloneForResponseEvent = request.clone()\n\n      return pureFetch(request).then(async (response) => {\n        this.logger.info('original fetch performed', response)\n\n        if (this.emitter.listenerCount('response') > 0) {\n          this.logger.info('emitting the \"response\" event...')\n\n          const responseClone = response.clone()\n\n          await emitAsync(this.emitter, 'response', {\n            response: responseClone,\n            isMockedResponse: false,\n            request: requestCloneForResponseEvent,\n            requestId,\n          })\n        }\n\n        return response\n      })\n    }\n\n    Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.fetch = pureFetch\n\n      this.logger.info(\n        'restored native \"globalThis.fetch\"!',\n        globalThis.fetch.name\n      )\n    })\n  }\n}\n", "export function createNetworkError(cause?: unknown) {\n  return Object.assign(new TypeError('Failed to fetch'), {\n    cause,\n  })\n}\n", "import { createNetworkError } from './createNetworkError'\n\nconst REQUEST_BODY_HEADERS = [\n  'content-encoding',\n  'content-language',\n  'content-location',\n  'content-type',\n  'content-length',\n]\n\nconst kRedirectCount = Symbol('kRedirectCount')\n\n/**\n * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/index.js#L1210\n */\nexport async function followFetchRedirect(\n  request: Request,\n  response: Response\n): Promise<Response> {\n  if (response.status !== 303 && request.body != null) {\n    return Promise.reject(createNetworkError())\n  }\n\n  const requestUrl = new URL(request.url)\n\n  let locationUrl: URL\n  try {\n    // If the location is a relative URL, use the request URL as the base URL.\n    locationUrl = new URL(response.headers.get('location')!, request.url) \n  } catch (error) {\n    return Promise.reject(createNetworkError(error))\n  }\n\n  if (\n    !(locationUrl.protocol === 'http:' || locationUrl.protocol === 'https:')\n  ) {\n    return Promise.reject(\n      createNetworkError('URL scheme must be a HTTP(S) scheme')\n    )\n  }\n\n  if (Reflect.get(request, kRedirectCount) > 20) {\n    return Promise.reject(createNetworkError('redirect count exceeded'))\n  }\n\n  Object.defineProperty(request, kRedirectCount, {\n    value: (Reflect.get(request, kRedirectCount) || 0) + 1,\n  })\n\n  if (\n    request.mode === 'cors' &&\n    (locationUrl.username || locationUrl.password) &&\n    !sameOrigin(requestUrl, locationUrl)\n  ) {\n    return Promise.reject(\n      createNetworkError('cross origin not allowed for request mode \"cors\"')\n    )\n  }\n\n  const requestInit: RequestInit = {}\n\n  if (\n    ([301, 302].includes(response.status) && request.method === 'POST') ||\n    (response.status === 303 && !['HEAD', 'GET'].includes(request.method))\n  ) {\n    requestInit.method = 'GET'\n    requestInit.body = null\n\n    REQUEST_BODY_HEADERS.forEach((headerName) => {\n      request.headers.delete(headerName)\n    })\n  }\n\n  if (!sameOrigin(requestUrl, locationUrl)) {\n    request.headers.delete('authorization')\n    request.headers.delete('proxy-authorization')\n    request.headers.delete('cookie')\n    request.headers.delete('host')\n  }\n\n  /**\n   * @note Undici \"safely\" extracts the request body.\n   * I suspect we cannot dispatch this request again\n   * since its body has been read and the stream is locked.\n   */\n\n  requestInit.headers = request.headers\n  return fetch(new Request(locationUrl, requestInit))\n}\n\n/**\n * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/util.js#L761\n */\nfunction sameOrigin(left: URL, right: URL): boolean {\n  if (left.origin === right.origin && left.origin === 'null') {\n    return true\n  }\n\n  if (\n    left.protocol === right.protocol &&\n    left.hostname === right.hostname &&\n    left.port === right.port\n  ) {\n    return true\n  }\n\n  return false\n}\n", "export class BrotliDecompressionStream extends TransformStream {\n  constructor() {\n    console.warn(\n      '[Interceptors]: Brotli decompression of response streams is not supported in the browser'\n    )\n\n    super({\n      transform(chunk, controller) {\n        // Keep the stream as passthrough, it does nothing.\n        controller.enqueue(chunk)\n      },\n    })\n  }\n}\n", "// Import from an internal alias that resolves to different modules\n// depending on the environment. This way, we can keep the fetch interceptor\n// intact while using different strategies for Brotli decompression.\nimport { BrotliDecompressionStream } from 'internal:brotli-decompress'\n\nclass PipelineStream extends TransformStream {\n  constructor(\n    transformStreams: Array<TransformStream>,\n    ...strategies: Array<QueuingStrategy>\n  ) {\n    super({}, ...strategies)\n\n    const readable = [super.readable as any, ...transformStreams].reduce(\n      (readable, transform) => readable.pipeThrough(transform)\n    )\n\n    Object.defineProperty(this, 'readable', {\n      get() {\n        return readable\n      },\n    })\n  }\n}\n\nexport function parseContentEncoding(contentEncoding: string): Array<string> {\n  return contentEncoding\n    .toLowerCase()\n    .split(',')\n    .map((coding) => coding.trim())\n}\n\nfunction createDecompressionStream(\n  contentEncoding: string\n): TransformStream | null {\n  if (contentEncoding === '') {\n    return null\n  }\n\n  const codings = parseContentEncoding(contentEncoding)\n\n  if (codings.length === 0) {\n    return null\n  }\n\n  const transformers = codings.reduceRight<Array<TransformStream>>(\n    (transformers, coding) => {\n      if (coding === 'gzip' || coding === 'x-gzip') {\n        return transformers.concat(new DecompressionStream('gzip'))\n      } else if (coding === 'deflate') {\n        return transformers.concat(new DecompressionStream('deflate'))\n      } else if (coding === 'br') {\n        return transformers.concat(new BrotliDecompressionStream())\n      } else {\n        transformers.length = 0\n      }\n\n      return transformers\n    },\n    []\n  )\n\n  return new PipelineStream(transformers)\n}\n\nexport function decompressResponse(\n  response: Response\n): ReadableStream<any> | null {\n  if (response.body === null) {\n    return null\n  }\n\n  const decompressionStream = createDecompressionStream(\n    response.headers.get('content-encoding') || ''\n  )\n\n  if (!decompressionStream) {\n    return null\n  }\n\n  // Use `pipeTo` and return the decompression stream's readable\n  // instead of `pipeThrough` because that will lock the original\n  // response stream, making it unusable as the input to Response.\n  response.body.pipeTo(decompressionStream.writable)\n  return decompressionStream.readable\n}\n", "import { invariant } from 'outvariant'\nimport { Emitter } from 'strict-event-emitter'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { createXMLHttpRequestProxy } from './XMLHttpRequestProxy'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\n\nexport type XMLHttpRequestEmitter = Emitter<HttpRequestEventMap>\n\nexport class XMLHttpRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static interceptorSymbol = Symbol('xhr')\n\n  constructor() {\n    super(XMLHttpRequestInterceptor.interceptorSymbol)\n  }\n\n  protected checkEnvironment() {\n    return hasConfigurableGlobal('XMLHttpRequest')\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('patching \"XMLHttpRequest\" module...')\n\n    const PureXMLHttpRequest = globalThis.XMLHttpRequest\n\n    invariant(\n      !(PureXMLHttpRequest as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"XMLHttpRequest\" module: already patched.'\n    )\n\n    globalThis.XMLHttpRequest = createXMLHttpRequestProxy({\n      emitter: this.emitter,\n      logger: this.logger,\n    })\n\n    logger.info(\n      'native \"XMLHttpRequest\" module patched!',\n      globalThis.XMLHttpRequest.name\n    )\n\n    Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.XMLHttpRequest = PureXMLHttpRequest\n      logger.info(\n        'native \"XMLHttpRequest\" module restored!',\n        globalThis.XMLHttpRequest.name\n      )\n    })\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport type { Logger } from '@open-draft/logger'\nimport { concatArrayBuffer } from './utils/concatArrayBuffer'\nimport { createEvent } from './utils/createEvent'\nimport {\n  decodeBuffer,\n  encodeBuffer,\n  toArrayBuffer,\n} from '../../utils/bufferUtils'\nimport { createProxy } from '../../utils/createProxy'\nimport { isDomParserSupportedType } from './utils/isDomParserSupportedType'\nimport { parseJson } from '../../utils/parseJson'\nimport { createResponse } from './utils/createResponse'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { createRequestId } from '../../createRequestId'\nimport { getBodyByteLength } from './utils/getBodyByteLength'\nimport { setRawRequest } from '../../getRawRequest'\n\nconst kIsRequestHandled = Symbol('kIsRequestHandled')\nconst IS_NODE = isNodeProcess()\nconst kFetchRequest = Symbol('kFetchRequest')\n\n/**\n * An `XMLHttpRequest` instance controller that allows us\n * to handle any given request instance (e.g. responding to it).\n */\nexport class XMLHttpRequestController {\n  public request: XMLHttpRequest\n  public requestId: string\n  public onRequest?: (\n    this: XMLHttpRequestController,\n    args: {\n      request: Request\n      requestId: string\n    }\n  ) => Promise<void>\n  public onResponse?: (\n    this: XMLHttpRequestController,\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ) => void;\n\n  [kIsRequestHandled]: boolean;\n  [kFetchRequest]?: Request\n  private method: string = 'GET'\n  private url: URL = null as any\n  private requestHeaders: Headers\n  private responseBuffer: Uint8Array\n  private events: Map<keyof XMLHttpRequestEventTargetEventMap, Array<Function>>\n  private uploadEvents: Map<\n    keyof XMLHttpRequestEventTargetEventMap,\n    Array<Function>\n  >\n\n  constructor(readonly initialRequest: XMLHttpRequest, public logger: Logger) {\n    this[kIsRequestHandled] = false\n\n    this.events = new Map()\n    this.uploadEvents = new Map()\n    this.requestId = createRequestId()\n    this.requestHeaders = new Headers()\n    this.responseBuffer = new Uint8Array()\n\n    this.request = createProxy(initialRequest, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case 'ontimeout': {\n            const eventName = propertyName.slice(\n              2\n            ) as keyof XMLHttpRequestEventTargetEventMap\n\n            /**\n             * @note Proxy callbacks to event listeners because JSDOM has trouble\n             * translating these properties to callbacks. It seemed to be operating\n             * on events exclusively.\n             */\n            this.request.addEventListener(eventName, nextValue as any)\n\n            return invoke()\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case 'open': {\n            const [method, url] = args as [string, string | undefined]\n\n            if (typeof url === 'undefined') {\n              this.method = 'GET'\n              this.url = toAbsoluteUrl(method)\n            } else {\n              this.method = method\n              this.url = toAbsoluteUrl(url)\n            }\n\n            this.logger = this.logger.extend(`${this.method} ${this.url.href}`)\n            this.logger.info('open', this.method, this.url.href)\n\n            return invoke()\n          }\n\n          case 'addEventListener': {\n            const [eventName, listener] = args as [\n              keyof XMLHttpRequestEventTargetEventMap,\n              Function\n            ]\n\n            this.registerEvent(eventName, listener)\n            this.logger.info('addEventListener', eventName, listener)\n\n            return invoke()\n          }\n\n          case 'setRequestHeader': {\n            const [name, value] = args as [string, string]\n            this.requestHeaders.set(name, value)\n\n            this.logger.info('setRequestHeader', name, value)\n\n            return invoke()\n          }\n\n          case 'send': {\n            const [body] = args as [\n              body?: XMLHttpRequestBodyInit | Document | null\n            ]\n\n            this.request.addEventListener('load', () => {\n              if (typeof this.onResponse !== 'undefined') {\n                // Create a Fetch API Response representation of whichever\n                // response this XMLHttpRequest received. Note those may\n                // be either a mocked and the original response.\n                const fetchResponse = createResponse(\n                  this.request,\n                  /**\n                   * The `response` property is the right way to read\n                   * the ambiguous response body, as the request's \"responseType\" may differ.\n                   * @see https://xhr.spec.whatwg.org/#the-response-attribute\n                   */\n                  this.request.response\n                )\n\n                // Notify the consumer about the response.\n                this.onResponse.call(this, {\n                  response: fetchResponse,\n                  isMockedResponse: this[kIsRequestHandled],\n                  request: fetchRequest,\n                  requestId: this.requestId!,\n                })\n              }\n            })\n\n            const requestBody =\n              typeof body === 'string' ? encodeBuffer(body) : body\n\n            // Delegate request handling to the consumer.\n            const fetchRequest = this.toFetchApiRequest(requestBody)\n            this[kFetchRequest] = fetchRequest.clone()\n\n            const onceRequestSettled =\n              this.onRequest?.call(this, {\n                request: fetchRequest,\n                requestId: this.requestId!,\n              }) || Promise.resolve()\n\n            onceRequestSettled.finally(() => {\n              // If the consumer didn't handle the request (called `.respondWith()`) perform it as-is.\n              if (!this[kIsRequestHandled]) {\n                this.logger.info(\n                  'request callback settled but request has not been handled (readystate %d), performing as-is...',\n                  this.request.readyState\n                )\n\n                /**\n                 * @note Set the intercepted request ID on the original request in Node.js\n                 * so that if it triggers any other interceptors, they don't attempt\n                 * to process it once again.\n                 *\n                 * For instance, XMLHttpRequest is often implemented via \"http.ClientRequest\"\n                 * and we don't want for both XHR and ClientRequest interceptors to\n                 * handle the same request at the same time (e.g. emit the \"response\" event twice).\n                 */\n                if (IS_NODE) {\n                  this.request.setRequestHeader(\n                    INTERNAL_REQUEST_ID_HEADER_NAME,\n                    this.requestId!\n                  )\n                }\n\n                return invoke()\n              }\n            })\n\n            break\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n    })\n\n    /**\n     * Proxy the `.upload` property to gather the event listeners/callbacks.\n     */\n    define(\n      this.request,\n      'upload',\n      createProxy(this.request.upload, {\n        setProperty: ([propertyName, nextValue], invoke) => {\n          switch (propertyName) {\n            case 'onloadstart':\n            case 'onprogress':\n            case 'onaboart':\n            case 'onerror':\n            case 'onload':\n            case 'ontimeout':\n            case 'onloadend': {\n              const eventName = propertyName.slice(\n                2\n              ) as keyof XMLHttpRequestEventTargetEventMap\n\n              this.registerUploadEvent(eventName, nextValue as Function)\n            }\n          }\n\n          return invoke()\n        },\n        methodCall: ([methodName, args], invoke) => {\n          switch (methodName) {\n            case 'addEventListener': {\n              const [eventName, listener] = args as [\n                keyof XMLHttpRequestEventTargetEventMap,\n                Function\n              ]\n              this.registerUploadEvent(eventName, listener)\n              this.logger.info('upload.addEventListener', eventName, listener)\n\n              return invoke()\n            }\n          }\n        },\n      })\n    )\n  }\n\n  private registerEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.events.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.events.set(eventName, nextEvents)\n\n    this.logger.info('registered event \"%s\"', eventName, listener)\n  }\n\n  private registerUploadEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.uploadEvents.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.uploadEvents.set(eventName, nextEvents)\n\n    this.logger.info('registered upload event \"%s\"', eventName, listener)\n  }\n\n  /**\n   * Responds to the current request with the given\n   * Fetch API `Response` instance.\n   */\n  public async respondWith(response: Response): Promise<void> {\n    /**\n     * @note Since `XMLHttpRequestController` delegates the handling of the responses\n     * to the \"load\" event listener that doesn't distinguish between the mocked and original\n     * responses, mark the request that had a mocked response with a corresponding symbol.\n     *\n     * Mark this request as having a mocked response immediately since\n     * calculating request/response total body length is asynchronous.\n     */\n    this[kIsRequestHandled] = true\n\n    /**\n     * Dispatch request upload events for requests with a body.\n     * @see https://github.com/mswjs/interceptors/issues/573\n     */\n    if (this[kFetchRequest]) {\n      const totalRequestBodyLength = await getBodyByteLength(\n        this[kFetchRequest]\n      )\n\n      this.trigger('loadstart', this.request.upload, {\n        loaded: 0,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('progress', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('load', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('loadend', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n    }\n\n    this.logger.info(\n      'responding with a mocked response: %d %s',\n      response.status,\n      response.statusText\n    )\n\n    define(this.request, 'status', response.status)\n    define(this.request, 'statusText', response.statusText)\n    define(this.request, 'responseURL', this.url.href)\n\n    this.request.getResponseHeader = new Proxy(this.request.getResponseHeader, {\n      apply: (_, __, args: [name: string]) => {\n        this.logger.info('getResponseHeader', args[0])\n\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info('headers not received yet, returning null')\n\n          // Headers not received yet, nothing to return.\n          return null\n        }\n\n        const headerValue = response.headers.get(args[0])\n        this.logger.info(\n          'resolved response header \"%s\" to',\n          args[0],\n          headerValue\n        )\n\n        return headerValue\n      },\n    })\n\n    this.request.getAllResponseHeaders = new Proxy(\n      this.request.getAllResponseHeaders,\n      {\n        apply: () => {\n          this.logger.info('getAllResponseHeaders')\n\n          if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n            this.logger.info('headers not received yet, returning empty string')\n\n            // Headers not received yet, nothing to return.\n            return ''\n          }\n\n          const headersList = Array.from(response.headers.entries())\n          const allHeaders = headersList\n            .map(([headerName, headerValue]) => {\n              return `${headerName}: ${headerValue}`\n            })\n            .join('\\r\\n')\n\n          this.logger.info('resolved all response headers to', allHeaders)\n\n          return allHeaders\n        },\n      }\n    )\n\n    // Update the response getters to resolve against the mocked response.\n    Object.defineProperties(this.request, {\n      response: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.response,\n      },\n      responseText: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseText,\n      },\n      responseXML: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseXML,\n      },\n    })\n\n    const totalResponseBodyLength = await getBodyByteLength(response.clone())\n\n    this.logger.info('calculated response body length', totalResponseBodyLength)\n\n    this.trigger('loadstart', this.request, {\n      loaded: 0,\n      total: totalResponseBodyLength,\n    })\n\n    this.setReadyState(this.request.HEADERS_RECEIVED)\n    this.setReadyState(this.request.LOADING)\n\n    const finalizeResponse = () => {\n      this.logger.info('finalizing the mocked response...')\n\n      this.setReadyState(this.request.DONE)\n\n      this.trigger('load', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n\n      this.trigger('loadend', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n    }\n\n    if (response.body) {\n      this.logger.info('mocked response has body, streaming...')\n\n      const reader = response.body.getReader()\n\n      const readNextResponseBodyChunk = async () => {\n        const { value, done } = await reader.read()\n\n        if (done) {\n          this.logger.info('response body stream done!')\n          finalizeResponse()\n          return\n        }\n\n        if (value) {\n          this.logger.info('read response body chunk:', value)\n          this.responseBuffer = concatArrayBuffer(this.responseBuffer, value)\n\n          this.trigger('progress', this.request, {\n            loaded: this.responseBuffer.byteLength,\n            total: totalResponseBodyLength,\n          })\n        }\n\n        readNextResponseBodyChunk()\n      }\n\n      readNextResponseBodyChunk()\n    } else {\n      finalizeResponse()\n    }\n  }\n\n  private responseBufferToText(): string {\n    return decodeBuffer(this.responseBuffer)\n  }\n\n  get response(): unknown {\n    this.logger.info(\n      'getResponse (responseType: %s)',\n      this.request.responseType\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    switch (this.request.responseType) {\n      case 'json': {\n        const responseJson = parseJson(this.responseBufferToText())\n        this.logger.info('resolved response JSON', responseJson)\n\n        return responseJson\n      }\n\n      case 'arraybuffer': {\n        const arrayBuffer = toArrayBuffer(this.responseBuffer)\n        this.logger.info('resolved response ArrayBuffer', arrayBuffer)\n\n        return arrayBuffer\n      }\n\n      case 'blob': {\n        const mimeType =\n          this.request.getResponseHeader('Content-Type') || 'text/plain'\n        const responseBlob = new Blob([this.responseBufferToText()], {\n          type: mimeType,\n        })\n\n        this.logger.info(\n          'resolved response Blob (mime type: %s)',\n          responseBlob,\n          mimeType\n        )\n\n        return responseBlob\n      }\n\n      default: {\n        const responseText = this.responseBufferToText()\n        this.logger.info(\n          'resolving \"%s\" response type as text',\n          this.request.responseType,\n          responseText\n        )\n\n        return responseText\n      }\n    }\n  }\n\n  get responseText(): string {\n    /**\n     * Throw when trying to read the response body as text when the\n     * \"responseType\" doesn't expect text. This just respects the spec better.\n     * @see https://xhr.spec.whatwg.org/#the-responsetext-attribute\n     */\n    invariant(\n      this.request.responseType === '' || this.request.responseType === 'text',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (\n      this.request.readyState !== this.request.LOADING &&\n      this.request.readyState !== this.request.DONE\n    ) {\n      return ''\n    }\n\n    const responseText = this.responseBufferToText()\n    this.logger.info('getResponseText: \"%s\"', responseText)\n\n    return responseText\n  }\n\n  get responseXML(): Document | null {\n    invariant(\n      this.request.responseType === '' ||\n        this.request.responseType === 'document',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    const contentType = this.request.getResponseHeader('Content-Type') || ''\n\n    if (typeof DOMParser === 'undefined') {\n      console.warn(\n        'Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.'\n      )\n      return null\n    }\n\n    if (isDomParserSupportedType(contentType)) {\n      return new DOMParser().parseFromString(\n        this.responseBufferToText(),\n        contentType\n      )\n    }\n\n    return null\n  }\n\n  public errorWith(error?: Error): void {\n    /**\n     * @note Mark this request as handled even if it received a mock error.\n     * This prevents the controller from trying to perform this request as-is.\n     */\n    this[kIsRequestHandled] = true\n    this.logger.info('responding with an error')\n\n    this.setReadyState(this.request.DONE)\n    this.trigger('error', this.request)\n    this.trigger('loadend', this.request)\n  }\n\n  /**\n   * Transitions this request's `readyState` to the given one.\n   */\n  private setReadyState(nextReadyState: number): void {\n    this.logger.info(\n      'setReadyState: %d -> %d',\n      this.request.readyState,\n      nextReadyState\n    )\n\n    if (this.request.readyState === nextReadyState) {\n      this.logger.info('ready state identical, skipping transition...')\n      return\n    }\n\n    define(this.request, 'readyState', nextReadyState)\n\n    this.logger.info('set readyState to: %d', nextReadyState)\n\n    if (nextReadyState !== this.request.UNSENT) {\n      this.logger.info('triggerring \"readystatechange\" event...')\n\n      this.trigger('readystatechange', this.request)\n    }\n  }\n\n  /**\n   * Triggers given event on the `XMLHttpRequest` instance.\n   */\n  private trigger<\n    EventName extends keyof (XMLHttpRequestEventTargetEventMap & {\n      readystatechange: ProgressEvent<XMLHttpRequestEventTarget>\n    })\n  >(\n    eventName: EventName,\n    target: XMLHttpRequest | XMLHttpRequestUpload,\n    options?: ProgressEventInit\n  ): void {\n    const callback = (target as XMLHttpRequest)[`on${eventName}`]\n    const event = createEvent(target, eventName, options)\n\n    this.logger.info('trigger \"%s\"', eventName, options || '')\n\n    // Invoke direct callbacks.\n    if (typeof callback === 'function') {\n      this.logger.info('found a direct \"%s\" callback, calling...', eventName)\n      callback.call(target as XMLHttpRequest, event)\n    }\n\n    // Invoke event listeners.\n    const events =\n      target instanceof XMLHttpRequestUpload ? this.uploadEvents : this.events\n\n    for (const [registeredEventName, listeners] of events) {\n      if (registeredEventName === eventName) {\n        this.logger.info(\n          'found %d listener(s) for \"%s\" event, calling...',\n          listeners.length,\n          eventName\n        )\n\n        listeners.forEach((listener) => listener.call(target, event))\n      }\n    }\n  }\n\n  /**\n   * Converts this `XMLHttpRequest` instance into a Fetch API `Request` instance.\n   */\n  private toFetchApiRequest(\n    body: XMLHttpRequestBodyInit | Document | null | undefined\n  ): Request {\n    this.logger.info('converting request to a Fetch API Request...')\n\n    // If the `Document` is used as the body of this XMLHttpRequest,\n    // set its inner text as the Fetch API Request body.\n    const resolvedBody =\n      body instanceof Document ? body.documentElement.innerText : body\n\n    const fetchRequest = new Request(this.url.href, {\n      method: this.method,\n      headers: this.requestHeaders,\n      /**\n       * @see https://xhr.spec.whatwg.org/#cross-origin-credentials\n       */\n      credentials: this.request.withCredentials ? 'include' : 'same-origin',\n      body: ['GET', 'HEAD'].includes(this.method.toUpperCase())\n        ? null\n        : resolvedBody,\n    })\n\n    const proxyHeaders = createProxy(fetchRequest.headers, {\n      methodCall: ([methodName, args], invoke) => {\n        // Forward the latest state of the internal request headers\n        // because the interceptor might have modified them\n        // without responding to the request.\n        switch (methodName) {\n          case 'append':\n          case 'set': {\n            const [headerName, headerValue] = args as [string, string]\n            this.request.setRequestHeader(headerName, headerValue)\n            break\n          }\n\n          case 'delete': {\n            const [headerName] = args as [string]\n            console.warn(\n              `XMLHttpRequest: Cannot remove a \"${headerName}\" header from the Fetch API representation of the \"${fetchRequest.method} ${fetchRequest.url}\" request. XMLHttpRequest headers cannot be removed.`\n            )\n            break\n          }\n        }\n\n        return invoke()\n      },\n    })\n    define(fetchRequest, 'headers', proxyHeaders)\n    setRawRequest(fetchRequest, this.request)\n\n    this.logger.info('converted request to a Fetch API Request!', fetchRequest)\n\n    return fetchRequest\n  }\n}\n\nfunction toAbsoluteUrl(url: string | URL): URL {\n  /**\n   * @note XMLHttpRequest interceptor may run in environments\n   * that implement XMLHttpRequest but don't implement \"location\"\n   * (for example, React Native). If that's the case, return the\n   * input URL as-is (nothing to be relative to).\n   * @see https://github.com/mswjs/msw/issues/1777\n   */\n  if (typeof location === 'undefined') {\n    return new URL(url)\n  }\n\n  return new URL(url.toString(), location.href)\n}\n\nfunction define(\n  target: object,\n  property: string | symbol,\n  value: unknown\n): void {\n  Reflect.defineProperty(target, property, {\n    // Ensure writable properties to allow redefining readonly properties.\n    writable: true,\n    enumerable: true,\n    value,\n  })\n}\n", "/**\n * Concatenate two `Uint8Array` buffers.\n */\nexport function concatArrayBuffer(\n  left: Uint8Array,\n  right: Uint8Array\n): Uint8Array {\n  const result = new Uint8Array(left.byteLength + right.byteLength)\n  result.set(left, 0)\n  result.set(right, left.byteLength)\n  return result\n}\n", "export class EventPolyfill implements Event {\n  readonly NONE = 0\n  readonly CAPTURING_PHASE = 1\n  readonly AT_TARGET = 2\n  readonly BUBBLING_PHASE = 3\n\n  public type: string = ''\n  public srcElement: EventTarget | null = null\n  public target: EventTarget | null\n  public currentTarget: EventTarget | null = null\n  public eventPhase: number = 0\n  public timeStamp: number\n  public isTrusted: boolean = true\n  public composed: boolean = false\n  public cancelable: boolean = true\n  public defaultPrevented: boolean = false\n  public bubbles: boolean = true\n  public lengthComputable: boolean = true\n  public loaded: number = 0\n  public total: number = 0\n\n  cancelBubble: boolean = false\n  returnValue: boolean = true\n\n  constructor(\n    type: string,\n    options?: { target: EventTarget; currentTarget: EventTarget }\n  ) {\n    this.type = type\n    this.target = options?.target || null\n    this.currentTarget = options?.currentTarget || null\n    this.timeStamp = Date.now()\n  }\n\n  public composedPath(): EventTarget[] {\n    return []\n  }\n\n  public initEvent(type: string, bubbles?: boolean, cancelable?: boolean) {\n    this.type = type\n    this.bubbles = !!bubbles\n    this.cancelable = !!cancelable\n  }\n\n  public preventDefault() {\n    this.defaultPrevented = true\n  }\n\n  public stopPropagation() {}\n  public stopImmediatePropagation() {}\n}\n", "import { EventPolyfill } from './EventPolyfill'\n\nexport class ProgressEventPolyfill extends EventPolyfill {\n  readonly lengthComputable: boolean\n  readonly composed: boolean\n  readonly loaded: number\n  readonly total: number\n\n  constructor(type: string, init?: ProgressEventInit) {\n    super(type)\n\n    this.lengthComputable = init?.lengthComputable || false\n    this.composed = init?.composed || false\n    this.loaded = init?.loaded || 0\n    this.total = init?.total || 0\n  }\n}\n", "import { EventPolyfill } from '../polyfills/EventPolyfill'\nimport { ProgressEventPolyfill } from '../polyfills/ProgressEventPolyfill'\n\nconst SUPPORTS_PROGRESS_EVENT = typeof ProgressEvent !== 'undefined'\n\nexport function createEvent(\n  target: XMLHttpRequest | XMLHttpRequestUpload,\n  type: string,\n  init?: ProgressEventInit\n): EventPolyfill | ProgressEvent {\n  const progressEvents = [\n    'error',\n    'progress',\n    'loadstart',\n    'loadend',\n    'load',\n    'timeout',\n    'abort',\n  ]\n\n  /**\n   * `ProgressEvent` is not supported in React Native.\n   * @see https://github.com/mswjs/interceptors/issues/40\n   */\n  const ProgressEventClass = SUPPORTS_PROGRESS_EVENT\n    ? ProgressEvent\n    : ProgressEventPolyfill\n\n  const event = progressEvents.includes(type)\n    ? new ProgressEventClass(type, {\n        lengthComputable: true,\n        loaded: init?.loaded || 0,\n        total: init?.total || 0,\n      })\n    : new EventPolyfill(type, {\n        target,\n        currentTarget: target,\n      })\n\n  return event\n}\n", "/**\n * Returns the source object of the given property on the target object\n * (the target itself, any parent in its prototype, or null).\n */\nexport function findPropertySource(\n  target: object,\n  propertyName: string | symbol\n): object | null {\n  if (!(propertyName in target)) {\n    return null\n  }\n\n  const hasProperty = Object.prototype.hasOwnProperty.call(target, propertyName)\n  if (hasProperty) {\n    return target\n  }\n\n  const prototype = Reflect.getPrototypeOf(target)\n  return prototype ? findPropertySource(prototype, propertyName) : null\n}\n", "import { findPropertySource } from './findPropertySource'\n\nexport interface ProxyOptions<Target extends Record<string, any>> {\n  constructorCall?(args: Array<unknown>, next: NextFunction<Target>): Target\n\n  methodCall?<F extends keyof Target>(\n    this: Target,\n    data: [methodName: F, args: Array<unknown>],\n    next: NextFunction<void>\n  ): void\n\n  setProperty?(\n    data: [propertyName: string | symbol, nextValue: unknown],\n    next: NextFunction<boolean>\n  ): boolean\n\n  getProperty?(\n    data: [propertyName: string | symbol, receiver: Target],\n    next: NextFunction<void>\n  ): void\n}\n\nexport type NextFunction<ReturnType> = () => ReturnType\n\nexport function createProxy<Target extends object>(\n  target: Target,\n  options: ProxyOptions<Target>\n): Target {\n  const proxy = new Proxy(target, optionsToProxyHandler(options))\n\n  return proxy\n}\n\nfunction optionsToProxyHandler<T extends Record<string, any>>(\n  options: ProxyOptions<T>\n): ProxyHandler<T> {\n  const { constructorCall, methodCall, getProperty, setProperty } = options\n  const handler: Proxy<PERSON>andler<T> = {}\n\n  if (typeof constructorCall !== 'undefined') {\n    handler.construct = function (target, args, newTarget) {\n      const next = Reflect.construct.bind(null, target as any, args, newTarget)\n      return constructorCall.call(newTarget, args, next)\n    }\n  }\n\n  handler.set = function (target, propertyName, nextValue) {\n    const next = () => {\n      const propertySource = findPropertySource(target, propertyName) || target\n      const ownDescriptors = Reflect.getOwnPropertyDescriptor(\n        propertySource,\n        propertyName\n      )\n\n      // Respect any custom setters present for this property.\n      if (typeof ownDescriptors?.set !== 'undefined') {\n        ownDescriptors.set.apply(target, [nextValue])\n        return true\n      }\n\n      // Otherwise, set the property on the source.\n      return Reflect.defineProperty(propertySource, propertyName, {\n        writable: true,\n        enumerable: true,\n        configurable: true,\n        value: nextValue,\n      })\n    }\n\n    if (typeof setProperty !== 'undefined') {\n      return setProperty.call(target, [propertyName, nextValue], next)\n    }\n\n    return next()\n  }\n\n  handler.get = function (target, propertyName, receiver) {\n    /**\n     * @note Using `Reflect.get()` here causes \"TypeError: Illegal invocation\".\n     */\n    const next = () => target[propertyName as any]\n\n    const value =\n      typeof getProperty !== 'undefined'\n        ? getProperty.call(target, [propertyName, receiver], next)\n        : next()\n\n    if (typeof value === 'function') {\n      return (...args: Array<any>) => {\n        const next = value.bind(target, ...args)\n\n        if (typeof methodCall !== 'undefined') {\n          return methodCall.call(target, [propertyName as any, args], next)\n        }\n\n        return next()\n      }\n    }\n\n    return value\n  }\n\n  return handler\n}\n", "export function isDomParserSupportedType(\n  type: string\n): type is DOMParserSupportedType {\n  const supportedTypes: Array<DOMParserSupportedType> = [\n    'application/xhtml+xml',\n    'application/xml',\n    'image/svg+xml',\n    'text/html',\n    'text/xml',\n  ]\n  return supportedTypes.some((supportedType) => {\n    return type.startsWith(supportedType)\n  })\n}\n", "/**\n * Parses a given string into JSON.\n * Gracefully handles invalid JSON by returning `null`.\n */\nexport function parseJson(data: string): Record<string, unknown> | null {\n  try {\n    const json = JSON.parse(data)\n    return json\n  } catch (_) {\n    return null\n  }\n}\n", "import { FetchResponse } from '../../../utils/fetchUtils'\n\n/**\n * Creates a Fetch API `Response` instance from the given\n * `XMLHttpRequest` instance and a response body.\n */\nexport function createResponse(\n  request: XMLHttpRequest,\n  body: BodyInit | null\n): Response {\n  /**\n   * Handle XMLHttpRequest responses that must have null as the\n   * response body when represented using Fetch API Response.\n   * XMLHttpRequest response will always have an empty string\n   * as the \"request.response\" in those cases, resulting in an error\n   * when constructing a Response instance.\n   * @see https://github.com/mswjs/interceptors/issues/379\n   */\n  const responseBodyOrNull = FetchResponse.isResponseWithBody(request.status)\n    ? body\n    : null\n\n  return new FetchResponse(responseBodyOrNull, {\n    url: request.responseURL,\n    status: request.status,\n    statusText: request.statusText,\n    headers: createHeadersFromXMLHttpReqestHeaders(\n      request.getAllResponseHeaders()\n    ),\n  })\n}\n\nfunction createHeadersFromXMLHttpReqestHeaders(headersString: string): Headers {\n  const headers = new Headers()\n\n  const lines = headersString.split(/[\\r\\n]+/)\n  for (const line of lines) {\n    if (line.trim() === '') {\n      continue\n    }\n\n    const [name, ...parts] = line.split(': ')\n    const value = parts.join(': ')\n\n    headers.append(name, value)\n  }\n\n  return headers\n}\n", "/**\n * Return a total byte length of the given request/response body.\n * If the `Content-Length` header is present, it will be used as the byte length.\n */\nexport async function getBodyByteLength(\n  input: Request | Response\n): Promise<number> {\n  const explicitContentLength = input.headers.get('content-length')\n\n  if (explicitContentLength != null && explicitContentLength !== '') {\n    return Number(explicitContentLength)\n  }\n\n  const buffer = await input.arrayBuffer()\n  return buffer.byteLength\n}\n", "import type { Logger } from '@open-draft/logger'\nimport { XMLHttpRequestEmitter } from '.'\nimport { RequestController } from '../../RequestController'\nimport { XMLHttpRequestController } from './XMLHttpRequestController'\nimport { handleRequest } from '../../utils/handleRequest'\n\nexport interface XMLHttpRequestProxyOptions {\n  emitter: XMLHttpRequestEmitter\n  logger: Logger\n}\n\n/**\n * Create a proxied `XMLHttpRequest` class.\n * The proxied class establishes spies on certain methods,\n * allowing us to intercept requests and respond to them.\n */\nexport function createXMLHttpRequestProxy({\n  emitter,\n  logger,\n}: XMLHttpRequestProxyOptions) {\n  const XMLHttpRequestProxy = new Proxy(globalThis.XMLHttpRequest, {\n    construct(target, args, newTarget) {\n      logger.info('constructed new XMLHttpRequest')\n\n      const originalRequest = Reflect.construct(\n        target,\n        args,\n        newTarget\n      ) as XMLHttpRequest\n\n      /**\n       * @note Forward prototype descriptors onto the proxied object.\n       * XMLHttpRequest is implemented in JSDOM in a way that assigns\n       * a bunch of descriptors, like \"set responseType()\" on the prototype.\n       * With this propagation, we make sure that those descriptors trigger\n       * when the user operates with the proxied request instance.\n       */\n      const prototypeDescriptors = Object.getOwnPropertyDescriptors(\n        target.prototype\n      )\n      for (const propertyName in prototypeDescriptors) {\n        Reflect.defineProperty(\n          originalRequest,\n          propertyName,\n          prototypeDescriptors[propertyName]\n        )\n      }\n\n      const xhrRequestController = new XMLHttpRequestController(\n        originalRequest,\n        logger\n      )\n\n      xhrRequestController.onRequest = async function ({ request, requestId }) {\n        const controller = new RequestController(request)\n\n        this.logger.info('awaiting mocked response...')\n\n        this.logger.info(\n          'emitting the \"request\" event for %s listener(s)...',\n          emitter.listenerCount('request')\n        )\n\n        const isRequestHandled = await handleRequest({\n          request,\n          requestId,\n          controller,\n          emitter,\n          onResponse: async (response) => {\n            await this.respondWith(response)\n          },\n          onRequestError: () => {\n            this.errorWith(new TypeError('Network error'))\n          },\n          onError: (error) => {\n            this.logger.info('request errored!', { error })\n\n            if (error instanceof Error) {\n              this.errorWith(error)\n            }\n          },\n        })\n\n        if (!isRequestHandled) {\n          this.logger.info(\n            'no mocked response received, performing request as-is...'\n          )\n        }\n      }\n\n      xhrRequestController.onResponse = async function ({\n        response,\n        isMockedResponse,\n        request,\n        requestId,\n      }) {\n        this.logger.info(\n          'emitting the \"response\" event for %s listener(s)...',\n          emitter.listenerCount('response')\n        )\n\n        emitter.emit('response', {\n          response,\n          isMockedResponse,\n          request,\n          requestId,\n        })\n      }\n\n      // Return the proxied request from the controller\n      // so that the controller can react to the consumer's interactions\n      // with this request (opening/sending/etc).\n      return xhrRequestController.request\n    },\n  })\n\n  return XMLHttpRequestProxy\n}\n", "import {\n  Interceptor,\n  BatchInterceptor,\n  HttpRequestEventMap,\n} from '@mswjs/interceptors'\nimport { FetchInterceptor } from '@mswjs/interceptors/fetch'\nimport { XMLHttpRequestInterceptor } from '@mswjs/interceptors/XMLHttpRequest'\nimport { SetupWorkerInternalContext, StartOptions } from '../glossary'\nimport type { RequiredDeep } from '~/core/typeUtils'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport { isHandlerKind } from '~/core/utils/internal/isHandlerKind'\n\nexport function createFallbackRequestListener(\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n): Interceptor<HttpRequestEventMap> {\n  const interceptor = new BatchInterceptor({\n    name: 'fallback',\n    interceptors: [new FetchInterceptor(), new XMLHttpRequestInterceptor()],\n  })\n\n  interceptor.on('request', async ({ request, requestId, controller }) => {\n    const requestCloneForLogs = request.clone()\n\n    const response = await handleRequest(\n      request,\n      requestId,\n      context.getRequestHandlers().filter(isHandlerKind('RequestHandler')),\n      options,\n      context.emitter,\n      {\n        onMockedResponse(_, { handler, parsedResult }) {\n          if (!options.quiet) {\n            context.emitter.once('response:mocked', ({ response }) => {\n              handler.log({\n                request: requestCloneForLogs,\n                response,\n                parsedResult,\n              })\n            })\n          }\n        },\n      },\n    )\n\n    if (response) {\n      controller.respondWith(response)\n    }\n  })\n\n  interceptor.on(\n    'response',\n    ({ response, isMockedResponse, request, requestId }) => {\n      context.emitter.emit(\n        isMockedResponse ? 'response:mocked' : 'response:bypass',\n        {\n          response,\n          request,\n          requestId,\n        },\n      )\n    },\n  )\n\n  interceptor.apply()\n\n  return interceptor\n}\n", "import { createFallbackRequestListener } from './createFallbackRequestListener'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { printStartMessage } from './utils/printStartMessage'\n\nexport function createFallbackStart(\n  context: SetupWorkerInternalContext,\n): StartHandler {\n  return async function start(options) {\n    context.fallbackInterceptor = createFallbackRequestListener(\n      context,\n      options,\n    )\n\n    printStartMessage({\n      message: 'Mocking enabled (fallback mode).',\n      quiet: options.quiet,\n    })\n\n    return undefined\n  }\n}\n", "import { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport function createFallbackStop(\n  context: SetupWorkerInternalContext,\n): StopHandler {\n  return function stop() {\n    context.fallbackInterceptor?.dispose()\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport {\n  SetupWorkerInternalContext,\n  ServiceWorkerIncomingEventsMap,\n  StartReturnType,\n  StopHandler,\n  StartHandler,\n  StartOptions,\n} from './glossary'\nimport { createStartHandler } from './start/createStartHandler'\nimport { createStop } from './stop/createStop'\nimport { ServiceWorkerMessage } from './start/utils/createMessageChannel'\nimport { RequestHandler } from '~/core/handlers/RequestHandler'\nimport { DEFAULT_START_OPTIONS } from './start/utils/prepareStartHandler'\nimport { createFallbackStart } from './start/createFallbackStart'\nimport { createFallbackStop } from './stop/createFallbackStop'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { SetupApi } from '~/core/SetupApi'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport type { LifeCycleEventsMap } from '~/core/sharedOptions'\nimport type { WebSocketHandler } from '~/core/handlers/WebSocketHandler'\nimport { SetupWorker } from './glossary'\nimport { supportsReadableStreamTransfer } from '../utils/supportsReadableStreamTransfer'\nimport { webSocketInterceptor } from '~/core/ws/webSocketInterceptor'\nimport { handleWebSocketEvent } from '~/core/ws/handleWebSocketEvent'\nimport { attachWebSocketLogger } from '~/core/ws/utils/attachWebSocketLogger'\n\ninterface Listener {\n  target: EventTarget\n  eventType: string\n  callback: EventListenerOrEventListenerObject\n}\n\nexport class SetupWorkerApi\n  extends SetupApi<LifeCycleEventsMap>\n  implements SetupWorker\n{\n  private context: SetupWorkerInternalContext\n  private startHandler: StartHandler = null as any\n  private stopHandler: StopHandler = null as any\n  private listeners: Array<Listener>\n\n  constructor(...handlers: Array<RequestHandler | WebSocketHandler>) {\n    super(...handlers)\n\n    invariant(\n      !isNodeProcess(),\n      devUtils.formatMessage(\n        'Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.',\n      ),\n    )\n\n    this.listeners = []\n    this.context = this.createWorkerContext()\n  }\n\n  private createWorkerContext(): SetupWorkerInternalContext {\n    const context: SetupWorkerInternalContext = {\n      // Mocking is not considered enabled until the worker\n      // signals back the successful activation event.\n      isMockingEnabled: false,\n      startOptions: null as any,\n      worker: null,\n      getRequestHandlers: () => {\n        return this.handlersController.currentHandlers()\n      },\n      registration: null,\n      emitter: this.emitter,\n      workerChannel: {\n        on: (eventType, callback) => {\n          this.context.events.addListener<\n            MessageEvent<ServiceWorkerMessage<typeof eventType, any>>\n          >(navigator.serviceWorker, 'message', (event) => {\n            // Avoid messages broadcasted from unrelated workers.\n            if (event.source !== this.context.worker) {\n              return\n            }\n\n            const message = event.data\n\n            if (!message) {\n              return\n            }\n\n            if (message.type === eventType) {\n              callback(event, message)\n            }\n          })\n        },\n        send: (type) => {\n          this.context.worker?.postMessage(type)\n        },\n      },\n      events: {\n        addListener: (target, eventType, callback) => {\n          target.addEventListener(eventType, callback as EventListener)\n          this.listeners.push({\n            eventType,\n            target,\n            callback: callback as EventListener,\n          })\n\n          return () => {\n            target.removeEventListener(eventType, callback as EventListener)\n          }\n        },\n        removeAllListeners: () => {\n          for (const { target, eventType, callback } of this.listeners) {\n            target.removeEventListener(eventType, callback)\n          }\n          this.listeners = []\n        },\n        once: (eventType) => {\n          const bindings: Array<() => void> = []\n\n          return new Promise<\n            ServiceWorkerMessage<\n              typeof eventType,\n              ServiceWorkerIncomingEventsMap[typeof eventType]\n            >\n          >((resolve, reject) => {\n            const handleIncomingMessage = (event: MessageEvent) => {\n              try {\n                const message = event.data\n\n                if (message.type === eventType) {\n                  resolve(message)\n                }\n              } catch (error) {\n                reject(error)\n              }\n            }\n\n            bindings.push(\n              this.context.events.addListener(\n                navigator.serviceWorker,\n                'message',\n                handleIncomingMessage,\n              ),\n              this.context.events.addListener(\n                navigator.serviceWorker,\n                'messageerror',\n                reject,\n              ),\n            )\n          }).finally(() => {\n            bindings.forEach((unbind) => unbind())\n          })\n        },\n      },\n      supports: {\n        serviceWorkerApi:\n          !('serviceWorker' in navigator) || location.protocol === 'file:',\n        readableStreamTransfer: supportsReadableStreamTransfer(),\n      },\n    }\n\n    this.startHandler = context.supports.serviceWorkerApi\n      ? createFallbackStart(context)\n      : createStartHandler(context)\n\n    this.stopHandler = context.supports.serviceWorkerApi\n      ? createFallbackStop(context)\n      : createStop(context)\n\n    return context\n  }\n\n  public async start(options: StartOptions = {}): StartReturnType {\n    if (options.waitUntilReady === true) {\n      devUtils.warn(\n        'The \"waitUntilReady\" option has been deprecated. Please remove it from this \"worker.start()\" call. Follow the recommended Browser integration (https://mswjs.io/docs/integrations/browser) to eliminate any race conditions between the Service Worker registration and any requests made by your application on initial render.',\n      )\n    }\n\n    this.context.startOptions = mergeRight(\n      DEFAULT_START_OPTIONS,\n      options,\n    ) as SetupWorkerInternalContext['startOptions']\n\n    // Enable the WebSocket interception.\n    handleWebSocketEvent({\n      getUnhandledRequestStrategy: () => {\n        return this.context.startOptions.onUnhandledRequest\n      },\n      getHandlers: () => {\n        return this.handlersController.currentHandlers()\n      },\n      onMockedConnection: (connection) => {\n        if (!this.context.startOptions.quiet) {\n          // Attach the logger for mocked connections since\n          // those won't be visible in the browser's devtools.\n          attachWebSocketLogger(connection)\n        }\n      },\n      onPassthroughConnection() {},\n    })\n    webSocketInterceptor.apply()\n\n    this.subscriptions.push(() => {\n      webSocketInterceptor.dispose()\n    })\n\n    return await this.startHandler(this.context.startOptions, options)\n  }\n\n  public stop(): void {\n    super.dispose()\n    this.context.events.removeAllListeners()\n    this.context.emitter.removeAllListeners()\n    this.stopHandler()\n  }\n}\n\n/**\n * Sets up a requests interception in the browser with the given request handlers.\n * @param {RequestHandler[]} handlers List of request handlers.\n *\n * @see {@link https://mswjs.io/docs/api/setup-worker `setupWorker()` API reference}\n */\nexport function setupWorker(\n  ...handlers: Array<RequestHandler | WebSocketHandler>\n): SetupWorker {\n  return new SetupWorkerApi(...handlers)\n}\n", "/**\n * Returns a boolean indicating whether the current browser\n * supports `ReadableStream` as a `Transferable` when posting\n * messages.\n */\nexport function supportsReadableStreamTransfer() {\n  try {\n    const stream = new ReadableStream({\n      start: (controller) => controller.close(),\n    })\n    const message = new MessageChannel()\n    message.port1.postMessage(stream, [stream])\n    return true\n  } catch {\n    return false\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAO,SAAS,eAAe,UAAkC;AAC/D,SAAO;IACL,QAAQ,SAAS;IACjB,YAAY,SAAS;IACrB,SAAS,OAAO,YAAY,SAAS,QAAQ,QAAQ,CAAC;EACxD;AACF;;;ACGO,SAAS,cAAqC,MAAS;AAC5D,SAAO,CACL,UAC0E;AAC1E,WACE,SAAS,QACT,OAAO,UAAU,YACjB,YAAY,SACZ,MAAM,WAAW;EAErB;AACF;;;ACjBO,SAAS,SAAS,OAAqB;AAC5C,SAAO,SAAS,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK;AAC3E;;;ACCO,SAAS,WACd,MACA,OACA;AACA,SAAO,OAAO,QAAQ,KAAK,EAAE;IAC3B,CAAC,QAAQ,CAAC,KAAK,UAAU,MAAM;AAC7B,YAAM,YAAY,OAAO,GAAG;AAE5B,UAAI,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,UAAU,GAAG;AACzD,eAAO,GAAG,IAAI,UAAU,OAAO,UAAU;AACzC,eAAO;MACT;AAEA,UAAI,SAAS,SAAS,KAAK,SAAS,UAAU,GAAG;AAC/C,eAAO,GAAG,IAAI,WAAW,WAAW,UAAU;AAC9C,eAAO;MACT;AAEA,aAAO,GAAG,IAAI;AACd,aAAO;IACT;IACA,OAAO,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;;;ACzBO,SAAS,sBAAsB,cAA+B;AACnE,QAAM,aAAa,OAAO,yBAAyB,YAAY,YAAY;AAG3E,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;EACT;AAGA,MACE,OAAO,WAAW,QAAQ,cAC1B,OAAO,WAAW,IAAI,MAAM,aAC5B;AACA,WAAO;EACT;AAGA,MAAI,OAAO,WAAW,QAAQ,eAAe,WAAW,SAAS,MAAM;AACrE,WAAO;EACT;AAEA,MAAI,OAAO,WAAW,QAAQ,eAAe,CAAC,WAAW,cAAc;AACrE,YAAQ;MACN,mDAAmD,YAAA;IACrD;AACA,WAAO;EACT;AAEA,SAAO;AACT;;;AC/BO,SAAS,UACd,QACA,OACuB;AACvB,SAAO,iBAAiB,OAAO;IAC7B,QAAQ;MACN,OAAO;MACP,YAAY;MACZ,UAAU;IACZ;IACA,eAAe;MACb,OAAO;MACP,YAAY;MACZ,UAAU;IACZ;EACF,CAAC;AAED,SAAO;AACT;ACpBA,IAAM,cAAc,OAAO,aAAa;AACxC,IAAM,oBAAoB,OAAO,mBAAmB;AAS7C,IAAM,yBAAN,cAA8C,aAAgB;EAInE,YAAY,MAAc,MAA2B;AACnD,UAAM,MAAM,IAAI;AAChB,SAAK,WAAW,IAAI,CAAC,CAAC,KAAK;AAC3B,SAAK,iBAAiB,IAAI;EAC5B;EAEA,IAAI,aAAa;AACf,WAAO,KAAK,WAAW;EACzB;EAEA,IAAI,WAAW,gBAAgB;AAC7B,SAAK,WAAW,IAAI;EACtB;EAEA,IAAI,mBAAmB;AACrB,WAAO,KAAK,iBAAiB;EAC/B;EAEA,IAAI,iBAAiB,sBAAsB;AACzC,SAAK,iBAAiB,IAAI;EAC5B;EAEO,iBAAuB;AAC5B,QAAI,KAAK,cAAc,CAAC,KAAK,iBAAiB,GAAG;AAC/C,WAAK,iBAAiB,IAAI;IAC5B;EACF;AACF;AAQO,IAAM,aAAN,cAAyB,MAAM;EAKpC,YAAY,MAAc,OAAuB,CAAC,GAAG;AACnD,UAAM,MAAM,IAAI;AAChB,SAAK,OAAO,KAAK,SAAS,SAAY,IAAI,KAAK;AAC/C,SAAK,SAAS,KAAK,WAAW,SAAY,KAAK,KAAK;AACpD,SAAK,WAAW,KAAK,aAAa,SAAY,QAAQ,KAAK;EAC7D;AACF;AAEO,IAAM,uBAAN,cAAmC,WAAW;EAInD,YAAY,MAAc,OAAuB,CAAC,GAAG;AACnD,UAAM,MAAM,IAAI;AAChB,SAAK,WAAW,IAAI,CAAC,CAAC,KAAK;AAC3B,SAAK,iBAAiB,IAAI;EAC5B;EAEA,IAAI,aAAa;AACf,WAAO,KAAK,WAAW;EACzB;EAEA,IAAI,WAAW,gBAAgB;AAC7B,SAAK,WAAW,IAAI;EACtB;EAEA,IAAI,mBAAmB;AACrB,WAAO,KAAK,iBAAiB;EAC/B;EAEA,IAAI,iBAAiB,sBAAsB;AACzC,SAAK,iBAAiB,IAAI;EAC5B;EAEO,iBAAuB;AAC5B,QAAI,KAAK,cAAc,CAAC,KAAK,iBAAiB,GAAG;AAC/C,WAAK,iBAAiB,IAAI;IAC5B;EACF;AACF;ACvFA,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,iBAAiB,OAAO,gBAAgB;AAmCvC,IAAM,4BAAN,MAEP;EAME,YACkB,QACC,WACjB;AAFgB,SAAA,SAAA;AACC,SAAA,YAAA;AAEjB,SAAK,KAAK,gBAAgB;AAC1B,SAAK,MAAM,IAAI,IAAI,OAAO,GAAG;AAC7B,SAAK,QAAQ,IAAI,IAAI,YAAY;AAIjC,SAAK,UAAU,iBAAiB,YAAY,CAAC,UAAU;AACrD,YAAM,UAAU;QACd,KAAK;QACL,IAAI,uBAAuB,WAAW;UACpC,MAAM,MAAM;UACZ,QAAQ,MAAM;UACd,YAAY;QACd,CAAC;MACH;AAEA,WAAK,QAAQ,EAAE,cAAc,OAAO;AAMpC,UAAI,QAAQ,kBAAkB;AAC5B,cAAM,eAAe;MACvB;IACF,CAAC;AAUD,SAAK,UAAU,iBAAiB,SAAS,CAAC,UAAU;AAClD,WAAK,QAAQ,EAAE;QACb,UAAU,KAAK,QAAQ,IAAI,WAAW,SAAS,KAAK,CAAC;MACvD;IACF,CAAC;EACH;;;;EAKO,iBACL,MACA,UACA,SACM;AACN,QAAI,CAAC,QAAQ,IAAI,UAAU,cAAc,GAAG;AAC1C,YAAM,gBAAgB,SAAS,KAAK,KAAK,MAAM;AAI/C,aAAO,eAAe,UAAU,gBAAgB;QAC9C,OAAO;QACP,YAAY;QACZ,cAAc;MAChB,CAAC;IACH;AAEA,SAAK,QAAQ,EAAE;MACb;MACA,QAAQ,IAAI,UAAU,cAAc;MACpC;IACF;EACF;;;;EAKO,oBACL,OACA,UACA,SACM;AACN,SAAK,QAAQ,EAAE;MACb;MACA,QAAQ,IAAI,UAAU,cAAc;MACpC;IACF;EACF;;;;EAKO,KAAK,MAA2B;AACrC,SAAK,UAAU,KAAK,IAAI;EAC1B;;;;;;EAOO,MAAM,MAAe,QAAuB;AACjD,SAAK,UAAU,MAAM,MAAM,MAAM;EACnC;AACF;AE/IA,IAAM,mCACJ;AAEK,IAAM,sBAAsB,OAAO,qBAAqB;AACxD,IAAM,UAAU,OAAO,SAAS;AAChC,IAAM,SAAS,OAAO,QAAQ;AAE9B,IAAM,oBAAN,cAAgC,YAAiC;EA2BtE,YAAY,KAAmB,WAAoC;AACjE,UAAM;AAvBR,SAAS,aAAa;AACtB,SAAS,OAAO;AAChB,SAAS,UAAU;AACnB,SAAS,SAAS;AASlB,SAAQ,UAAyC;AACjD,SAAQ,aAEG;AACX,SAAQ,WAA0C;AAClD,SAAQ,WAAsD;AAO5D,SAAK,MAAM,IAAI,SAAS;AACxB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa,KAAK;AACvB,SAAK,iBAAiB;AAEtB,SAAK,mBAAmB,IAAI,IAAI,gBAAyB;AAEzD,mBAAe,YAAY;AACzB,UAAI,MAAM,KAAK,mBAAmB,GAAG;AACnC;MACF;AAEA,WAAK,WACH,OAAO,cAAc,WACjB,YACA,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,IAC/C,UAAU,CAAC,IACX;AAON,UAAI,KAAK,eAAe,KAAK,YAAY;AACvC,aAAK,aAAa,KAAK;AACvB,aAAK,cAAc,UAAU,MAAM,IAAI,MAAM,MAAM,CAAC,CAAC;MACvD;IACF,CAAC;EACH;EAEA,IAAI,OAAO,UAAyC;AAClD,SAAK,oBAAoB,QAAQ,KAAK,OAAO;AAC7C,SAAK,UAAU;AACf,QAAI,aAAa,MAAM;AACrB,WAAK,iBAAiB,QAAQ,QAAQ;IACxC;EACF;EACA,IAAI,SAAwC;AAC1C,WAAO,KAAK;EACd;EAEA,IAAI,UACF,UACA;AACA,SAAK;MACH;MACA,KAAK;IACP;AACA,SAAK,aAAa;AAClB,QAAI,aAAa,MAAM;AACrB,WAAK,iBAAiB,WAAW,QAAQ;IAC3C;EACF;EACA,IAAI,YAAwE;AAC1E,WAAO,KAAK;EACd;EAEA,IAAI,QAAQ,UAAyC;AACnD,SAAK,oBAAoB,SAAS,KAAK,QAAQ;AAC/C,SAAK,WAAW;AAChB,QAAI,aAAa,MAAM;AACrB,WAAK,iBAAiB,SAAS,QAAQ;IACzC;EACF;EACA,IAAI,UAAyC;AAC3C,WAAO,KAAK;EACd;EAEA,IAAI,QAAQ,UAAqD;AAC/D,SAAK,oBAAoB,SAAS,KAAK,QAAkC;AACzE,SAAK,WAAW;AAChB,QAAI,aAAa,MAAM;AACrB,WAAK,iBAAiB,SAAS,QAAQ;IACzC;EACF;EACA,IAAI,UAAqD;AACvD,WAAO,KAAK;EACd;;;;EAKO,KAAK,MAA2B;AACrC,QAAI,KAAK,eAAe,KAAK,YAAY;AACvC,WAAK,MAAM;AACX,YAAM,IAAI,aAAa,mBAAmB;IAC5C;AAIA,QAAI,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,KAAK,QAAQ;AACvE;IACF;AAIA,SAAK,kBAAkB,YAAY,IAAI;AAEvC,mBAAe,MAAM;AAnJzB,UAAAA;AAsJM,WAAK,iBAAiB;AAOtB,OAAAA,MAAA,KAAK,OAAA,MAAL,OAAA,SAAAA,IAAA,KAAA,MAAgB,IAAA;IAClB,CAAC;EACH;EAEO,MAAM,OAAe,KAAM,QAAuB;AACvD,cAAU,MAAM,gCAAgC;AAChD;MACE,SAAS,OAAS,QAAQ,OAAQ,QAAQ;MAC1C;IACF;AAEA,SAAK,MAAM,EAAE,MAAM,MAAM;EAC3B;EAEA,EAlIS,qBACA,SAiIA,OAAM,EACb,OAAe,KACf,QACA,WAAW,MACL;AAMN,QAAI,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,KAAK,QAAQ;AACvE;IACF;AAEA,SAAK,aAAa,KAAK;AAEvB,mBAAe,MAAM;AACnB,WAAK,aAAa,KAAK;AAEvB,WAAK;QACH;UACE;UACA,IAAI,WAAW,SAAS;YACtB;YACA;YACA;UACF,CAAC;QACH;MACF;AAGA,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,WAAW;IAClB,CAAC;EACH;EAYO,iBACL,MACA,UACA,SACM;AACN,WAAO,MAAM;MACX;MACA;MACA;IACF;EACF;EAEA,oBACE,MACA,UACA,SACM;AACN,WAAO,MAAM,oBAAoB,MAAM,UAAU,OAAO;EAC1D;AACF;AA7Na,kBACK,aAAa;AADlB,kBAEK,OAAO;AAFZ,kBAGK,UAAU;AAHf,kBAIK,SAAS;AA2N3B,SAAS,YAAY,MAA6B;AAChD,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK;EACd;AAEA,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK;EACd;AAEA,SAAO,KAAK;AACd;AD3OA,IAAMC,YAAW,OAAO,UAAU;AAClC,IAAMC,kBAAiB,OAAO,gBAAgB;AAC9C,IAAM,QAAQ,OAAO,OAAO;AAoCrB,IAAM,4BAAN,MAEP;EASE,YACmB,QACA,WACA,kBACjB;AAHiB,SAAA,SAAA;AACA,SAAA,YAAA;AACA,SAAA,mBAAA;AAEjB,SAAKC,SAAQ,IAAI,IAAI,YAAY;AACjC,SAAK,sBAAsB,IAAI,gBAAgB;AAC/C,SAAK,sBAAsB,IAAI,gBAAgB;AAM/C,SAAK,UAAU,iBAAiB,YAAY,CAAC,UAAU;AAGrD,UAAI,OAAO,KAAK,kBAAkB,aAAa;AAC7C;MACF;AAMA,qBAAe,MAAM;AACnB,YAAI,CAAC,MAAM,kBAAkB;AAM3B,eAAK,KAAK,EAAE,MAAM,IAAI;QACxB;MACF,CAAC;IACH,CAAC;AAED,SAAK,UAAU;MACb;MACA,KAAK,sBAAsB,KAAK,IAAI;IACtC;EACF;;;;;EAMA,IAAW,SAAoB;AAC7BC;MACE,KAAK;MACL;IACF;AAEA,WAAO,KAAK;EACd;;;;EAKO,UAAgB;AACrBA;MACE,CAAC,KAAK,iBAAiB,KAAK,cAAc,eAAe,UAAU;MACnE;IACF;AAEA,UAAM,gBAAgB,KAAK,iBAAiB;AAG5C,kBAAc,aAAa,KAAK,OAAO;AAKvC,kBAAc;MACZ;MACA,CAAC,UAAU;AACT,aAAKD,SAAQ,EAAE;UACb,UAAU,KAAK,eAAgB,IAAI,MAAM,QAAQ,KAAK,CAAC;QACzD;MACF;MACA,EAAE,MAAM,KAAK;IACf;AAEA,kBAAc,iBAAiB,WAAW,CAAC,UAAU;AAKnD,WAAK,UAAU;QACb;UACE,KAAK;UACL,IAAI,aAAa,YAAY;YAC3B,MAAM,MAAM;YACZ,QAAQ,MAAM;UAChB,CAAC;QACH;MACF;IACF,CAAC;AAID,SAAK,OAAO;MACV;MACA,CAAC,UAAU;AACT,aAAK,gBAAgB,KAAK;MAC5B;MACA;QACE,QAAQ,KAAK,oBAAoB;MACnC;IACF;AAIA,kBAAc;MACZ;MACA,CAAC,UAAU;AACT,aAAK,gBAAgB,KAAK;MAC5B;MACA;QACE,QAAQ,KAAK,oBAAoB;MACnC;IACF;AAEA,kBAAc,iBAAiB,SAAS,MAAM;AAC5C,YAAM,aAAa;QACjB;QACA,IAAI,MAAM,SAAS,EAAE,YAAY,KAAK,CAAC;MACzC;AAIA,WAAKA,SAAQ,EAAE,cAAc,UAAU;AAIvC,UAAI,CAAC,WAAW,kBAAkB;AAChC,aAAK,OAAO,cAAc,UAAU,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,CAAC;MACtE;IACF,CAAC;AAED,SAAK,gBAAgB;EACvB;;;;EAKO,iBACL,OACA,UACA,SACM;AACN,QAAI,CAAC,QAAQ,IAAI,UAAUE,eAAc,GAAG;AAC1C,YAAM,gBAAgB,SAAS,KAAK,KAAK,MAAM;AAI/C,aAAO,eAAe,UAAUA,iBAAgB;QAC9C,OAAO;QACP,YAAY;MACd,CAAC;IACH;AAEA,SAAKF,SAAQ,EAAE;MACb;MACA,QAAQ,IAAI,UAAUE,eAAc;MACpC;IACF;EACF;;;;EAKO,oBACL,OACA,UACA,SACM;AACN,SAAKF,SAAQ,EAAE;MACb;MACA,QAAQ,IAAI,UAAUE,eAAc;MACpC;IACF;EACF;;;;;;;;EASO,KAAK,MAA2B;AACrC,SAAK,KAAK,EAAE,IAAI;EAClB;EAEA,EApMSF,WAoMA,MAAK,EAAE,MAA2B;AACzC,UAAM,EAAE,cAAc,IAAI;AAE1BC;MACE;MACA;MACA,KAAK,OAAO;IACd;AAGA,QACE,cAAc,eAAe,UAAU,WACvC,cAAc,eAAe,UAAU,QACvC;AACA;IACF;AAKA,QAAI,cAAc,eAAe,UAAU,YAAY;AACrD,oBAAc;QACZ;QACA,MAAM;AACJ,wBAAc,KAAK,IAAI;QACzB;QACA,EAAE,MAAM,KAAK;MACf;AACA;IACF;AAGA,kBAAc,KAAK,IAAI;EACzB;;;;EAKO,QAAc;AACnB,UAAM,EAAE,cAAc,IAAI;AAE1BA;MACE;MACA;MACA,KAAK,OAAO;IACd;AAMA,SAAK,oBAAoB,MAAM;AAE/B,QACE,cAAc,eAAe,UAAU,WACvC,cAAc,eAAe,UAAU,QACvC;AACA;IACF;AAGA,kBAAc,MAAM;AAGpB,mBAAe,MAAM;AACnB,WAAKD,SAAQ,EAAE;QACb;UACE,KAAK;UACL,IAAI,qBAAqB,SAAS;;;;;YAKhC,MAAM;YACN,YAAY;UACd,CAAC;QACH;MACF;IACF,CAAC;EACH;EAEQ,sBAAsB,OAA0C;AAKtE,UAAM,eAAe;MACnB,MAAM;MACN,IAAI,uBAAuB,WAAW;QACpC,MAAM,MAAM;QACZ,QAAQ,MAAM;QACd,YAAY;MACd,CAAC;IACH;AASA,SAAKA,SAAQ,EAAE,cAAc,YAAY;AAMzC,QAAI,CAAC,aAAa,kBAAkB;AAClC,WAAK,OAAO;QACV;;;;;;UAME,KAAK;;;UAGL,IAAI,aAAa,WAAW;YAC1B,MAAM,MAAM;YACZ,QAAQ,MAAM;UAChB,CAAC;QACH;MACF;IACF;EACF;EAEQ,gBAAgB,QAAqB;AAE3C,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,MAAM;IAC3B;EACF;EAEQ,gBAAgB,OAAyB;AAI/C,SAAK,oBAAoB,MAAM;AAE/B,UAAM,aAAa;MACjB,KAAK;MACL,IAAI,qBAAqB,SAAS;QAChC,MAAM,MAAM;QACZ,QAAQ,MAAM;QACd,UAAU,MAAM;QAChB,YAAY;MACd,CAAC;IACH;AAEA,SAAKA,SAAQ,EAAE,cAAc,UAAU;AAIvC,QAAI,CAAC,WAAW,kBAAkB;AAKhC,WAAK,OAAO,MAAM,EAAE,MAAM,MAAM,MAAM,MAAM;IAC9C;EACF;AACF;AEvZO,IAAM,0BAAN,cACG,YAEV;EACE,YAA+B,QAA2B;AACxD,UAAM;AADuB,SAAA,SAAA;AAM7B,SAAK,OAAO,iBAAiB,SAAS,CAAC,UAAU;AAC/C,WAAK,cAAc,UAAU,KAAK,QAAQ,IAAI,WAAW,SAAS,KAAK,CAAC,CAAC;IAC3E,CAAC;AAMD,SAAK,OAAO,OAAO,IAAI,CAAC,SAAS;AAC/B,WAAK;QACH;UACE,KAAK;;;UAGL,IAAI,uBAAuB,YAAY;YACrC;YACA,QAAQ,KAAK,OAAO;YACpB,YAAY;UACd,CAAC;QACH;MACF;IACF;EACF;EAEO,iBACL,MACA,UAGA,SACM;AACN,WAAO,MAAM,iBAAiB,MAAM,UAA2B,OAAO;EACxE;EAEO,cACL,OACS;AACT,WAAO,MAAM,cAAc,KAAK;EAClC;EAEO,KAAK,MAA2B;AACrC,mBAAe,MAAM;AACnB,UACE,KAAK,OAAO,eAAe,KAAK,OAAO,WACvC,KAAK,OAAO,eAAe,KAAK,OAAO,QACvC;AACA;MACF;AAEA,YAAM,gBAAgB,MAAM;AAC1B,aAAK,OAAO;UACV;;;;;;;;;YASE,KAAK;YACL,IAAI,aAAa,WAAW;cAC1B;cACA,QAAQ,KAAK,OAAO;YACtB,CAAC;UACH;QACF;MACF;AAEA,UAAI,KAAK,OAAO,eAAe,KAAK,OAAO,YAAY;AACrD,aAAK,OAAO;UACV;UACA,MAAM;AACJ,0BAAc;UAChB;UACA,EAAE,MAAM,KAAK;QACf;MACF,OAAO;AACL,sBAAc;MAChB;IACF,CAAC;EACH;EAEO,MAAM,MAAc,QAAuB;AAMhD,SAAK,OAAO,MAAM,EAAE,MAAM,MAAM;EAClC;AACF;ACjDO,IAAM,wBAAN,cAAmC,YAA+B;EAGvE,cAAc;AACZ,UAAM,sBAAqB,MAAM;EACnC;EAEU,mBAA4B;AACpC,WAAO,sBAAsB,WAAW;EAC1C;EAEU,QAAc;AACtB,UAAM,8BAA8B,OAAO;MACzC;MACA;IACF;AAEA,UAAM,iBAAiB,IAAI,MAAM,WAAW,WAAW;MACrD,WAAW,CACT,QACA,MACA,cACG;AACH,cAAM,CAAC,KAAK,SAAS,IAAI;AAEzB,cAAM,mBAAmB,MAAiB;AACxC,iBAAO,QAAQ,UAAU,QAAQ,MAAM,SAAS;QAClD;AAKA,cAAM,SAAS,IAAI,kBAAkB,KAAK,SAAS;AACnD,cAAM,YAAY,IAAI,wBAAwB,MAAM;AAKpD,uBAAe,MAAM;AACnB,cAAI;AACF,kBAAM,SAAS,IAAI;cACjB;cACA;cACA;YACF;AAKA,kBAAM,yBAAyB,KAAK,QAAQ,KAAK,cAAc;cAC7D,QAAQ,IAAI,0BAA0B,QAAQ,SAAS;cACvD;cACA,MAAM;gBACJ;cACF;YACF,CAAC;AAED,gBAAI,wBAAwB;AAC1B,qBAAO,mBAAmB,EAAE,QAAQ,KAAK;YAC3C,OAAO;AACL,qBAAO,mBAAmB,EAAE,QAAQ,IAAI;AAExC,qBAAO,QAAQ;AAIf,qBAAO,iBAAiB,QAAQ,MAAM;AACpC,uBAAO,cAAc,UAAU,QAAQ,IAAI,MAAM,MAAM,CAAC,CAAC;AAIzD,oBAAI,OAAO,eAAe,GAAG;AAC3B,yBAAO,WAAW,OAAO,eAAe,EAAE;gBAC5C;cACF,CAAC;YACH;UACF,SAASG,QAAP;AAOA,gBAAIA,kBAAiB,OAAO;AAC1B,qBAAO,cAAc,IAAI,MAAM,OAAO,CAAC;AAIvC,kBACE,OAAO,eAAe,UAAU,WAChC,OAAO,eAAe,UAAU,QAChC;AACA,uBAAO,MAAM,EAAE,MAAMA,OAAM,SAAS,KAAK;cAC3C;AAEA,sBAAQ,MAAMA,MAAK;YACrB;UACF;QACF,CAAC;AAED,eAAO;MACT;IACF,CAAC;AAED,WAAO,eAAe,YAAY,aAAa;MAC7C,OAAO;MACP,cAAc;IAChB,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO;QACL;QACA;QACA;MACF;IACF,CAAC;EACH;AACF;AArHO,IAAM,uBAAN;AAAM,qBACJ,SAAS,OAAO,WAAW;;;ACjE7B,IAAM,uBAAuB,IAAI,qBAAqB;;;ACetD,SAAS,qBAAqB,SAAsC;AACzE,uBAAqB,GAAG,cAAc,OAAO,eAAe;AAC1D,UAAM,WAAW,QAAQ,YAAY,EAAE,OAAO,cAAc,cAAc,CAAC;AAG3E,QAAI,SAAS,SAAS,GAAG;AACvB,yCAAS,mBAAmB;AAE5B,YAAM,QAAQ;QACZ,SAAS,IAAI,CAAC,YAAY;AAIxB,iBAAO,QAAQ,IAAI,UAAU;QAC/B,CAAC;MACH;AAEA;IACF;AAGA,UAAM,UAAU,IAAI,QAAQ,WAAW,OAAO,KAAK;MACjD,SAAS;QACP,SAAS;QACT,YAAY;MACd;IACF,CAAC;AACD,UAAM;MACJ;MACA,QAAQ,4BAA4B;IACtC,EAAE,MAAM,CAACC,WAAU;AACjB,YAAM,aAAa,IAAI,MAAM,OAAO;AACpC,aAAO,eAAe,YAAY,SAAS;QACzC,YAAY;QACZ,cAAc;QACd,OAAOA;MACT,CAAC;AACD,iBAAW,OAAO,OAAO,cAAc,UAAU;IACnD,CAAC;AAED,uCAAS,wBAAwB;AAIjC,eAAW,OAAO,QAAQ;EAC5B,CAAC;AACH;;;ACvDO,SAAS,iBAAiB,MAA6B;AAC5D,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK;EACd;AAEA,MAAI,gBAAgB,aAAa;AAC/B,WAAO,KAAK;EACd;AAEA,SAAO,IAAI,KAAK,CAAC,IAAW,CAAC,EAAE;AACjC;;;AClBA,IAAM,aAAa;AAEZ,SAAS,gBAAgB,SAAyB;AACvD,MAAI,QAAQ,UAAU,YAAY;AAChC,WAAO;EACT;AAEA,SAAO,GAAG,QAAQ,MAAM,GAAG,UAAU,CAAC;AACxC;;;ACLA,eAAsB,cAAc,MAAsC;AACxE,MAAI,gBAAgB,MAAM;AACxB,UAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,WAAO,QAAQ,gBAAgB,IAAI,CAAC;EACtC;AAGA,MAAI,OAAO,SAAS,YAAY,gBAAgB,MAAM;AACpD,UAAM,OAAO,IAAI,YAAY,EAAE,OAAO,IAAmB;AACzD,WAAO,eAAe,gBAAgB,IAAI,CAAC;EAC7C;AAEA,SAAO,gBAAgB,IAAI;AAC7B;;;ACLA,IAAM,SAAS;EACb,QAAQ;EACR,UAAU;EACV,UAAU;EACV,QAAQ;AACV;AAEO,SAAS,sBACd,YACM;AACN,QAAM,EAAE,QAAQ,OAAO,IAAI;AAE3B,oBAAkB,MAAM;AASxB,SAAO,iBAAiB,WAAW,CAAC,UAAU;AAC5C,6BAAyB,KAAK;EAChC,CAAC;AAED,SAAO,iBAAiB,SAAS,CAAC,UAAU;AAC1C,uBAAmB,KAAK;EAC1B,CAAC;AAGD,SAAO,OAAO,iBAAiB,SAAS,CAAC,UAAU;AACjD,mBAAe,KAAK;EACtB,CAAC;AAED,SAAO,OAAO,IAAI,MAAM,OAAO,MAAM;IACnC,MAAM,QAAQ,SAAS,MAAM;AAC3B,YAAM,CAAC,IAAI,IAAI;AACf,YAAM,eAAe,IAAI,aAAa,WAAW,EAAE,KAAK,CAAC;AACzD,aAAO,iBAAiB,cAAc;QACpC,eAAe;UACb,YAAY;UACZ,UAAU;UACV,OAAO,OAAO;QAChB;QACA,QAAQ;UACN,YAAY;UACZ,UAAU;UACV,OAAO,OAAO;QAChB;MACF,CAAC;AAED,qBAAe,MAAM;AACnB,uCAA+B,YAAY;MAC7C,CAAC;AAED,aAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;IAC5C;EACF,CAAC;AAED,SAAO;IACL;IACA,MAAM;AACJ,aAAO,iBAAiB,WAAW,CAAC,UAAU;AAC5C,iCAAyB,KAAK;MAChC,CAAC;IACH;IACA,EAAE,MAAM,KAAK;EACf;AAIA,SAAO,OAAO,IAAI,MAAM,OAAO,MAAM;IACnC,MAAM,QAAQ,SAAS,MAAM;AAC3B,YAAM,CAAC,IAAI,IAAI;AACf,YAAM,eAAe,IAAI,aAAa,WAAW,EAAE,KAAK,CAAC;AACzD,aAAO,iBAAiB,cAAc;QACpC,eAAe;UACb,YAAY;UACZ,UAAU;UACV,OAAO,OAAO;QAChB;QACA,QAAQ;UACN,YAAY;UACZ,UAAU;UACV,OAAO,OAAO;QAChB;MACF,CAAC;AAED,qCAA+B,YAAY;AAE3C,aAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;IAC5C;EACF,CAAC;AACH;AAQO,SAAS,kBAAkB,QAAmC;AACnE,QAAM,YAAY,YAAY,OAAO,GAAG;AAGxC,UAAQ;IACN,SAAS,cAAc,GAAG,aAAa,CAAC,UAAU,SAAS,EAAE;IAC7D,SAAS,OAAO,MAAM;IACtB;EACF;AAEA,UAAQ,IAAI,WAAW,OAAO,MAAM;AAEpC,UAAQ,SAAS;AACnB;AAEA,SAAS,mBAAmB,OAAmB;AAC7C,QAAM,SAAS,MAAM;AACrB,QAAM,YAAY,YAAY,OAAO,GAAG;AAGxC,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,UAAU,SAAS;IAC5D;IACA,SAAS,OAAO,MAAM;IACtB;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;AAEA,SAAS,eAAe,OAAc;AACpC,QAAM,SAAS,MAAM;AACrB,QAAM,YAAY,YAAY,OAAO,GAAG;AAGxC,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,UAAe,SAAS;IACjE;IACA,SAAS,OAAO,MAAM;IACtB;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;AAKA,eAAe,yBAAyB,OAAoC;AAC1E,QAAM,aAAa,iBAAiB,MAAM,IAAI;AAC9C,QAAM,aAAa,MAAM,cAAc,MAAM,IAAI;AACjD,QAAM,QAAQ,MAAM,mBAAmB,MAAM;AAG7C,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,UAAU,MAAM,UAAU;IACpF;IACA,SAAS,OAAO,QAAQ;IACxB;IACA;IACA;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;AAMA,eAAe,+BACb,OACA;AACA,QAAM,aAAa,iBAAiB,MAAM,IAAI;AAC9C,QAAM,aAAa,MAAM,cAAc,MAAM,IAAI;AAGjD,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,UAAU,UAAU,MAAM,UAAU;IAC7E;IACA,SAAS,OAAO,MAAM;IACtB;IACA;IACA;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;AAMA,eAAe,+BACb,OACA;AACA,QAAM,aAAa,iBAAiB,MAAM,IAAI;AAC9C,QAAM,aAAa,MAAM,cAAc,MAAM,IAAI;AAGjD,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,UAAU,UAAU,MAAM,UAAU;IAC7E;IACA,SAAS,OAAO,MAAM;IACtB;IACA;IACA;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;AAEA,eAAe,yBAAyB,OAAoC;AAC1E,QAAM,aAAa,iBAAiB,MAAM,IAAI;AAC9C,QAAM,aAAa,MAAM,cAAc,MAAM,IAAI;AACjD,QAAM,QAAQ,MAAM,mBAAmB,MAAM;AAG7C,UAAQ;IACN,SAAS;MACP,GAAG,aAAa,EAAE,cAAc,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,UAAU,MAAM,UAAU;IACpF;IACA,SAAS,OAAO,QAAQ;IACxB;IACA;IACA;EACF;AAEA,UAAQ,IAAI,KAAK;AAEjB,UAAQ,SAAS;AACnB;;;AClQA,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,YAAiB,MAAmB;AAC/D,UAAQ,MAAM;IAEZ,KAAK;AACH,aAAO;IAGT,KAAK;IACL,KAAK;AACH,aAAO,OAAO,UAAU;IAG1B,KAAK;AACH,aAAO,KAAK,UAAU,UAAU;IAGlC,KAAK,KAAK;AAER,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO;MACT;AAEA,YAAM,OAAO,KAAK,UAAU,UAAU;AAGtC,UAAI,SAAS,QAAQ,SAAS,QAAQ,mBAAmB,KAAK,IAAI,GAAG;AACnE,eAAO;MACT;AAEA,aAAO;IACT;EACF;AACF;AAEO,SAAS,OAAO,YAAoB,aAA4B;AACrE,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;EACT;AAEA,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,QAAQ;IAC7B;IACA,CAAC,OAAO,WAAW,GAAG,SAAS;AAC7B,YAAM,aAAa,YAAY,eAAA;AAC/B,YAAM,QAAQ,oBAAoB,YAAY,IAAI;AAElD,UAAI,CAAC,WAAW;AACd;AACA,eAAO;MACT;AAEA,aAAO;IACT;EACF;AAGA,MAAI,kBAAkB,YAAY,QAAQ;AACxC,wBAAoB,IAAI,YAAY,MAAM,eAAe,EAAE,KAAK,GAAG,CAAA;EACrE;AAEA,qBAAmB,iBAAiB,QAAQ,WAAW,GAAG;AAE1D,SAAO;AACT;AC/DA,IAAM,yBAAyB;AAO/B,SAAS,gBAAgBC,QAAoB;AAC3C,MAAI,CAACA,OAAM,OAAO;AAChB;EACF;AAEA,QAAM,YAAYA,OAAM,MAAM,MAAM,IAAI;AACxC,YAAU,OAAO,GAAG,sBAAsB;AAC1CA,SAAM,QAAQ,UAAU,KAAK,IAAI;AACnC;AAEO,IAAM,iBAAN,cAA6B,MAAM;EAGxC,YAA4B,YAAoB,aAAoB;AAClE,UAAM,OAAO;AADa,SAAA,UAAA;AAF5B,SAAA,OAAO;AAIL,SAAK,UAAU,OAAO,SAAS,GAAG,WAAW;AAC7C,oBAAgB,IAAI;EACtB;AACF;AA2BO,IAAMC,aAAuB,CAClC,WACA,YACG,gBACmB;AACtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAe,SAAS,GAAG,WAAW;EAClD;AACF;AAEAA,WAAU,KAAK,CAAC,kBAAkB,WAAW,YAAY,gBAAgB;AACvE,MAAI,CAAC,WAAW;AACd,UAAM,gBACJ,YAAY,WAAW,IAAI,UAAU,OAAO,SAAS,GAAG,WAAW;AACrE,QAAID;AAEJ,QAAI;AACFA,eAAQ,QAAQ,UAAU,kBAA4C;QACpE;MACF,CAAC;IACH,SAAS,KAAP;AACAA,eAAS,iBAAwC,aAAa;IAChE;AAEA,UAAMA;EACR;AACF;AC7EO,SAAS,gBAAyB;AACvC,MAAI,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAC3E,WAAO;EACT;AAEA,MAAI,OAAO,YAAY,aAAa;AAElC,UAAM,OAAQ,QAAgB;AAC9B,QAAI,SAAS,cAAc,SAAS,UAAU;AAC5C,aAAO;IACT;AAGA,WAAO,CAAC,EACN,QAAQ,YACR,QAAQ,SAAS;EAErB;AAEA,SAAO;AACT;AERO,IAAM,QAAQ,OAInB,YAC6C;AAC7C,MAAI;AACF,UAAM,OAAO,MAAM,QAAQ,EAAE,MAAM,CAACA,WAAU;AAC5C,YAAMA;IACR,CAAC;AACD,WAAO,EAAE,OAAO,MAAM,KAAK;EAC7B,SAASA,QAAP;AACA,WAAO,EAAE,OAAAA,QAAO,MAAM,KAAK;EAC7B;AACF;AEzBO,SAAS,qBAAqB,WAA2B;AAC9D,SAAO,IAAI,IAAI,WAAW,SAAS,IAAI,EAAE;AAC3C;ACAO,SAAS,wBACd,cACA,mBACA,YACsB;AACtB,QAAM,YAAY;IAChB,aAAa;IACb,aAAa;IACb,aAAa;EACf;AACA,QAAM,iBAAiB,UAAU,OAAO,CAAC,UAAkC;AACzE,WAAO,SAAS;EAClB,CAAC;AACD,QAAM,SAAS,eAAe,KAAK,CAACE,YAAW;AAC7C,WAAO,WAAWA,QAAO,WAAW,iBAAiB;EACvD,CAAC;AAED,SAAO,UAAU;AACnB;AFdO,IAAM,oBAAoB,OAC/B,KACA,UAA+B,CAAC,GAChC,eACwC;AAExC,QAAM,oBAAoB,qBAAqB,GAAG;AAElD,QAAM,oBAAoB,MAAM,UAAU,cACvC,iBAAiB,EACjB;IAAK,CAAC,kBACL,cAAc;MAAO,CAAC,iBACpB,wBAAwB,cAAc,mBAAmB,UAAU;IACrE;EACF;AACF,MAAI,CAAC,UAAU,cAAc,cAAc,kBAAkB,SAAS,GAAG;AAOvE,aAAS,OAAO;EAClB;AAEA,QAAM,CAAC,oBAAoB,IAAI;AAE/B,MAAI,sBAAsB;AAGxB,yBAAqB,OAAO;AAG5B,WAAO;MACL;QACE;QACA;QACA;MACF;MACA;IACF;EACF;AAGA,QAAM,qBAAqB,MAAM;IAC/B,YAAY;AACV,YAAM,eAAe,MAAM,UAAU,cAAc,SAAS,KAAK,OAAO;AACxE,aAAO;;;QAGL,wBAAwB,cAAc,mBAAmB,UAAU;QACnE;MACF;IACF;EACF;AAGA,MAAI,mBAAmB,OAAO;AAC5B,UAAM,kBAAkB,mBAAmB,MAAM,QAAQ,SAAS,OAAO;AAIzE,QAAI,iBAAiB;AACnB,YAAM,WAAW,IAAI,KAAI,mCAAS,UAAS,KAAK,SAAS,IAAI;AAE7D,YAAM,IAAI;QACR,SAAS,cAAc,mDACmB,SAAS,IAAI,oBAAoB,iBAAiB;;;;oFAIhB;MAC9E;IACF;AAGA,UAAM,IAAI;MACR,SAAS;QACP;QACA,mBAAmB,MAAM;MAC3B;IACF;EACF;AAEA,SAAO,mBAAmB;AAC5B;AIjFO,SAAS,kBAAkB,OAA8B,CAAC,GAAG;AAClE,MAAI,KAAK,OAAO;AACd;EACF;AAEA,QAAM,UAAU,KAAK,WAAW;AAGhC,UAAQ;IACN,KAAKC,SAAS,cAAc,OAAO,CAAC;IACpC;EACF;AAEA,UAAQ;IACN;IACA;IACA;EACF;AAEA,UAAQ,IAAI,qDAAqD;AAEjE,MAAI,KAAK,WAAW;AAElB,YAAQ,IAAI,sBAAsB,KAAK,SAAS;EAClD;AAEA,MAAI,KAAK,aAAa;AAEpB,YAAQ,IAAI,iBAAiB,KAAK,WAAW;EAC/C;AAEA,MAAI,KAAK,QAAQ;AAEf,YAAQ,IAAI,sBAAsB,KAAK,OAAO,IAAI,KAAK,OAAO,SAAS;EACzE;AAGA,UAAQ,SAAS;AACnB;AD7CA,eAAsB,cACpB,SACA,SACA;;AACA,UAAQ,cAAc,KAAK,eAAe;AAC1C,QAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,iBAAiB;AAK/D,MAAI,QAAQ,kBAAkB;AAC5BA,aAAS;MACP;IACF;AACA;EACF;AAEA,UAAQ,mBAAmB;AAE3B,oBAAkB;IAChB,OAAO,QAAQ;IACf,cAAaC,MAAA,QAAQ,iBAAR,gBAAAA,IAAsB;IACnC,YAAW,aAAQ,WAAR,mBAAgB;IAC3B,QAAQ,QAAQ;EAClB,CAAC;AACH;AEXO,IAAM,gBAAN,MAAoB;EACzB,YAA6B,MAAmB;AAAnB,SAAA,OAAA;EAAoB;EAE1C,YACL,UACG,MACG;AACN,UAAM,CAAC,MAAM,QAAQ,IAAI;AACzB,SAAK,KAAK,YAAY,EAAE,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,CAAC;EAC3D;AACF;ACxBO,SAAS,oBACd,SACsC;AAMtC,MAAI,CAAC,QAAQ,KAAK,EAAE,SAAS,QAAQ,MAAM,GAAG;AAC5C,WAAO;EACT;AAEA,SAAO,QAAQ;AACjB;ACbO,SAAS,mBACd,mBACS;AACT,SAAO,IAAI,QAAQ,kBAAkB,KAAK;IACxC,GAAG;IACH,MAAM,oBAAoB,iBAAiB;EAC7C,CAAC;AACH;ACGO,IAAM,wBAAwB,CACnC,SACA,YACG;AACH,SAAO,OACL,OACA,YAIG;AACH,UAAM,iBAAiB,IAAI,cAAc,MAAM,MAAM,CAAC,CAAC;AAEvD,UAAM,YAAY,QAAQ,QAAQ;AAClC,UAAM,UAAU,mBAAmB,QAAQ,OAAO;AAClD,UAAM,sBAAsB,QAAQ,MAAM;AAM1C,UAAM,eAAe,QAAQ,MAAM;AACnC,mBAAe,MAAM,IAAI,SAAS,YAAY;AAE9C,QAAI;AACF,YAAM;QACJ;QACA;QACA,QAAQ,mBAAmB,EAAE,OAAO,cAAc,gBAAgB,CAAC;QACnE;QACA,QAAQ;QACR;UACE,wBAAwB;AACtB,2BAAe,YAAY,aAAa;UAC1C;UACA,MAAM,iBAAiB,UAAU,EAAE,SAAS,aAAa,GAAG;AAI1D,kBAAM,gBAAgB,SAAS,MAAM;AACrC,kBAAM,uBAAuB,SAAS,MAAM;AAC5C,kBAAM,eAAe,eAAe,QAAQ;AAM5C,gBAAI,QAAQ,SAAS,wBAAwB;AAC3C,oBAAM,uBAAuB,SAAS;AAEtC,6BAAe;gBACb;gBACA;kBACE,GAAG;kBACH,MAAM;gBACR;gBACA,uBAAuB,CAAC,oBAAoB,IAAI;cAClD;YACF,OAAO;AAOL,oBAAM,uBACJ,SAAS,SAAS,OACd,OACA,MAAM,cAAc,YAAY;AAEtC,6BAAe,YAAY,iBAAiB;gBAC1C,GAAG;gBACH,MAAM;cACR,CAAC;YACH;AAEA,gBAAI,CAAC,QAAQ,OAAO;AAClB,sBAAQ,QAAQ,KAAK,mBAAmB,MAAM;AAC5C,wBAAQ,IAAI;kBACV,SAAS;kBACT,UAAU;kBACV;gBACF,CAAC;cACH,CAAC;YACH;UACF;QACF;MACF;IACF,SAASJ,QAAO;AACd,UAAIA,kBAAiB,OAAO;AAC1BG,iBAAS;UACP;;;;;UAKA,QAAQ;UACR,QAAQ;UACRH,OAAM,SAASA;QACjB;AAIA,uBAAe,YAAY,iBAAiB;UAC1C,QAAQ;UACR,YAAY;UACZ,SAAS;YACP,gBAAgB;UAClB;UACA,MAAM,KAAK,UAAU;YACnB,MAAMA,OAAM;YACZ,SAASA,OAAM;YACf,OAAOA,OAAM;UACf,CAAC;QACH,CAAC;MACH;IACF;EACF;AACF;AC/HA,eAAsB,qBACpB,SACe;AAEf,UAAQ,cAAc,KAAK,yBAAyB;AAEpD,QAAM,EAAE,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,0BAA0B;AAQxE,MAAI,QAAQ,aAAa,oCAAyB;AAChDG,aAAS;MACP,6FAA6F,QAAQ,cAAc;;;;;;;IAOrH;EACF;AACF;ACjCA,IAAM,UAAU,IAAI,YAAY;AAEzB,SAAS,aAAa,MAA0B;AACrD,SAAO,QAAQ,OAAO,IAAI;AAC5B;AAEO,SAAS,aAAa,QAAqB,UAA2B;AAC3E,QAAM,UAAU,IAAI,YAAY,QAAQ;AACxC,SAAO,QAAQ,OAAO,MAAM;AAC9B;AAOO,SAAS,cAAc,OAAgC;AAC5D,SAAO,MAAM,OAAO;IAClB,MAAM;IACN,MAAM,aAAa,MAAM;EAC3B;AACF;ACnBO,IAAM,oBAAmC,OAAO,iBAAiB;ACGjE,SAAS,YAAY,KAAsB;AAChD,MAAI;AACF,QAAI,IAAI,GAAG;AACX,WAAO;EACT,SAAS,QAAP;AACA,WAAO;EACT;AACF;ACTO,SAAS,iBACd,YACA,QACe;AACf,QAAM,aAAa,OAAO,sBAAsB,MAAM;AAEtD,QAAM,SAAS,WAAW,KAAK,CAACE,YAAW;AACzC,WAAOA,QAAO,gBAAgB;EAChC,CAAC;AAED,MAAI,QAAQ;AACV,WAAO,QAAQ,IAAI,QAAQ,MAAM;EACnC;AAEA;AACF;ACQO,IAAM,iBAAN,cAA4B,SAAS;EAS1C,OAAO,yBAAyB,QAAyB;AACvD,WAAO,UAAU,OAAO,UAAU;EACpC;EAEA,OAAO,mBAAmB,QAAyB;AACjD,WAAO,eAAc,2BAA2B,SAAS,MAAM;EACjE;;;;;EAMA,OAAO,mBAAmB,QAAyB;AACjD,WAAO,CAAC,eAAc,0BAA0B,SAAS,MAAM;EACjE;EAEA,OAAO,OAAO,KAAyB,UAA0B;AAC/D,QAAI,CAAC,OAAO,QAAQ,YAAY,CAAC,YAAY,GAAG,GAAG;AACjD;IACF;AAEA,UAAM,QAAQ,iBAA2C,SAAS,QAAQ;AAE1E,QAAI,OAAO;AAGT,YAAM,QAAQ,KAAK,IAAI,IAAI,GAAG,CAAC;IACjC,OAAO;AAEL,aAAO,eAAe,UAAU,OAAO;QACrC,OAAO;QACP,YAAY;QACZ,cAAc;QACd,UAAU;MACZ,CAAC;IACH;EACF;;;;EAKA,OAAO,gBAAgB,YAAoC;AACzD,UAAM,UAAU,IAAI,QAAQ;AAC5B,aAAS,OAAO,GAAG,OAAO,WAAW,QAAQ,QAAQ,GAAG;AACtD,cAAQ,OAAO,WAAW,IAAI,GAAG,WAAW,OAAO,CAAC,CAAC;IACvD;AACA,WAAO;EACT;EAEA,YAAY,MAAwB,OAA0B,CAAC,GAAG;AApFpE,QAAAD;AAqFI,UAAM,UAASA,MAAA,KAAK,WAAL,OAAAA,MAAe;AAC9B,UAAM,aAAa,eAAc,yBAAyB,MAAM,IAC5D,SACA;AACJ,UAAM,YAAY,eAAc,mBAAmB,MAAM,IAAI,OAAO;AAEpE,UAAM,WAAW;MACf,QAAQ;MACR,YAAY,KAAK;MACjB,SAAS,KAAK;IAChB,CAAC;AAED,QAAI,WAAW,YAAY;AAKzB,YAAM,QAAQ,iBAA2C,SAAS,IAAI;AAEtE,UAAI,OAAO;AACT,cAAM,SAAS;MACjB,OAAO;AACL,eAAO,eAAe,MAAM,UAAU;UACpC,OAAO;UACP,YAAY;UACZ,cAAc;UACd,UAAU;QACZ,CAAC;MACH;IACF;AAEA,mBAAc,OAAO,KAAK,KAAK,IAAI;EACrC;AACF;AA5FO,IAAM,gBAAN;AAAM,cAKK,4BAA4B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AALzD,cAOK,6BAA6B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;ACjCvE,IAAM,cAAc,OAAO,aAAa;AAkBjC,SAAS,cAAc,SAAkB,YAA2B;AACzE,UAAQ,IAAI,SAAS,aAAa,UAAU;AAC9C;ACpBA,IAAI,YAAY,OAAO;AACvB,IAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ;AACf,cAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAChE;AAOA,IAAI,iBAAiB,CAAC;AACtB,SAAS,gBAAgB;EACvB,MAAM,MAAM;EACZ,MAAM,MAAM;EACZ,OAAO,MAAM;EACb,KAAK,MAAM;EACX,QAAQ,MAAM;AAChB,CAAC;AACD,SAAS,OAAO,MAAM;AACpB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,KAAK,MAAM;AAClB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,KAAK,MAAM;AAClB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,IAAI,MAAM;AACjB,SAAO,WAAW,IAAI;AACxB;AACA,SAAS,MAAM,MAAM;AACnB,SAAO,WAAW,IAAI;AACxB;AAGA,IAAI,UAAU,cAAc;AAC5B,IAAI,SAAS,MAAM;EACjB,YAAY,MAAM;AAoBlB;AAnBE,SAAK,OAAO;AACZ,SAAK,SAAS,IAAI,KAAK,IAAI;AAC3B,UAAM,cAAc,YAAY,OAAO;AACvC,UAAM,eAAe,YAAY,WAAW;AAC5C,UAAM,mBAAmB,gBAAgB,OAAO,gBAAgB,UAAU,OAAO,gBAAgB,eAAe,KAAK,KAAK,WAAW,WAAW;AAChJ,QAAI,kBAAkB;AACpB,WAAK,QAAQ,sBAAsB,cAAc,OAAO,IAAI,OAAO,KAAK;AACxE,WAAK,OAAO,sBAAsB,cAAc,MAAM,IAAI,OAAO,KAAK;AACtE,WAAK,UAAU,sBAAsB,cAAc,SAAS,IAAI,OAAO,KAAK;AAC5E,WAAK,UAAU,sBAAsB,cAAc,SAAS,IAAI,OAAO,KAAK;AAC5E,WAAK,QAAQ,sBAAsB,cAAc,OAAO,IAAI,OAAO,KAAK;IAC1E,OAAO;AACL,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,OAAO;IACd;EACF;EAEA,OAAO,QAAQ;AACb,WAAO,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,MAAM,EAAE;EAC5C;;;;;;EAMA,MAAM,YAAY,aAAa;AAC7B,SAAK,SAAS;MACZ,OAAO;MACP,SAAS,KAAK,OAAO;MACrB;MACA,QAAQ,KAAK;MACb,QAAQ;QACN,QAAQ;MACV;IACF,CAAC;EACH;;;;;;EAMA,KAAK,YAAY,aAAa;AAC5B,SAAK,SAAS;MACZ,OAAO;MACP;MACA;MACA,QAAQ,KAAK;MACb,QAAQ;QACN,QAAQ;MACV;IACF,CAAC;AACD,UAAM,eAAe,IAAI,iBAAiB;AAC1C,WAAO,CAAC,aAAa,iBAAiB;AACpC,mBAAa,QAAQ;AACrB,WAAK,SAAS;QACZ,OAAO;QACP,SAAS,GAAG,QAAQ,IAAI,KAAK,GAAG,aAAa,SAAS,IAAI,CAAC;QAC3D,aAAa;QACb,QAAQ,KAAK;QACb,QAAQ;UACN,QAAQ;QACV;MACF,CAAC;IACH;EACF;;;;;;EAMA,QAAQ,YAAY,aAAa;AAC/B,SAAK,SAAS;MACZ,OAAO;MACP;MACA;MACA,QAAQ,KAAU,KAAK,MAAM;MAC7B,QAAQ;QACN,WAAW;QACX,QAAQ;MACV;IACF,CAAC;EACH;;;;;;EAMA,QAAQ,YAAY,aAAa;AAC/B,SAAK,SAAS;MACZ,OAAO;MACP;MACA;MACA,QAAQ,KAAU,KAAK,MAAM;MAC7B,QAAQ;QACN,WAAW;QACX,QAAQ;MACV;IACF,CAAC;EACH;;;;;;EAMA,MAAM,YAAY,aAAa;AAC7B,SAAK,SAAS;MACZ,OAAO;MACP;MACA;MACA,QAAQ,KAAU,KAAK,MAAM;MAC7B,QAAQ;QACN,WAAW;QACX,QAAQ;MACV;IACF,CAAC;EACH;;;;;;;;;;EAUA,KAAK,UAAU;AACb,aAAS;EACX;EACA,YAAY,OAAO,SAAS;AAC1B,WAAO;MACL,WAA2B,oBAAI,KAAK;MACpC;MACA;IACF;EACF;EACA,SAAS,MAAM;AACb,UAAM;MACJ;MACA;MACA;MACA,QAAQ;MACR,cAAc,CAAC;IACjB,IAAI;AACJ,UAAM,QAAQ,KAAK,YAAY,OAAO,OAAO;AAC7C,UAAM,kBAAiB,6CAAc,cAAa;AAClD,UAAM,eAAc,6CAAc,WAAU;AAC5C,UAAM,WAAW;MACf,WAAW,eAAe,cAAc;MACxC,QAAQ,eAAe,WAAW;IACpC;AACA,UAAM,QAAQ,KAAK,UAAU,KAAK;AAClC;MACE,CAAC,SAAS,UAAU,KAAK,gBAAgB,MAAM,SAAS,CAAC,CAAC,EAAE,OAAO,UAAU,OAAO,SAAS,OAAO,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,eAAe,OAAO,CAAC,EAAE,KAAK,GAAG;MAC1J,GAAG,YAAY,IAAI,cAAc;IACnC;EACF;EACA,gBAAgB,WAAW;AACzB,WAAO,GAAG,UAAU;MAClB;IACF,CAAC,IAAI,UAAU,gBAAgB,CAAC;EAClC;EACA,UAAU,OAAO;AACf,YAAQ,OAAO;MACb,KAAK;MACL,KAAK;MACL,KAAK,QAAQ;AACX,eAAO;MACT;MACA,KAAK,WAAW;AACd,eAAO;MACT;MACA,KAAK,SAAS;AACZ,eAAO;MACT;IACF;EACF;AACF;AACA,IAAI,mBAAmB,MAAM;EAI3B,cAAc;AAHd;AACA;AACA;AAEE,SAAK,YAAY,YAAY,IAAI;EACnC;EACA,UAAU;AACR,SAAK,UAAU,YAAY,IAAI;AAC/B,UAAM,YAAY,KAAK,UAAU,KAAK;AACtC,SAAK,YAAY,UAAU,QAAQ,CAAC;EACtC;AACF;AACA,IAAI,OAAO,MAAM;AACjB,SAAS,IAAI,YAAY,aAAa;AACpC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAM,OAAO,SAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;EACF;AACA,UAAQ,IAAI,SAAS,GAAG,WAAW;AACrC;AACA,SAAS,KAAK,YAAY,aAAa;AACrC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAM,OAAO,SAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;EACF;AACA,UAAQ,KAAK,SAAS,GAAG,WAAW;AACtC;AACA,SAAS,MAAM,YAAY,aAAa;AACtC,MAAI,SAAS;AACX,YAAQ,OAAO,MAAM,OAAO,SAAS,GAAG,WAAW,IAAI,IAAI;AAC3D;EACF;AACA,UAAQ,MAAM,SAAS,GAAG,WAAW;AACvC;AACA,SAAS,YAAY,cAAc;;AACjC,MAAI,SAAS;AACX,WAAO,QAAQ,IAAI,YAAY;EACjC;AACA,UAAOA,MAAA,WAAW,YAAY,MAAvB,gBAAAA,IAA0B;AACnC;AACA,SAAS,sBAAsB,OAAO,UAAU;AAC9C,SAAO,UAAU,UAAU,UAAU;AACvC;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,OAAO,YAAY,aAAa;AAClC,WAAO;EACT;AACA,MAAI,YAAY,MAAM;AACpB,WAAO;EACT;AACA,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO;EACT;AACA,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,KAAK,UAAU,OAAO;EAC/B;AACA,SAAO,QAAQ,SAAS;AAC1B;ACnRO,IAAM,kBAAN,cAA8B,MAAM;EACzC,YACkB,SACA,MACA,OAChB;AACA;MACE,+CAA+C,KAAA,IAAS,KAAK,SAAS,CAAA;IACxE;AANgB,SAAA,UAAA;AACA,SAAA,OAAA;AACA,SAAA,QAAA;AAKhB,SAAK,OAAO;EACd;AACF;ACSO,IAAM,WAAN,MAAuC;EAO5C,OAAO,cACL,SACA,WACQ;AACR,WAAO,QAAQ,cAAmB,SAAS;EAC7C;EAEA,cAAc;AACZ,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,eAAe,SAAQ;AAC5B,SAAK,oCAAoC;EAC3C;EAEQ,mBACN,mBACA,WACA,UACM;AACN,SAAK;MACH;MAEA,GAAI,CAAC,WAAW,QAAQ;IAE1B;EACF;EAEQ,cACN,WACiC;AAGjC,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,KAAK,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC;EAC1E;EAEQ,gBACN,WACA,UACoC;AACpC,UAAM,QAAQ,UAAU,QAAQ,QAAQ;AAExC,QAAI,QAAQ,IAAI;AACd,gBAAU,OAAO,OAAO,CAAC;IAC3B;AAEA,WAAO,CAAC;EACV;EAEQ,kBACN,WACA,UAC6B;AAC7B,UAAM,eAAe,IAAI,SAA+B;AACtD,WAAK,eAAe,WAAW,YAAY;AAM3C,aAAO,SAAS,MAAM,MAAM,IAAI;IAClC;AAGA,WAAO,eAAe,cAAc,QAAQ,EAAE,OAAO,SAAS,KAAK,CAAC;AAEpE,WAAO;EACT;EAEO,gBAAgB,cAA4B;AACjD,SAAK,eAAe;AACpB,WAAO;EACT;;;;;;EAOO,kBAA0B;AAC/B,WAAO,KAAK;EACd;;;;;EAMO,aAAkC;AACvC,WAAO,MAAM,KAAK,KAAK,OAAO,KAAK,CAAC;EACtC;;;;;;;;;;EAWO,KACL,cACG,MACM;AACT,UAAM,YAAY,KAAK,cAAc,SAAS;AAC9C,cAAU,QAAQ,CAAC,aAAa;AAC9B,eAAS,MAAM,MAAM,IAAI;IAC3B,CAAC;AAED,WAAO,UAAU,SAAS;EAC5B;EAUO,YACL,WACA,UACM;AAEN,SAAK,mBAAmB,eAAe,WAAW,QAAQ;AAE1D,UAAM,gBAAgB,KAAK,cAAc,SAAS,EAAE,OAAO,QAAQ;AACnE,SAAK,OAAO,IAAI,WAAW,aAAa;AAExC,QACE,KAAK,eAAe,KACpB,KAAK,cAAc,SAAS,IAAI,KAAK,gBACrC,CAAC,KAAK,mCACN;AACA,WAAK,oCAAoC;AAEzC,YAAM,oBAAoB,IAAI;QAC5B;QACA;QACA,KAAK,cAAc,SAAS;MAC9B;AACA,cAAQ,KAAK,iBAAiB;IAChC;AAEA,WAAO;EACT;EAUO,GACL,WACA,UACM;AACN,WAAO,KAAK,YAAY,WAAW,QAAQ;EAC7C;EAUO,KACL,WACA,UACM;AACN,WAAO,KAAK;MACV;MACA,KAAK,kBAAkB,WAAW,QAAQ;IAC5C;EACF;EAUO,gBACL,WACA,UACM;AACN,UAAM,YAAY,KAAK,cAAc,SAAS;AAE9C,QAAI,UAAU,SAAS,GAAG;AACxB,YAAM,gBAAgB,CAAC,QAAQ,EAAE,OAAO,SAAS;AACjD,WAAK,OAAO,IAAI,WAAW,aAAa;IAC1C,OAAO;AACL,WAAK,OAAO,IAAI,WAAW,UAAU,OAAO,QAAQ,CAAC;IACvD;AAEA,WAAO;EACT;EAUO,oBACL,WACA,UACM;AACN,WAAO,KAAK;MACV;MACA,KAAK,kBAAkB,WAAW,QAAQ;IAC5C;EACF;EAUO,eACL,WACA,UACM;AACN,UAAM,YAAY,KAAK,cAAc,SAAS;AAE9C,QAAI,UAAU,SAAS,GAAG;AACxB,WAAK,gBAAgB,WAAW,QAAQ;AACxC,WAAK,OAAO,IAAI,WAAW,SAAS;AAGpC,WAAK,mBAAmB,kBAAkB,WAAW,QAAQ;IAC/D;AAEA,WAAO;EACT;;;;;;;EAgBO,IACL,WACA,UACM;AACN,WAAO,KAAK,eAAe,WAAW,QAAQ;EAChD;EAMO,mBACL,WACM;AACN,QAAI,WAAW;AACb,WAAK,OAAO,OAAO,SAAS;IAC9B,OAAO;AACL,WAAK,OAAO,MAAM;IACpB;AAEA,WAAO;EACT;;;;EASO,UAAU,WAA8C;AAC7D,WAAO,MAAM,KAAK,KAAK,cAAc,SAAS,CAAC;EACjD;;;;EASO,cAAc,WAAsD;AACzE,WAAO,KAAK,cAAc,SAAS,EAAE;EACvC;EAEO,aACL,WACoC;AACpC,WAAO,KAAK,UAAU,SAAS;EACjC;AACF;AA7TO,IAAM,UAAN;AAAM,QAKJ,sBAAsB;ACdxB,IAAM,kCACX;AAEK,SAAS,gBAAmB,QAA+B;AAChE;;IAEE,WAAW,MAAM,KAAK;;AAE1B;AAEA,SAAS,gBAAgB,QAAgB,OAAkB;AAEzD,aAAW,MAAM,IAAI;AACvB;AAEO,SAAS,mBAAmB,QAAsB;AAEvD,SAAO,WAAW,MAAM;AAC1B;AAaO,IAAME,eAAN,MAAsD;EAO3D,YAA6B,QAAgB;AAAhB,SAAA,SAAA;AAC3B,SAAK,aAAa;AAElB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,IAAI,OAAO,OAAO,WAAY;AAI5C,SAAK,QAAQ,gBAAgB,CAAC;AAE9B,SAAK,OAAO,KAAK,iCAAiC;EACpD;;;;;EAMU,mBAA4B;AACpC,WAAO;EACT;;;;;EAMO,QAAc;AACnB,UAAM,SAAS,KAAK,OAAO,OAAO,OAAO;AACzC,WAAO,KAAK,6BAA6B;AAEzC,QAAI,KAAK,eAAe,WAA+B;AACrD,aAAO,KAAK,8BAA8B;AAC1C;IACF;AAEA,UAAM,cAAc,KAAK,iBAAiB;AAE1C,QAAI,CAAC,aAAa;AAChB,aAAO,KAAK,wDAAwD;AACpE;IACF;AAEA,SAAK,aAAa;AAKlB,UAAM,kBAAkB,KAAK,YAAY;AAEzC,QAAI,iBAAiB;AACnB,aAAO,KAAK,sCAAsC;AAGlD,WAAK,KAAK,CAAC,OAAO,aAAa;AAC7B,eAAO,KAAK,8BAA8B,KAAK;AAI/C,wBAAgB,QAAQ,YAAY,OAAO,QAAQ;AAInD,aAAK,cAAc,KAAK,MAAM;AAC5B,0BAAgB,QAAQ,eAAe,OAAO,QAAQ;AACtD,iBAAO,KAAK,kCAAkC,KAAK;QACrD,CAAC;AAED,eAAO;MACT;AAEA,WAAK,aAAa;AAElB;IACF;AAEA,WAAO,KAAK,yDAAyD;AAGrE,SAAK,MAAM;AAGX,SAAK,YAAY;AAEjB,SAAK,aAAa;EACpB;;;;;;EAOU,QAAc;EAAC;;;;EAKlB,GACL,OACA,UACM;AACN,UAAM,SAAS,KAAK,OAAO,OAAO,IAAI;AAEtC,QACE,KAAK,eAAe,eACpB,KAAK,eAAe,YACpB;AACA,aAAO,KAAK,4CAA4C;AACxD,aAAO;IACT;AAEA,WAAO,KAAK,+BAA+B,OAAO,QAAQ;AAE1D,SAAK,QAAQ,GAAG,OAAO,QAAQ;AAC/B,WAAO;EACT;EAEO,KACL,OACA,UACM;AACN,SAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,WAAO;EACT;EAEO,IACL,OACA,UACM;AACN,SAAK,QAAQ,IAAI,OAAO,QAAQ;AAChC,WAAO;EACT;EAEO,mBACL,OACM;AACN,SAAK,QAAQ,mBAAmB,KAAK;AACrC,WAAO;EACT;;;;EAKO,UAAgB;AACrB,UAAM,SAAS,KAAK,OAAO,OAAO,SAAS;AAE3C,QAAI,KAAK,eAAe,YAAgC;AACtD,aAAO,KAAK,mCAAmC;AAC/C;IACF;AAEA,WAAO,KAAK,8BAA8B;AAC1C,SAAK,aAAa;AAElB,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB,aAAO,KAAK,8CAA8C;AAC1D;IACF;AAIA,SAAK,cAAc;AAEnB,WAAO,KAAK,0BAA0B,gBAAgB,KAAK,MAAM,CAAC;AAElE,QAAI,KAAK,cAAc,SAAS,GAAG;AACjC,aAAO,KAAK,oCAAoC,KAAK,cAAc,MAAM;AAEzE,iBAAW,WAAW,KAAK,eAAe;AACxC,gBAAQ;MACV;AAEA,WAAK,gBAAgB,CAAC;AAEtB,aAAO,KAAK,kCAAkC,KAAK,cAAc,MAAM;IACzE;AAEA,SAAK,QAAQ,mBAAmB;AAChC,WAAO,KAAK,yBAAyB;AAErC,SAAK,aAAa;EACpB;EAEQ,cAAgC;AAzO1C,QAAAF;AA0OI,UAAM,WAAW,gBAAsB,KAAK,MAAM;AAClD,SAAK,OAAO,KAAK,+BAA8BA,MAAA,YAAA,OAAA,SAAA,SAAU,gBAAV,OAAA,SAAAA,IAAuB,IAAI;AAC1E,WAAO;EACT;EAEQ,cAAoB;AAC1B,oBAAgB,KAAK,QAAQ,IAAI;AACjC,SAAK,OAAO,KAAK,wBAAwB,KAAK,OAAO,WAAW;EAClE;EAEQ,gBAAsB;AAC5B,uBAAmB,KAAK,MAAM;AAC9B,SAAK,OAAO,KAAK,4BAA4B,KAAK,OAAO,WAAW;EACtE;AACF;AClPO,SAASG,mBAA0B;AACxC,SAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAC3C;ACcO,IAAM,mBAAN,cAGGD,aAAoB;EAK5B,YAAY,SAAmD;AAC7D,qBAAiB,SAAS,OAAO,QAAQ,IAAI;AAC7C,UAAM,iBAAiB,MAAM;AAC7B,SAAK,eAAe,QAAQ;EAC9B;EAEU,QAAQ;AAChB,UAAM,SAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,WAAO,KAAK,mCAAmC,KAAK,aAAa,MAAM;AAEvE,eAAW,eAAe,KAAK,cAAc;AAC3C,aAAO,KAAK,gCAAgC,YAAY,YAAY,IAAI;AACxE,kBAAY,MAAM;AAElB,aAAO,KAAK,yCAAyC;AACrD,WAAK,cAAc,KAAK,MAAM,YAAY,QAAQ,CAAC;IACrD;EACF;EAEO,GACL,OACA,UACM;AAGN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,GAAG,OAAO,QAAQ;IAChC;AAEA,WAAO;EACT;EAEO,KACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,KAAK,OAAO,QAAQ;IAClC;AAEA,WAAO;EACT;EAEO,IACL,OACA,UACM;AACN,eAAW,eAAe,KAAK,cAAc;AAC3C,kBAAY,IAAI,OAAO,QAAQ;IACjC;AAEA,WAAO;EACT;EAEO,mBACL,OACM;AACN,eAAW,gBAAgB,KAAK,cAAc;AAC5C,mBAAa,mBAAmB,KAAK;IACvC;AAEA,WAAO;EACT;AACF;AEtFO,SAAS,uBAAuB,SAAqC;AAC1E,SAAO,CACL,GACA,YAIG;;AACH,UAAM,EAAE,SAAS,aAAa,IAAI;AAClC,UAAM,UAAU,mBAAmB,aAAa,OAAO;AASvD,SAAIF,MAAA,aAAa,SAAS,SAAtB,gBAAAA,IAA4B,SAAS,WAAW;AAClD;IACF;AAEA,UAAM,WACJ,aAAa,SAAS,WAAW,IAC7B,SAAS,MAAM,IACf,IAAI;;;;;;;MAOF,cAAc,mBAAmB,aAAa,SAAS,MAAM,IACzD,aAAa,SAAS,OACtB;MACJ;QACE,GAAG;;;;;;QAMH,KAAK,QAAQ;MACf;IACF;AAEN,YAAQ,QAAQ;MACd,aAAa,mBAAmB,oBAAoB;MACpD;QACE,WAAW,aAAa,QAAQ;QAChC;QACA;MACF;IACF;EACF;AACF;AC5DO,SAAS,oBACd,cACA,SACM;AACN,MAAI,EAAC,mCAAS,UAAS,CAAC,SAAS,KAAK,WAAW,aAAa,KAAK,GAAG;AACpED,aAAS;MACP,uFACgF,aAAa,KAAK;;;;IAKpG;EACF;AACF;A1BRO,IAAM,qBAAqB,CAChC,YACiB;AACjB,SAAO,SAAS,MAAM,SAAS,eAAe;AAC5C,UAAM,sBAAsB,YAAY;AAItC,cAAQ,OAAO,mBAAmB;AAGlC,cAAQ,cAAc;QACpB;QACA,sBAAsB,SAAS,OAAO;MACxC;AAGA,cAAQ,cAAc,GAAG,YAAY,uBAAuB,OAAO,CAAC;AAEpE,YAAM,WAAW,MAAM;QACrB,QAAQ,cAAc;QACtB,QAAQ,cAAc;QACtB,QAAQ;MACV;AAEA,YAAM,CAAC,QAAQ,YAAY,IAAI;AAE/B,UAAI,CAAC,QAAQ;AACX,cAAM,wBAAuB,+CAAe,cACxCA,SAAS;UACP;;;;;UAKA,QAAQ,cAAc;QACxB,IACAA,SAAS;UACP;;;;;UAKA,QAAQ,cAAc;UACtB,SAAS;QACX;AAEJ,cAAM,IAAI,MAAM,oBAAoB;MACtC;AAEA,cAAQ,SAAS;AACjB,cAAQ,eAAe;AAEvB,cAAQ,OAAO,YAAY,QAAQ,gBAAgB,MAAM;AACvD,YAAI,OAAO,UAAU,aAAa;AAKhC,kBAAQ,cAAc,KAAK,eAAe;QAC5C;AAGA,eAAO,cAAc,QAAQ,iBAAiB;AAK9C,eAAO,YAAY,EAAE,MAAM,kBAAkB,CAAC;MAChD,CAAC;AAID,YAAM,qBAAqB,OAAO,EAAE,MAAM,CAACH,WAAU;AACnDG,iBAAS;UACP;QACF;AAEA,gBAAQ,MAAMH,MAAK;MACrB,CAAC;AAED,cAAQ,oBAAoB,OAAO;QACjC,MAAM,QAAQ,cAAc,KAAK,mBAAmB;QACpD;MACF;AAIA,0BAAoB,cAAc,QAAQ,YAAY;AAEtD,aAAO;IACT;AAEA,UAAM,qBAAqB,oBAAoB,EAAE;MAC/C,OAAO,iBAAiB;AACtB,cAAM,kBAAkB,aAAa,cAAc,aAAa;AAKhE,YAAI,iBAAiB;AACnB,gBAAM,IAAI,QAAc,CAAC,YAAY;AACnC,4BAAgB,iBAAiB,eAAe,MAAM;AACpD,kBAAI,gBAAgB,UAAU,aAAa;AACzC,uBAAO,QAAQ;cACjB;YACF,CAAC;UACH,CAAC;QACH;AAGA,cAAM,cAAc,SAAS,OAAO,EAAE,MAAM,CAACA,WAAU;AACrD,gBAAM,IAAI,MAAM,6BAA6BA,iCAAO,OAAO,EAAE;QAC/D,CAAC;AAED,eAAO;MACT;IACF;AAEA,WAAO;EACT;AACF;A4BhIO,SAAS,iBAAiB,OAA4B,CAAC,GAAS;AACrE,MAAI,KAAK,OAAO;AACd;EACF;AAGA,UAAQ;IACN,KAAKG,SAAS,cAAc,mBAAmB,CAAC;IAChD;EACF;AACF;ADRO,IAAM,aAAa,CACxB,YACgB;AAChB,SAAO,SAAS,OAAO;;AAGrB,QAAI,CAAC,QAAQ,kBAAkB;AAC7BA,eAAS;QACP;MACF;AACA;IACF;AAOA,YAAQ,cAAc,KAAK,iBAAiB;AAC5C,YAAQ,mBAAmB;AAC3B,WAAO,cAAc,QAAQ,iBAAiB;AAM9C,WAAO,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAE9C,qBAAiB,EAAE,QAAOC,MAAA,QAAQ,iBAAR,gBAAAA,IAAsB,MAAM,CAAC;EACzD;AACF;AEzBO,IAAM,wBAAoD;EAC/D,eAAe;IACb,KAAK;IACL,SAAS;EACX;EACA,OAAO;EACP,gBAAgB;EAChB,oBAAoB;EACpB,WAAW,WAAW,sBAAsB;AAC1C,WAAO,cAAc;EACvB;AACF;ACLO,SAAS,yBAG4B;AAC1C,QAAM,WAAoD,CACxD,SACA,WACG;AACH,aAAS,QAAQ;AAEjB,aAAS,UAAU,CAAC,SAAS;AAC3B,UAAI,SAAS,UAAU,WAAW;AAChC;MACF;AAEA,eAAS,SAAS;AAElB,YAAM,cAAc,CAAQ,UAAiB;AAC3C,iBAAS,QAAQ;AACjB,eAAO;MACT;AAEA,aAAO;QACL,gBAAgB,UAAU,OAAO,QAAQ,QAAQ,IAAI,EAAE,KAAK,WAAW;MACzE;IACF;AAEA,aAAS,SAAS,CAAC,WAAW;AAC5B,UAAI,SAAS,UAAU,WAAW;AAChC;MACF;AAEA,qBAAe,MAAM;AACnB,iBAAS,QAAQ;MACnB,CAAC;AAED,aAAO,OAAQ,SAAS,kBAAkB,MAAO;IACnD;EACF;AAEA,SAAO;AACT;;AChDO,IAAMI,oBAAN,mBAAqD,QAAe;EAMzE,YAAY,WAAmC,MAAM;AACnD,UAAM,mBAAmB,uBAAuB;AAChD,UAAM,CAAC,iBAAiB,mBAAmB;AACzC,uBAAiB,iBAAiB,cAAc;AAChD,2CAAW,iBAAiB,SAAS,iBAAiB;IACxD,CAAC;AAXE;AACL;AAEO;AACA;AASL,uBAAK,WAAY;AACjB,SAAK,UAAU,mBAAK,WAAU;AAC9B,SAAK,SAAS,mBAAK,WAAU;EAC/B;EAEA,IAAW,QAAQ;AACjB,WAAO,mBAAK,WAAU;EACxB;EAEA,IAAW,kBAAkB;AAC3B,WAAO,mBAAK,WAAU;EACxB;EAEO,KACL,aACA,YACA;AACA,WAAO,sBAAK,yCAAL,WAAe,MAAM,KAAK,aAAa,UAAU;EAC1D;EAEO,MACL,YACA;AACA,WAAO,sBAAK,yCAAL,WAAe,MAAM,MAAM,UAAU;EAC9C;EAEO,QAAQ,WAAuC;AACpD,WAAO,sBAAK,yCAAL,WAAe,MAAM,QAAQ,SAAS;EAC/C;AAUF,GAlDE,2BADK,4CA2CL,cAAA,SACE,SACqC;AACrC,SAAO,OAAO,iBAAiB,SAAS;IACtC,SAAS,EAAE,cAAc,MAAM,OAAO,KAAK,QAAQ;IACnD,QAAQ,EAAE,cAAc,MAAM,OAAO,KAAK,OAAO;EACnD,CAAC;AACH,GAlDK;AERA,IAAM,mBAAN,cAA+B,MAAM;EAC1C,YAAY,SAAkB;AAC5B,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,iBAAiB,SAAS;EACxD;AACF;ADFA,IAAM,kBAAkB,OAAO,iBAAiB;AACzC,IAAM,mBAAmB,OAAO,kBAAkB;AAElD,IAAM,oBAAN,MAAwB;EAkB7B,YAAoB,SAAkB;AAAlB,SAAA,UAAA;AAClB,SAAK,eAAe,IAAI;AACxB,SAAK,gBAAgB,IAAI,IAAIA,iBAAgB;EAC/C;;;;;;;;EASO,YAAY,UAA0B;AAC3C,IAAAP,WAAU;MACR;MACA,CAAC,KAAK,eAAe;MACrB;MACA,KAAK,QAAQ;MACb,KAAK,QAAQ;IACf;AAEA,SAAK,eAAe,IAAI;AACxB,SAAK,gBAAgB,EAAE,QAAQ,QAAQ;EASzC;;;;;;;;;EAUO,UAAU,QAA4C;AAC3D,IAAAA,WAAU;MACR;MACA,CAAC,KAAK,eAAe;MACrB;MACA,KAAK,QAAQ;MACb,KAAK,QAAQ;IACf;AAEA,SAAK,eAAe,IAAI;AAOxB,SAAK,gBAAgB,EAAE,QAAQ,MAAM;EACvC;AACF;AE7EA,eAAsB,UAIpB,SACA,cACG,MACY;AACf,QAAM,WAAW,QAAQ,UAAU,SAAS;AAE5C,MAAI,SAAS,WAAW,GAAG;AACzB;EACF;AAEA,aAAW,YAAY,UAAU;AAC/B,UAAM,SAAS,MAAM,SAAS,IAAI;EACpC;AACF;AErBO,SAASQ,UAAY,OAAY,QAAQ,OAAmB;AACjE,SAAO,QACH,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,WAAW,UAAU,IAC3D,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAChD;ACCO,SAAS,qBACd,KACA,KACA;AACA,MAAI;AACF,QAAI,GAAG;AACP,WAAO;EACT,SAAQ,GAAN;AACA,WAAO;EACT;AACF;ACZO,SAAS,0BAA0B,MAAyB;AACjE,SAAO,IAAI;IACT,KAAK;MACH,gBAAgB,QACZ;QACE,MAAM,KAAK;QACX,SAAS,KAAK;QACd,OAAO,KAAK;MACd,IACA;IACN;IACA;MACE,QAAQ;MACR,YAAY;MACZ,SAAS;QACP,gBAAgB;MAClB;IACF;EACF;AACF;AAYO,SAAS,gBAAgB,UAA8C;AAC5E,SACE,YAAY,QACZ,oBAAoB,YACpB,qBAAqB,UAAU,MAAM,KACrC,SAAS,SAAS;AAEtB;AAOO,SAAS,eAAe,OAAmC;AAChE,SACEA,UAA8B,OAAO,IAAI,KACzC,qBAAqB,OAAO,QAAQ,KACpC,qBAAqB,OAAO,YAAY,KACxC,qBAAqB,OAAO,UAAU;AAE1C;AC1DO,SAAS,gBACdT,QACgC;AAChC,MAAIA,UAAS,MAAM;AACjB,WAAO;EACT;AAEA,MAAI,EAAEA,kBAAiB,QAAQ;AAC7B,WAAO;EACT;AAEA,SAAO,UAAUA,UAAS,WAAWA;AACvC;AJgCA,eAAsBU,eACpB,SACkB;AAClB,QAAM,iBAAiB,OACrB,aACG;AACH,QAAI,oBAAoB,OAAO;AAC7B,cAAQ,QAAQ,QAAQ;AACxB,aAAO;IACT;AAGA,QAAI,gBAAgB,QAAQ,GAAG;AAC7B,cAAQ,eAAe,QAAQ;AAC/B,aAAO;IACT;AAOA,QAAI,eAAe,QAAQ,GAAG;AAC5B,YAAM,QAAQ,WAAW,QAAQ;AACjC,aAAO;IACT;AAGA,QAAID,UAAS,QAAQ,GAAG;AACtB,cAAQ,QAAQ,QAAQ;AACxB,aAAO;IACT;AAEA,WAAO;EACT;AAEA,QAAM,sBAAsB,OAAOT,WAAqC;AAGtE,QAAIA,kBAAiB,kBAAkB;AACrC,YAAM,OAAO;IACf;AAGA,QAAI,gBAAgBA,MAAK,GAAG;AAC1B,cAAQ,QAAQA,MAAK;AACrB,aAAO;IACT;AAGA,QAAIA,kBAAiB,UAAU;AAC7B,aAAO,MAAM,eAAeA,MAAK;IACnC;AAEA,WAAO;EACT;AAKA,UAAQ,QAAQ,KAAK,WAAW,CAAC,EAAE,WAAW,iBAAiB,MAAM;AACnE,QAAI,qBAAqB,QAAQ,WAAW;AAC1C;IACF;AAEA,QAAI,QAAQ,WAAW,gBAAgB,EAAE,UAAU,WAAW;AAC5D,cAAQ,WAAW,gBAAgB,EAAE,QAAQ,MAAS;IACxD;EACF,CAAC;AAED,QAAM,sBAAsB,IAAIQ,iBAA+B;AAK/D,MAAI,QAAQ,QAAQ,QAAQ;AAC1B,QAAI,QAAQ,QAAQ,OAAO,SAAS;AAClC,0BAAoB,OAAO,QAAQ,QAAQ,OAAO,MAAM;IAC1D,OAAO;AACL,cAAQ,QAAQ,OAAO;QACrB;QACA,MAAM;AACJ,8BAAoB,OAAO,QAAQ,QAAQ,OAAO,MAAM;QAC1D;QACA,EAAE,MAAM,KAAK;MACf;IACF;EACF;AAEA,QAAM,SAAS,MAAM,MAAM,YAAY;AAKrC,UAAM,0BAA0B,UAAU,QAAQ,SAAS,WAAW;MACpE,WAAW,QAAQ;MACnB,SAAS,QAAQ;MACjB,YAAY,QAAQ;IACtB,CAAC;AAED,UAAM,QAAQ,KAAK;;MAEjB;MACA;MACA,QAAQ,WAAW,gBAAgB;IACrC,CAAC;AAID,WAAO,MAAM,QAAQ,WAAW,gBAAgB;EAClD,CAAC;AAGD,MAAI,oBAAoB,UAAU,YAAY;AAC5C,YAAQ,QAAQ,oBAAoB,eAAe;AACnD,WAAO;EACT;AAEA,MAAI,OAAO,OAAO;AAGhB,QAAI,MAAM,oBAAoB,OAAO,KAAK,GAAG;AAC3C,aAAO;IACT;AAKA,QAAI,QAAQ,QAAQ,cAAc,oBAAoB,IAAI,GAAG;AAI3D,YAAM,+BAA+B,IAAI;QACvC,QAAQ;MACV;AAEA,YAAM,UAAU,QAAQ,SAAS,sBAAsB;QACrD,OAAO,OAAO;QACd,SAAS,QAAQ;QACjB,WAAW,QAAQ;QACnB,YAAY;MACd,CAAC,EAAE,KAAK,MAAM;AAKZ,YACE,6BAA6B,gBAAgB,EAAE,UAAU,WACzD;AACA,uCAA6B,gBAAgB,EAAE,QAAQ,MAAS;QAClE;MACF,CAAC;AAED,YAAM,aAAa,MAAM;QACvB,MAAM,6BAA6B,gBAAgB;MACrD;AASA,UAAI,WAAW,OAAO;AACpB,eAAO,oBAAoB,WAAW,KAAK;MAC7C;AAEA,UAAI,WAAW,MAAM;AACnB,eAAO,eAAe,WAAW,IAAI;MACvC;IACF;AAGA,YAAQ,WAAW,0BAA0B,OAAO,KAAK,CAAC;AAC1D,WAAO;EACT;AAQA,MAAI,OAAO,MAAM;AACf,WAAO,eAAe,OAAO,IAAI;EACnC;AAGA,SAAO;AACT;AKtOO,SAASG,uBAAsB,cAA+B;AACnE,QAAM,aAAa,OAAO,yBAAyB,YAAY,YAAY;AAG3E,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;EACT;AAGA,MACE,OAAO,WAAW,QAAQ,cAC1B,OAAO,WAAW,IAAI,MAAM,aAC5B;AACA,WAAO;EACT;AAGA,MAAI,OAAO,WAAW,QAAQ,eAAe,WAAW,SAAS,MAAM;AACrE,WAAO;EACT;AAEA,MAAI,OAAO,WAAW,QAAQ,eAAe,CAAC,WAAW,cAAc;AACrE,YAAQ;MACN,mDAAmD,YAAA;IACrD;AACA,WAAO;EACT;AAEA,SAAO;AACT;AEjCO,SAAS,mBAAmB,OAAiB;AAClD,SAAO,OAAO,OAAO,IAAI,UAAU,iBAAiB,GAAG;IACrD;EACF,CAAC;AACH;ACFA,IAAM,uBAAuB;EAC3B;EACA;EACA;EACA;EACA;AACF;AAEA,IAAM,iBAAiB,OAAO,gBAAgB;AAK9C,eAAsB,oBACpB,SACA,UACmB;AACnB,MAAI,SAAS,WAAW,OAAO,QAAQ,QAAQ,MAAM;AACnD,WAAO,QAAQ,OAAO,mBAAmB,CAAC;EAC5C;AAEA,QAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;AAEtC,MAAI;AACJ,MAAI;AAEF,kBAAc,IAAI,IAAI,SAAS,QAAQ,IAAI,UAAU,GAAI,QAAQ,GAAG;EACtE,SAASX,QAAP;AACA,WAAO,QAAQ,OAAO,mBAAmBA,MAAK,CAAC;EACjD;AAEA,MACE,EAAE,YAAY,aAAa,WAAW,YAAY,aAAa,WAC/D;AACA,WAAO,QAAQ;MACb,mBAAmB,qCAAqC;IAC1D;EACF;AAEA,MAAI,QAAQ,IAAI,SAAS,cAAc,IAAI,IAAI;AAC7C,WAAO,QAAQ,OAAO,mBAAmB,yBAAyB,CAAC;EACrE;AAEA,SAAO,eAAe,SAAS,gBAAgB;IAC7C,QAAQ,QAAQ,IAAI,SAAS,cAAc,KAAK,KAAK;EACvD,CAAC;AAED,MACE,QAAQ,SAAS,WAChB,YAAY,YAAY,YAAY,aACrC,CAAC,WAAW,YAAY,WAAW,GACnC;AACA,WAAO,QAAQ;MACb,mBAAmB,kDAAkD;IACvE;EACF;AAEA,QAAM,cAA2B,CAAC;AAElC,MACG,CAAC,KAAK,GAAG,EAAE,SAAS,SAAS,MAAM,KAAK,QAAQ,WAAW,UAC3D,SAAS,WAAW,OAAO,CAAC,CAAC,QAAQ,KAAK,EAAE,SAAS,QAAQ,MAAM,GACpE;AACA,gBAAY,SAAS;AACrB,gBAAY,OAAO;AAEnB,yBAAqB,QAAQ,CAAC,eAAe;AAC3C,cAAQ,QAAQ,OAAO,UAAU;IACnC,CAAC;EACH;AAEA,MAAI,CAAC,WAAW,YAAY,WAAW,GAAG;AACxC,YAAQ,QAAQ,OAAO,eAAe;AACtC,YAAQ,QAAQ,OAAO,qBAAqB;AAC5C,YAAQ,QAAQ,OAAO,QAAQ;AAC/B,YAAQ,QAAQ,OAAO,MAAM;EAC/B;AAQA,cAAY,UAAU,QAAQ;AAC9B,SAAO,MAAM,IAAI,QAAQ,aAAa,WAAW,CAAC;AACpD;AAKA,SAAS,WAAW,MAAW,OAAqB;AAClD,MAAI,KAAK,WAAW,MAAM,UAAU,KAAK,WAAW,QAAQ;AAC1D,WAAO;EACT;AAEA,MACE,KAAK,aAAa,MAAM,YACxB,KAAK,aAAa,MAAM,YACxB,KAAK,SAAS,MAAM,MACpB;AACA,WAAO;EACT;AAEA,SAAO;AACT;AC3GO,IAAM,4BAAN,cAAwC,gBAAgB;EAC7D,cAAc;AACZ,YAAQ;MACN;IACF;AAEA,UAAM;MACJ,UAAU,OAAO,YAAY;AAE3B,mBAAW,QAAQ,KAAK;MAC1B;IACF,CAAC;EACH;AACF;ACRA,IAAM,iBAAN,cAA6B,gBAAgB;EAC3C,YACE,qBACG,YACH;AACA,UAAM,CAAC,GAAG,GAAG,UAAU;AAEvB,UAAM,WAAW,CAAC,MAAM,UAAiB,GAAG,gBAAgB,EAAE;MAC5D,CAACY,WAAU,cAAcA,UAAS,YAAY,SAAS;IACzD;AAEA,WAAO,eAAe,MAAM,YAAY;MACtC,MAAM;AACJ,eAAO;MACT;IACF,CAAC;EACH;AACF;AAEO,SAAS,qBAAqB,iBAAwC;AAC3E,SAAO,gBACJ,YAAY,EACZ,MAAM,GAAG,EACT,IAAI,CAAC,WAAW,OAAO,KAAK,CAAC;AAClC;AAEA,SAAS,0BACP,iBACwB;AACxB,MAAI,oBAAoB,IAAI;AAC1B,WAAO;EACT;AAEA,QAAM,UAAU,qBAAqB,eAAe;AAEpD,MAAI,QAAQ,WAAW,GAAG;AACxB,WAAO;EACT;AAEA,QAAM,eAAe,QAAQ;IAC3B,CAACC,eAAc,WAAW;AACxB,UAAI,WAAW,UAAU,WAAW,UAAU;AAC5C,eAAOA,cAAa,OAAO,IAAI,oBAAoB,MAAM,CAAC;MAC5D,WAAW,WAAW,WAAW;AAC/B,eAAOA,cAAa,OAAO,IAAI,oBAAoB,SAAS,CAAC;MAC/D,WAAW,WAAW,MAAM;AAC1B,eAAOA,cAAa,OAAO,IAAI,0BAA0B,CAAC;MAC5D,OAAO;AACLA,sBAAa,SAAS;MACxB;AAEA,aAAOA;IACT;IACA,CAAC;EACH;AAEA,SAAO,IAAI,eAAe,YAAY;AACxC;AAEO,SAAS,mBACd,UAC4B;AAC5B,MAAI,SAAS,SAAS,MAAM;AAC1B,WAAO;EACT;AAEA,QAAM,sBAAsB;IAC1B,SAAS,QAAQ,IAAI,kBAAkB,KAAK;EAC9C;AAEA,MAAI,CAAC,qBAAqB;AACxB,WAAO;EACT;AAKA,WAAS,KAAK,OAAO,oBAAoB,QAAQ;AACjD,SAAO,oBAAoB;AAC7B;AJpEO,IAAM,oBAAN,cAA+BP,aAAiC;EAGrE,cAAc;AACZ,UAAM,kBAAiB,MAAM;EAC/B;EAEU,mBAAmB;AAC3B,WAAOK,uBAAsB,OAAO;EACtC;EAEA,MAAgB,QAAQ;AACtB,UAAM,YAAY,WAAW;AAE7B,IAAAV;MACE,CAAE,UAAkB,iBAAiB;MACrC;IACF;AAEA,eAAW,QAAQ,OAAO,OAAO,SAAS;AACxC,YAAM,YAAYM,iBAAgB;AAQlC,YAAM,gBACJ,OAAO,UAAU,YACjB,OAAO,aAAa,eACpB,CAAC,YAAY,KAAK,IACd,IAAI,IAAI,OAAO,SAAS,IAAI,IAC5B;AAEN,YAAM,UAAU,IAAI,QAAQ,eAAe,IAAI;AAK/C,UAAI,iBAAiB,SAAS;AAC5B,sBAAc,SAAS,KAAK;MAC9B;AAEA,YAAM,kBAAkB,IAAIC,iBAA0B;AACtD,YAAM,aAAa,IAAI,kBAAkB,OAAO;AAEhD,WAAK,OAAO,KAAK,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AACvD,WAAK,OAAO,KAAK,qCAAqC;AAEtD,WAAK,OAAO;QACV;QACA,KAAK,QAAQ,cAAc,SAAS;MACtC;AAEA,YAAM,mBAAmB,MAAME,eAAc;QAC3C;QACA;QACA,SAAS,KAAK;QACd;QACA,YAAY,OAAO,gBAAgB;AACjC,eAAK,OAAO,KAAK,6BAA6B;YAC5C;UACF,CAAC;AAGD,gBAAM,qBAAqB,mBAAmB,WAAW;AACzD,gBAAM,WACJ,uBAAuB,OACnB,cACA,IAAI,cAAc,oBAAoB,WAAW;AAEvD,wBAAc,OAAO,QAAQ,KAAK,QAAQ;AAQ1C,cAAI,cAAc,mBAAmB,SAAS,MAAM,GAAG;AAGrD,gBAAI,QAAQ,aAAa,SAAS;AAChC,8BAAgB,OAAO,mBAAmB,qBAAqB,CAAC;AAChE;YACF;AAEA,gBAAI,QAAQ,aAAa,UAAU;AACjC,kCAAoB,SAAS,QAAQ,EAAE;gBACrC,CAACI,cAAa;AACZ,kCAAgB,QAAQA,SAAQ;gBAClC;gBACA,CAAC,WAAW;AACV,kCAAgB,OAAO,MAAM;gBAC/B;cACF;AACA;YACF;UACF;AAEA,cAAI,KAAK,QAAQ,cAAc,UAAU,IAAI,GAAG;AAC9C,iBAAK,OAAO,KAAK,kCAAkC;AAKnD,kBAAM,UAAU,KAAK,SAAS,YAAY;;;;cAIxC,UAAU,SAAS,MAAM;cACzB,kBAAkB;cAClB;cACA;YACF,CAAC;UACH;AAEA,0BAAgB,QAAQ,QAAQ;QAClC;QACA,gBAAgB,CAAC,aAAa;AAC5B,eAAK,OAAO,KAAK,wBAAwB,EAAE,SAAS,CAAC;AACrD,0BAAgB,OAAO,mBAAmB,QAAQ,CAAC;QACrD;QACA,SAAS,CAACd,WAAU;AAClB,eAAK,OAAO,KAAK,6BAA6B,EAAE,OAAAA,OAAM,CAAC;AACvD,0BAAgB,OAAOA,MAAK;QAC9B;MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,aAAK,OAAO,KAAK,qDAAqD;AACtE,eAAO;MACT;AAEA,WAAK,OAAO;QACV;MACF;AAQA,YAAM,+BAA+B,QAAQ,MAAM;AAEnD,aAAO,UAAU,OAAO,EAAE,KAAK,OAAO,aAAa;AACjD,aAAK,OAAO,KAAK,4BAA4B,QAAQ;AAErD,YAAI,KAAK,QAAQ,cAAc,UAAU,IAAI,GAAG;AAC9C,eAAK,OAAO,KAAK,kCAAkC;AAEnD,gBAAM,gBAAgB,SAAS,MAAM;AAErC,gBAAM,UAAU,KAAK,SAAS,YAAY;YACxC,UAAU;YACV,kBAAkB;YAClB,SAAS;YACT;UACF,CAAC;QACH;AAEA,eAAO;MACT,CAAC;IACH;AAEA,WAAO,eAAe,WAAW,OAAO,mBAAmB;MACzD,YAAY;MACZ,cAAc;MACd,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO,eAAe,WAAW,OAAO,mBAAmB;QACzD,OAAO;MACT,CAAC;AAED,iBAAW,QAAQ;AAEnB,WAAK,OAAO;QACV;QACA,WAAW,MAAM;MACnB;IACF,CAAC;EACH;AACF;AA1LO,IAAM,mBAAN;AAAM,iBACJ,SAAS,OAAO,OAAO;AOdzB,SAAS,kBACd,MACA,OACY;AACZ,QAAM,SAAS,IAAI,WAAW,KAAK,aAAa,MAAM,UAAU;AAChE,SAAO,IAAI,MAAM,CAAC;AAClB,SAAO,IAAI,OAAO,KAAK,UAAU;AACjC,SAAO;AACT;ACXO,IAAM,gBAAN,MAAqC;EAwB1C,YACE,MACA,SACA;AA1BF,SAAS,OAAO;AAChB,SAAS,kBAAkB;AAC3B,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAE1B,SAAO,OAAe;AACtB,SAAO,aAAiC;AAExC,SAAO,gBAAoC;AAC3C,SAAO,aAAqB;AAE5B,SAAO,YAAqB;AAC5B,SAAO,WAAoB;AAC3B,SAAO,aAAsB;AAC7B,SAAO,mBAA4B;AACnC,SAAO,UAAmB;AAC1B,SAAO,mBAA4B;AACnC,SAAO,SAAiB;AACxB,SAAO,QAAgB;AAEvB,SAAA,eAAwB;AACxB,SAAA,cAAuB;AAMrB,SAAK,OAAO;AACZ,SAAK,UAAS,WAAA,OAAA,SAAA,QAAS,WAAU;AACjC,SAAK,iBAAgB,WAAA,OAAA,SAAA,QAAS,kBAAiB;AAC/C,SAAK,YAAY,KAAK,IAAI;EAC5B;EAEO,eAA8B;AACnC,WAAO,CAAC;EACV;EAEO,UAAU,MAAc,SAAmB,YAAsB;AACtE,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC,CAAC;AACjB,SAAK,aAAa,CAAC,CAAC;EACtB;EAEO,iBAAiB;AACtB,SAAK,mBAAmB;EAC1B;EAEO,kBAAkB;EAAC;EACnB,2BAA2B;EAAC;AACrC;AChDO,IAAM,wBAAN,cAAoC,cAAc;EAMvD,YAAY,MAAc,MAA0B;AAClD,UAAM,IAAI;AAEV,SAAK,oBAAmB,QAAA,OAAA,SAAA,KAAM,qBAAoB;AAClD,SAAK,YAAW,QAAA,OAAA,SAAA,KAAM,aAAY;AAClC,SAAK,UAAS,QAAA,OAAA,SAAA,KAAM,WAAU;AAC9B,SAAK,SAAQ,QAAA,OAAA,SAAA,KAAM,UAAS;EAC9B;AACF;ACbA,IAAM,0BAA0B,OAAO,kBAAkB;AAElD,SAAS,YACd,QACA,MACA,MAC+B;AAC/B,QAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AAMA,QAAM,qBAAqB,0BACvB,gBACA;AAEJ,QAAM,QAAQ,eAAe,SAAS,IAAI,IACtC,IAAI,mBAAmB,MAAM;IAC3B,kBAAkB;IAClB,SAAQ,QAAA,OAAA,SAAA,KAAM,WAAU;IACxB,QAAO,QAAA,OAAA,SAAA,KAAM,UAAS;EACxB,CAAC,IACD,IAAI,cAAc,MAAM;IACtB;IACA,eAAe;EACjB,CAAC;AAEL,SAAO;AACT;ACpCO,SAAS,mBACd,QACA,cACe;AACf,MAAI,EAAE,gBAAgB,SAAS;AAC7B,WAAO;EACT;AAEA,QAAM,cAAc,OAAO,UAAU,eAAe,KAAK,QAAQ,YAAY;AAC7E,MAAI,aAAa;AACf,WAAO;EACT;AAEA,QAAM,YAAY,QAAQ,eAAe,MAAM;AAC/C,SAAO,YAAY,mBAAmB,WAAW,YAAY,IAAI;AACnE;ACKO,SAAS,YACd,QACA,SACQ;AACR,QAAM,QAAQ,IAAI,MAAM,QAAQ,sBAAsB,OAAO,CAAC;AAE9D,SAAO;AACT;AAEA,SAAS,sBACP,SACiB;AACjB,QAAM,EAAE,iBAAiB,YAAY,aAAa,YAAY,IAAI;AAClE,QAAM,UAA2B,CAAC;AAElC,MAAI,OAAO,oBAAoB,aAAa;AAC1C,YAAQ,YAAY,SAAU,QAAQ,MAAM,WAAW;AACrD,YAAM,OAAO,QAAQ,UAAU,KAAK,MAAM,QAAe,MAAM,SAAS;AACxE,aAAO,gBAAgB,KAAK,WAAW,MAAM,IAAI;IACnD;EACF;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,WAAW;AACvD,UAAM,OAAO,MAAM;AACjB,YAAM,iBAAiB,mBAAmB,QAAQ,YAAY,KAAK;AACnE,YAAM,iBAAiB,QAAQ;QAC7B;QACA;MACF;AAGA,UAAI,QAAO,kBAAA,OAAA,SAAA,eAAgB,SAAQ,aAAa;AAC9C,uBAAe,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC;AAC5C,eAAO;MACT;AAGA,aAAO,QAAQ,eAAe,gBAAgB,cAAc;QAC1D,UAAU;QACV,YAAY;QACZ,cAAc;QACd,OAAO;MACT,CAAC;IACH;AAEA,QAAI,OAAO,gBAAgB,aAAa;AACtC,aAAO,YAAY,KAAK,QAAQ,CAAC,cAAc,SAAS,GAAG,IAAI;IACjE;AAEA,WAAO,KAAK;EACd;AAEA,UAAQ,MAAM,SAAU,QAAQ,cAAc,UAAU;AAItD,UAAM,OAAO,MAAM,OAAO,YAAmB;AAE7C,UAAM,QACJ,OAAO,gBAAgB,cACnB,YAAY,KAAK,QAAQ,CAAC,cAAc,QAAQ,GAAG,IAAI,IACvD,KAAK;AAEX,QAAI,OAAO,UAAU,YAAY;AAC/B,aAAO,IAAI,SAAqB;AAC9B,cAAMe,QAAO,MAAM,KAAK,QAAQ,GAAG,IAAI;AAEvC,YAAI,OAAO,eAAe,aAAa;AACrC,iBAAO,WAAW,KAAK,QAAQ,CAAC,cAAqB,IAAI,GAAGA,KAAI;QAClE;AAEA,eAAOA,MAAK;MACd;IACF;AAEA,WAAO;EACT;AAEA,SAAO;AACT;ACvGO,SAAS,yBACd,MACgC;AAChC,QAAM,iBAAgD;IACpD;IACA;IACA;IACA;IACA;EACF;AACA,SAAO,eAAe,KAAK,CAAC,kBAAkB;AAC5C,WAAO,KAAK,WAAW,aAAa;EACtC,CAAC;AACH;ACTO,SAAS,UAAU,MAA8C;AACtE,MAAI;AACF,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,WAAO;EACT,SAAS,GAAP;AACA,WAAO;EACT;AACF;ACLO,SAAS,eACd,SACA,MACU;AASV,QAAM,qBAAqB,cAAc,mBAAmB,QAAQ,MAAM,IACtE,OACA;AAEJ,SAAO,IAAI,cAAc,oBAAoB;IAC3C,KAAK,QAAQ;IACb,QAAQ,QAAQ;IAChB,YAAY,QAAQ;IACpB,SAAS;MACP,QAAQ,sBAAsB;IAChC;EACF,CAAC;AACH;AAEA,SAAS,sCAAsC,eAAgC;AAC7E,QAAM,UAAU,IAAI,QAAQ;AAE5B,QAAM,QAAQ,cAAc,MAAM,SAAS;AAC3C,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,KAAK,MAAM,IAAI;AACtB;IACF;AAEA,UAAM,CAAC,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,IAAI;AACxC,UAAM,QAAQ,MAAM,KAAK,IAAI;AAE7B,YAAQ,OAAO,MAAM,KAAK;EAC5B;AAEA,SAAO;AACT;AC5CA,eAAsB,kBACpB,OACiB;AACjB,QAAM,wBAAwB,MAAM,QAAQ,IAAI,gBAAgB;AAEhE,MAAI,yBAAyB,QAAQ,0BAA0B,IAAI;AACjE,WAAO,OAAO,qBAAqB;EACrC;AAEA,QAAM,SAAS,MAAM,MAAM,YAAY;AACvC,SAAO,OAAO;AAChB;AVIA,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,IAAMC,WAAU,cAAc;AAC9B,IAAM,gBAAgB,OAAO,eAAe;AAMrC,IAAM,2BAAN,MAA+B;EAgCpC,YAAqB,gBAAuC,QAAgB;AAAvD,SAAA,iBAAA;AAAuC,SAAA,SAAA;AAV5D,SAAQ,SAAiB;AACzB,SAAQ,MAAW;AAUjB,SAAK,iBAAiB,IAAI;AAE1B,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,YAAYT,iBAAgB;AACjC,SAAK,iBAAiB,IAAI,QAAQ;AAClC,SAAK,iBAAiB,IAAI,WAAW;AAErC,SAAK,UAAU,YAAY,gBAAgB;MACzC,aAAa,CAAC,CAAC,cAAc,SAAS,GAAG,WAAW;AAClD,gBAAQ,cAAc;UACpB,KAAK,aAAa;AAChB,kBAAM,YAAY,aAAa;cAC7B;YACF;AAOA,iBAAK,QAAQ,iBAAiB,WAAW,SAAgB;AAEzD,mBAAO,OAAO;UAChB;UAEA,SAAS;AACP,mBAAO,OAAO;UAChB;QACF;MACF;MACA,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AA3FlD,YAAAH;AA4FQ,gBAAQ,YAAY;UAClB,KAAK,QAAQ;AACX,kBAAM,CAAC,QAAQ,GAAG,IAAI;AAEtB,gBAAI,OAAO,QAAQ,aAAa;AAC9B,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,MAAM;YACjC,OAAO;AACL,mBAAK,SAAS;AACd,mBAAK,MAAM,cAAc,GAAG;YAC9B;AAEA,iBAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,MAAA,IAAU,KAAK,IAAI,IAAA,EAAM;AAClE,iBAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI,IAAI;AAEnD,mBAAO,OAAO;UAChB;UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,WAAW,QAAQ,IAAI;AAK9B,iBAAK,cAAc,WAAW,QAAQ;AACtC,iBAAK,OAAO,KAAK,oBAAoB,WAAW,QAAQ;AAExD,mBAAO,OAAO;UAChB;UAEA,KAAK,oBAAoB;AACvB,kBAAM,CAAC,MAAM,KAAK,IAAI;AACtB,iBAAK,eAAe,IAAI,MAAM,KAAK;AAEnC,iBAAK,OAAO,KAAK,oBAAoB,MAAM,KAAK;AAEhD,mBAAO,OAAO;UAChB;UAEA,KAAK,QAAQ;AACX,kBAAM,CAAC,IAAI,IAAI;AAIf,iBAAK,QAAQ,iBAAiB,QAAQ,MAAM;AAC1C,kBAAI,OAAO,KAAK,eAAe,aAAa;AAI1C,sBAAM,gBAAgB;kBACpB,KAAK;;;;;;kBAML,KAAK,QAAQ;gBACf;AAGA,qBAAK,WAAW,KAAK,MAAM;kBACzB,UAAU;kBACV,kBAAkB,KAAK,iBAAiB;kBACxC,SAAS;kBACT,WAAW,KAAK;gBAClB,CAAC;cACH;YACF,CAAC;AAED,kBAAM,cACJ,OAAO,SAAS,WAAW,aAAa,IAAI,IAAI;AAGlD,kBAAM,eAAe,KAAK,kBAAkB,WAAW;AACvD,iBAAK,aAAa,IAAI,aAAa,MAAM;AAEzC,kBAAM,uBACJA,MAAA,KAAK,cAAL,OAAA,SAAAA,IAAgB,KAAK,MAAM;cACzB,SAAS;cACT,WAAW,KAAK;YAClB,CAAA,MAAM,QAAQ,QAAQ;AAExB,+BAAmB,QAAQ,MAAM;AAE/B,kBAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,qBAAK,OAAO;kBACV;kBACA,KAAK,QAAQ;gBACf;AAWA,oBAAIY,UAAS;AACX,uBAAK,QAAQ;oBACX;oBACA,KAAK;kBACP;gBACF;AAEA,uBAAO,OAAO;cAChB;YACF,CAAC;AAED;UACF;UAEA,SAAS;AACP,mBAAO,OAAO;UAChB;QACF;MACF;IACF,CAAC;AAKD;MACE,KAAK;MACL;MACA,YAAY,KAAK,QAAQ,QAAQ;QAC/B,aAAa,CAAC,CAAC,cAAc,SAAS,GAAG,WAAW;AAClD,kBAAQ,cAAc;YACpB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK,aAAa;AAChB,oBAAM,YAAY,aAAa;gBAC7B;cACF;AAEA,mBAAK,oBAAoB,WAAW,SAAqB;YAC3D;UACF;AAEA,iBAAO,OAAO;QAChB;QACA,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAC1C,kBAAQ,YAAY;YAClB,KAAK,oBAAoB;AACvB,oBAAM,CAAC,WAAW,QAAQ,IAAI;AAI9B,mBAAK,oBAAoB,WAAW,QAAQ;AAC5C,mBAAK,OAAO,KAAK,2BAA2B,WAAW,QAAQ;AAE/D,qBAAO,OAAO;YAChB;UACF;QACF;MACF,CAAC;IACH;EACF;EAEQ,cACN,WACA,UACM;AACN,UAAM,aAAa,KAAK,OAAO,IAAI,SAAS,KAAK,CAAC;AAClD,UAAM,aAAa,WAAW,OAAO,QAAQ;AAC7C,SAAK,OAAO,IAAI,WAAW,UAAU;AAErC,SAAK,OAAO,KAAK,yBAAyB,WAAW,QAAQ;EAC/D;EAEQ,oBACN,WACA,UACM;AACN,UAAM,aAAa,KAAK,aAAa,IAAI,SAAS,KAAK,CAAC;AACxD,UAAM,aAAa,WAAW,OAAO,QAAQ;AAC7C,SAAK,aAAa,IAAI,WAAW,UAAU;AAE3C,SAAK,OAAO,KAAK,gCAAgC,WAAW,QAAQ;EACtE;;;;;EAMA,MAAa,YAAY,UAAmC;AAS1D,SAAK,iBAAiB,IAAI;AAM1B,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,yBAAyB,MAAM;QACnC,KAAK,aAAa;MACpB;AAEA,WAAK,QAAQ,aAAa,KAAK,QAAQ,QAAQ;QAC7C,QAAQ;QACR,OAAO;MACT,CAAC;AACD,WAAK,QAAQ,YAAY,KAAK,QAAQ,QAAQ;QAC5C,QAAQ;QACR,OAAO;MACT,CAAC;AACD,WAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;QACxC,QAAQ;QACR,OAAO;MACT,CAAC;AACD,WAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ;QAC3C,QAAQ;QACR,OAAO;MACT,CAAC;IACH;AAEA,SAAK,OAAO;MACV;MACA,SAAS;MACT,SAAS;IACX;AAEA,WAAO,KAAK,SAAS,UAAU,SAAS,MAAM;AAC9C,WAAO,KAAK,SAAS,cAAc,SAAS,UAAU;AACtD,WAAO,KAAK,SAAS,eAAe,KAAK,IAAI,IAAI;AAEjD,SAAK,QAAQ,oBAAoB,IAAI,MAAM,KAAK,QAAQ,mBAAmB;MACzE,OAAO,CAAC,GAAG,IAAI,SAAyB;AACtC,aAAK,OAAO,KAAK,qBAAqB,KAAK,CAAC,CAAC;AAE7C,YAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,eAAK,OAAO,KAAK,0CAA0C;AAG3D,iBAAO;QACT;AAEA,cAAM,cAAc,SAAS,QAAQ,IAAI,KAAK,CAAC,CAAC;AAChD,aAAK,OAAO;UACV;UACA,KAAK,CAAC;UACN;QACF;AAEA,eAAO;MACT;IACF,CAAC;AAED,SAAK,QAAQ,wBAAwB,IAAI;MACvC,KAAK,QAAQ;MACb;QACE,OAAO,MAAM;AACX,eAAK,OAAO,KAAK,uBAAuB;AAExC,cAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB;AAC3D,iBAAK,OAAO,KAAK,kDAAkD;AAGnE,mBAAO;UACT;AAEA,gBAAM,cAAc,MAAM,KAAK,SAAS,QAAQ,QAAQ,CAAC;AACzD,gBAAM,aAAa,YAChB,IAAI,CAAC,CAAC,YAAY,WAAW,MAAM;AAClC,mBAAO,GAAG,UAAA,KAAe,WAAA;UAC3B,CAAC,EACA,KAAK,MAAM;AAEd,eAAK,OAAO,KAAK,oCAAoC,UAAU;AAE/D,iBAAO;QACT;MACF;IACF;AAGA,WAAO,iBAAiB,KAAK,SAAS;MACpC,UAAU;QACR,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;MACA,cAAc;QACZ,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;MACA,aAAa;QACX,YAAY;QACZ,cAAc;QACd,KAAK,MAAM,KAAK;MAClB;IACF,CAAC;AAED,UAAM,0BAA0B,MAAM,kBAAkB,SAAS,MAAM,CAAC;AAExE,SAAK,OAAO,KAAK,mCAAmC,uBAAuB;AAE3E,SAAK,QAAQ,aAAa,KAAK,SAAS;MACtC,QAAQ;MACR,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,QAAQ,gBAAgB;AAChD,SAAK,cAAc,KAAK,QAAQ,OAAO;AAEvC,UAAM,mBAAmB,MAAM;AAC7B,WAAK,OAAO,KAAK,mCAAmC;AAEpD,WAAK,cAAc,KAAK,QAAQ,IAAI;AAEpC,WAAK,QAAQ,QAAQ,KAAK,SAAS;QACjC,QAAQ,KAAK,eAAe;QAC5B,OAAO;MACT,CAAC;AAED,WAAK,QAAQ,WAAW,KAAK,SAAS;QACpC,QAAQ,KAAK,eAAe;QAC5B,OAAO;MACT,CAAC;IACH;AAEA,QAAI,SAAS,MAAM;AACjB,WAAK,OAAO,KAAK,wCAAwC;AAEzD,YAAM,SAAS,SAAS,KAAK,UAAU;AAEvC,YAAM,4BAA4B,YAAY;AAC5C,cAAM,EAAE,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK;AAE1C,YAAI,MAAM;AACR,eAAK,OAAO,KAAK,4BAA4B;AAC7C,2BAAiB;AACjB;QACF;AAEA,YAAI,OAAO;AACT,eAAK,OAAO,KAAK,6BAA6B,KAAK;AACnD,eAAK,iBAAiB,kBAAkB,KAAK,gBAAgB,KAAK;AAElE,eAAK,QAAQ,YAAY,KAAK,SAAS;YACrC,QAAQ,KAAK,eAAe;YAC5B,OAAO;UACT,CAAC;QACH;AAEA,kCAA0B;MAC5B;AAEA,gCAA0B;IAC5B,OAAO;AACL,uBAAiB;IACnB;EACF;EAEQ,uBAA+B;AACrC,WAAO,aAAa,KAAK,cAAc;EACzC;EAEA,IAAI,WAAoB;AACtB,SAAK,OAAO;MACV;MACA,KAAK,QAAQ;IACf;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;IACT;AAEA,YAAQ,KAAK,QAAQ,cAAc;MACjC,KAAK,QAAQ;AACX,cAAM,eAAe,UAAU,KAAK,qBAAqB,CAAC;AAC1D,aAAK,OAAO,KAAK,0BAA0B,YAAY;AAEvD,eAAO;MACT;MAEA,KAAK,eAAe;AAClB,cAAM,cAAc,cAAc,KAAK,cAAc;AACrD,aAAK,OAAO,KAAK,iCAAiC,WAAW;AAE7D,eAAO;MACT;MAEA,KAAK,QAAQ;AACX,cAAM,WACJ,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AACpD,cAAM,eAAe,IAAI,KAAK,CAAC,KAAK,qBAAqB,CAAC,GAAG;UAC3D,MAAM;QACR,CAAC;AAED,aAAK,OAAO;UACV;UACA;UACA;QACF;AAEA,eAAO;MACT;MAEA,SAAS;AACP,cAAM,eAAe,KAAK,qBAAqB;AAC/C,aAAK,OAAO;UACV;UACA,KAAK,QAAQ;UACb;QACF;AAEA,eAAO;MACT;IACF;EACF;EAEA,IAAI,eAAuB;AAMzB,IAAAf;MACE,KAAK,QAAQ,iBAAiB,MAAM,KAAK,QAAQ,iBAAiB;MAClE;IACF;AAEA,QACE,KAAK,QAAQ,eAAe,KAAK,QAAQ,WACzC,KAAK,QAAQ,eAAe,KAAK,QAAQ,MACzC;AACA,aAAO;IACT;AAEA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,SAAK,OAAO,KAAK,yBAAyB,YAAY;AAEtD,WAAO;EACT;EAEA,IAAI,cAA+B;AACjC,IAAAA;MACE,KAAK,QAAQ,iBAAiB,MAC5B,KAAK,QAAQ,iBAAiB;MAChC;IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM;AACjD,aAAO;IACT;AAEA,UAAM,cAAc,KAAK,QAAQ,kBAAkB,cAAc,KAAK;AAEtE,QAAI,OAAO,cAAc,aAAa;AACpC,cAAQ;QACN;MACF;AACA,aAAO;IACT;AAEA,QAAI,yBAAyB,WAAW,GAAG;AACzC,aAAO,IAAI,UAAU,EAAE;QACrB,KAAK,qBAAqB;QAC1B;MACF;IACF;AAEA,WAAO;EACT;EAEO,UAAUD,QAAqB;AAKpC,SAAK,iBAAiB,IAAI;AAC1B,SAAK,OAAO,KAAK,0BAA0B;AAE3C,SAAK,cAAc,KAAK,QAAQ,IAAI;AACpC,SAAK,QAAQ,SAAS,KAAK,OAAO;AAClC,SAAK,QAAQ,WAAW,KAAK,OAAO;EACtC;;;;EAKQ,cAAc,gBAA8B;AAClD,SAAK,OAAO;MACV;MACA,KAAK,QAAQ;MACb;IACF;AAEA,QAAI,KAAK,QAAQ,eAAe,gBAAgB;AAC9C,WAAK,OAAO,KAAK,+CAA+C;AAChE;IACF;AAEA,WAAO,KAAK,SAAS,cAAc,cAAc;AAEjD,SAAK,OAAO,KAAK,yBAAyB,cAAc;AAExD,QAAI,mBAAmB,KAAK,QAAQ,QAAQ;AAC1C,WAAK,OAAO,KAAK,yCAAyC;AAE1D,WAAK,QAAQ,oBAAoB,KAAK,OAAO;IAC/C;EACF;;;;EAKQ,QAKN,WACA,QACA,SACM;AACN,UAAM,WAAY,OAA0B,KAAK,SAAA,EAAW;AAC5D,UAAM,QAAQ,YAAY,QAAQ,WAAW,OAAO;AAEpD,SAAK,OAAO,KAAK,gBAAgB,WAAW,WAAW,EAAE;AAGzD,QAAI,OAAO,aAAa,YAAY;AAClC,WAAK,OAAO,KAAK,4CAA4C,SAAS;AACtE,eAAS,KAAK,QAA0B,KAAK;IAC/C;AAGA,UAAM,SACJ,kBAAkB,uBAAuB,KAAK,eAAe,KAAK;AAEpE,eAAW,CAAC,qBAAqB,SAAS,KAAK,QAAQ;AACrD,UAAI,wBAAwB,WAAW;AACrC,aAAK,OAAO;UACV;UACA,UAAU;UACV;QACF;AAEA,kBAAU,QAAQ,CAAC,aAAa,SAAS,KAAK,QAAQ,KAAK,CAAC;MAC9D;IACF;EACF;;;;EAKQ,kBACN,MACS;AACT,SAAK,OAAO,KAAK,8CAA8C;AAI/D,UAAM,eACJ,gBAAgB,WAAW,KAAK,gBAAgB,YAAY;AAE9D,UAAM,eAAe,IAAI,QAAQ,KAAK,IAAI,MAAM;MAC9C,QAAQ,KAAK;MACb,SAAS,KAAK;;;;MAId,aAAa,KAAK,QAAQ,kBAAkB,YAAY;MACxD,MAAM,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,OAAO,YAAY,CAAC,IACpD,OACA;IACN,CAAC;AAED,UAAM,eAAe,YAAY,aAAa,SAAS;MACrD,YAAY,CAAC,CAAC,YAAY,IAAI,GAAG,WAAW;AAI1C,gBAAQ,YAAY;UAClB,KAAK;UACL,KAAK,OAAO;AACV,kBAAM,CAAC,YAAY,WAAW,IAAI;AAClC,iBAAK,QAAQ,iBAAiB,YAAY,WAAW;AACrD;UACF;UAEA,KAAK,UAAU;AACb,kBAAM,CAAC,UAAU,IAAI;AACrB,oBAAQ;cACN,oCAAoC,UAAA,sDAAgE,aAAa,MAAA,IAAU,aAAa,GAAA;YAC1I;AACA;UACF;QACF;AAEA,eAAO,OAAO;MAChB;IACF,CAAC;AACD,WAAO,cAAc,WAAW,YAAY;AAC5C,kBAAc,cAAc,KAAK,OAAO;AAExC,SAAK,OAAO,KAAK,6CAA6C,YAAY;AAE1E,WAAO;EACT;AACF;AAEA,SAAS,cAAc,KAAwB;AAQ7C,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO,IAAI,IAAI,GAAG;EACpB;AAEA,SAAO,IAAI,IAAI,IAAI,SAAS,GAAG,SAAS,IAAI;AAC9C;AAEA,SAAS,OACP,QACA,UACA,OACM;AACN,UAAQ,eAAe,QAAQ,UAAU;;IAEvC,UAAU;IACV,YAAY;IACZ;EACF,CAAC;AACH;AW/sBO,SAAS,0BAA0B;EACxC;EACA;AACF,GAA+B;AAC7B,QAAM,sBAAsB,IAAI,MAAM,WAAW,gBAAgB;IAC/D,UAAU,QAAQ,MAAM,WAAW;AACjC,aAAO,KAAK,gCAAgC;AAE5C,YAAM,kBAAkB,QAAQ;QAC9B;QACA;QACA;MACF;AASA,YAAM,uBAAuB,OAAO;QAClC,OAAO;MACT;AACA,iBAAW,gBAAgB,sBAAsB;AAC/C,gBAAQ;UACN;UACA;UACA,qBAAqB,YAAY;QACnC;MACF;AAEA,YAAM,uBAAuB,IAAI;QAC/B;QACA;MACF;AAEA,2BAAqB,YAAY,eAAgB,EAAE,SAAS,UAAU,GAAG;AACvE,cAAM,aAAa,IAAI,kBAAkB,OAAO;AAEhD,aAAK,OAAO,KAAK,6BAA6B;AAE9C,aAAK,OAAO;UACV;UACA,QAAQ,cAAc,SAAS;QACjC;AAEA,cAAM,mBAAmB,MAAMU,eAAc;UAC3C;UACA;UACA;UACA;UACA,YAAY,OAAO,aAAa;AAC9B,kBAAM,KAAK,YAAY,QAAQ;UACjC;UACA,gBAAgB,MAAM;AACpB,iBAAK,UAAU,IAAI,UAAU,eAAe,CAAC;UAC/C;UACA,SAAS,CAACV,WAAU;AAClB,iBAAK,OAAO,KAAK,oBAAoB,EAAE,OAAAA,OAAM,CAAC;AAE9C,gBAAIA,kBAAiB,OAAO;AAC1B,mBAAK,UAAUA,MAAK;YACtB;UACF;QACF,CAAC;AAED,YAAI,CAAC,kBAAkB;AACrB,eAAK,OAAO;YACV;UACF;QACF;MACF;AAEA,2BAAqB,aAAa,eAAgB;QAChD;QACA;QACA;QACA;MACF,GAAG;AACD,aAAK,OAAO;UACV;UACA,QAAQ,cAAc,UAAU;QAClC;AAEA,gBAAQ,KAAK,YAAY;UACvB;UACA;UACA;UACA;QACF,CAAC;MACH;AAKA,aAAO,qBAAqB;IAC9B;EACF,CAAC;AAED,SAAO;AACT;AZ5GO,IAAM,6BAAN,cAAwCM,aAAiC;EAG9E,cAAc;AACZ,UAAM,2BAA0B,iBAAiB;EACnD;EAEU,mBAAmB;AAC3B,WAAOK,uBAAsB,gBAAgB;EAC/C;EAEU,QAAQ;AAChB,UAAM,SAAS,KAAK,OAAO,OAAO,OAAO;AAEzC,WAAO,KAAK,qCAAqC;AAEjD,UAAM,qBAAqB,WAAW;AAEtCV,IAAAA;MACE,CAAE,mBAA2B,iBAAiB;MAC9C;IACF;AAEA,eAAW,iBAAiB,0BAA0B;MACpD,SAAS,KAAK;MACd,QAAQ,KAAK;IACf,CAAC;AAED,WAAO;MACL;MACA,WAAW,eAAe;IAC5B;AAEA,WAAO,eAAe,WAAW,gBAAgB,mBAAmB;MAClE,YAAY;MACZ,cAAc;MACd,OAAO;IACT,CAAC;AAED,SAAK,cAAc,KAAK,MAAM;AAC5B,aAAO,eAAe,WAAW,gBAAgB,mBAAmB;QAClE,OAAO;MACT,CAAC;AAED,iBAAW,iBAAiB;AAC5B,aAAO;QACL;QACA,WAAW,eAAe;MAC5B;IACF,CAAC;EACH;AACF;AAnDO,IAAM,4BAAN;AAAM,0BACJ,oBAAoB,OAAO,KAAK;AaElC,SAAS,8BACd,SACA,SACkC;AAClC,QAAM,cAAc,IAAI,iBAAiB;IACvC,MAAM;IACN,cAAc,CAAC,IAAI,iBAAiB,GAAG,IAAI,0BAA0B,CAAC;EACxE,CAAC;AAED,cAAY,GAAG,WAAW,OAAO,EAAE,SAAS,WAAW,WAAW,MAAM;AACtE,UAAM,sBAAsB,QAAQ,MAAM;AAE1C,UAAM,WAAW,MAAMS;MACrB;MACA;MACA,QAAQ,mBAAmB,EAAE,OAAOO,cAAc,gBAAgB,CAAC;MACnE;MACA,QAAQ;MACR;QACE,iBAAiB,GAAG,EAAE,SAAS,aAAa,GAAG;AAC7C,cAAI,CAAC,QAAQ,OAAO;AAClB,oBAAQ,QAAQ,KAAK,mBAAmB,CAAC,EAAE,UAAAH,UAAS,MAAM;AACxD,sBAAQ,IAAI;gBACV,SAAS;gBACT,UAAAA;gBACA;cACF,CAAC;YACH,CAAC;UACH;QACF;MACF;IACF;AAEA,QAAI,UAAU;AACZ,iBAAW,YAAY,QAAQ;IACjC;EACF,CAAC;AAED,cAAY;IACV;IACA,CAAC,EAAE,UAAU,kBAAkB,SAAS,UAAU,MAAM;AACtD,cAAQ,QAAQ;QACd,mBAAmB,oBAAoB;QACvC;UACE;UACA;UACA;QACF;MACF;IACF;EACF;AAEA,cAAY,MAAM;AAElB,SAAO;AACT;AC/DO,SAAS,oBACd,SACc;AACd,SAAO,eAAe,MAAM,SAAS;AACnC,YAAQ,sBAAsB;MAC5B;MACA;IACF;AAEA,sBAAkB;MAChB,SAAS;MACT,OAAO,QAAQ;IACjB,CAAC;AAED,WAAO;EACT;AACF;ACjBO,SAAS,mBACd,SACa;AACb,SAAO,SAAS,OAAO;;AACrB,KAAAV,MAAA,QAAQ,wBAAR,gBAAAA,IAA6B;AAC7B,qBAAiB,EAAE,QAAO,aAAQ,iBAAR,mBAAsB,MAAM,CAAC;EACzD;AACF;AELO,SAAS,iCAAiC;AAC/C,MAAI;AACF,UAAM,SAAS,IAAI,eAAe;MAChC,OAAO,CAAC,eAAe,WAAW,MAAM;IAC1C,CAAC;AACD,UAAM,UAAU,IAAI,eAAe;AACnC,YAAQ,MAAM,YAAY,QAAQ,CAAC,MAAM,CAAC;AAC1C,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;ADkBO,IAAM,iBAAN,cACG,SAEV;EAME,eAAe,UAAoD;AACjE,UAAM,GAAG,QAAQ;AANX;AACA,wCAA6B;AAC7B,uCAA2B;AAC3B;AAKN,IAAAH;MACE,CAAC,cAAc;MACfE,SAAS;QACP;MACF;IACF;AAEA,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,KAAK,oBAAoB;EAC1C;EAEQ,sBAAkD;AACxD,UAAM,UAAsC;;;MAG1C,kBAAkB;MAClB,cAAc;MACd,QAAQ;MACR,oBAAoB,MAAM;AACxB,eAAO,KAAK,mBAAmB,gBAAgB;MACjD;MACA,cAAc;MACd,SAAS,KAAK;MACd,eAAe;QACb,IAAI,CAAC,WAAW,aAAa;AAC3B,eAAK,QAAQ,OAAO,YAElB,UAAU,eAAe,WAAW,CAAC,UAAU;AAE/C,gBAAI,MAAM,WAAW,KAAK,QAAQ,QAAQ;AACxC;YACF;AAEA,kBAAM,UAAU,MAAM;AAEtB,gBAAI,CAAC,SAAS;AACZ;YACF;AAEA,gBAAI,QAAQ,SAAS,WAAW;AAC9B,uBAAS,OAAO,OAAO;YACzB;UACF,CAAC;QACH;QACA,MAAM,CAAC,SAAS;;AACd,WAAAC,MAAA,KAAK,QAAQ,WAAb,gBAAAA,IAAqB,YAAY;QACnC;MACF;MACA,QAAQ;QACN,aAAa,CAAC,QAAQ,WAAW,aAAa;AAC5C,iBAAO,iBAAiB,WAAW,QAAyB;AAC5D,eAAK,UAAU,KAAK;YAClB;YACA;YACA;UACF,CAAC;AAED,iBAAO,MAAM;AACX,mBAAO,oBAAoB,WAAW,QAAyB;UACjE;QACF;QACA,oBAAoB,MAAM;AACxB,qBAAW,EAAE,QAAQ,WAAW,SAAS,KAAK,KAAK,WAAW;AAC5D,mBAAO,oBAAoB,WAAW,QAAQ;UAChD;AACA,eAAK,YAAY,CAAC;QACpB;QACA,MAAM,CAAC,cAAc;AACnB,gBAAM,WAA8B,CAAC;AAErC,iBAAO,IAAI,QAKT,CAAC,SAAS,WAAW;AACrB,kBAAM,wBAAwB,CAAC,UAAwB;AACrD,kBAAI;AACF,sBAAM,UAAU,MAAM;AAEtB,oBAAI,QAAQ,SAAS,WAAW;AAC9B,0BAAQ,OAAO;gBACjB;cACF,SAASJ,QAAO;AACd,uBAAOA,MAAK;cACd;YACF;AAEA,qBAAS;cACP,KAAK,QAAQ,OAAO;gBAClB,UAAU;gBACV;gBACA;cACF;cACA,KAAK,QAAQ,OAAO;gBAClB,UAAU;gBACV;gBACA;cACF;YACF;UACF,CAAC,EAAE,QAAQ,MAAM;AACf,qBAAS,QAAQ,CAAC,WAAW,OAAO,CAAC;UACvC,CAAC;QACH;MACF;MACA,UAAU;QACR,kBACE,EAAE,mBAAmB,cAAc,SAAS,aAAa;QAC3D,wBAAwB,+BAA+B;MACzD;IACF;AAEA,SAAK,eAAe,QAAQ,SAAS,mBACjC,oBAAoB,OAAO,IAC3B,mBAAmB,OAAO;AAE9B,SAAK,cAAc,QAAQ,SAAS,mBAChC,mBAAmB,OAAO,IAC1B,WAAW,OAAO;AAEtB,WAAO;EACT;EAEA,MAAa,MAAM,UAAwB,CAAC,GAAoB;AAC9D,QAAI,QAAQ,mBAAmB,MAAM;AACnCG,eAAS;QACP;MACF;IACF;AAEA,SAAK,QAAQ,eAAee;MAC1B;MACA;IACF;AAGA,yBAAqB;MACnB,6BAA6B,MAAM;AACjC,eAAO,KAAK,QAAQ,aAAa;MACnC;MACA,aAAa,MAAM;AACjB,eAAO,KAAK,mBAAmB,gBAAgB;MACjD;MACA,oBAAoB,CAAC,eAAe;AAClC,YAAI,CAAC,KAAK,QAAQ,aAAa,OAAO;AAGpC,gCAAsB,UAAU;QAClC;MACF;MACA,0BAA0B;MAAC;IAC7B,CAAC;AACD,yBAAqB,MAAM;AAE3B,SAAK,cAAc,KAAK,MAAM;AAC5B,2BAAqB,QAAQ;IAC/B,CAAC;AAED,WAAO,MAAM,KAAK,aAAa,KAAK,QAAQ,cAAc,OAAO;EACnE;EAEO,OAAa;AAClB,UAAM,QAAQ;AACd,SAAK,QAAQ,OAAO,mBAAmB;AACvC,SAAK,QAAQ,QAAQ,mBAAmB;AACxC,SAAK,YAAY;EACnB;AACF;AAQO,SAAS,eACX,UACU;AACb,SAAO,IAAI,eAAe,GAAG,QAAQ;AACvC;", "names": ["_a", "kEmitter", "kBoundListener", "kEmitter", "invariant", "kBoundListener", "error", "error", "error", "invariant", "worker", "devU<PERSON>s", "_a", "symbol", "Interceptor", "createRequestId", "DeferredPromise", "isObject", "handleRequest", "hasConfigurableGlobal", "readable", "transformers", "response", "next", "IS_NODE", "isHandlerKind", "mergeRight"]}