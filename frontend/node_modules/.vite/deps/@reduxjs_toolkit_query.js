import {
  NamedSchemaError,
  QueryStatus,
  _NEVER,
  buildCreate<PERSON>pi,
  copyWithStructuralSharing,
  coreModule,
  coreModuleName,
  createApi,
  defaultSerializeQueryArgs,
  fakeBaseQuery,
  fetchBaseQuery,
  retry,
  setupListeners,
  skipToken
} from "./chunk-Q2XD5C35.js";
import "./chunk-3KIIGLBH.js";
import "./chunk-EQCVQC35.js";
export {
  NamedSchemaError,
  QueryStatus,
  _NEVER,
  buildCreateApi,
  copyWithStructuralSharing,
  coreModule,
  coreModuleName,
  createApi,
  defaultSerializeQueryArgs,
  fakeBaseQuery,
  fetchBaseQuery,
  retry,
  setupListeners,
  skipToken
};
