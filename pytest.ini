[tool:pytest]
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --strict-markers
    --strict-config
    --disable-warnings
    -v
    --tb=short
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=50
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    auth: marks tests as authentication tests
    database: marks tests as database tests
    service: marks tests as service layer tests
    cli: marks tests as CLI tests
    bdd: marks tests as BDD tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::RuntimeWarning
